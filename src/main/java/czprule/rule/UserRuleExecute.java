/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 设备操作自定义规则执行器
 * 作    者 : 张余平
 * 开发日期 : 2012-4-11
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.dao.CustomCodexDao;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.operationmodel.BasisCompOperate;
import czprule.rule.operationmodel.BasisDeviceOperate;
import czprule.system.CBSystemConstants;
import czprule.system.DeviceSVGPanelUtil;
import czprule.system.ShowMessage;

public class UserRuleExecute implements RulebaseInf {

	public boolean execute(RuleBaseMode Srcrbm) {
		// TODO Auto-generated method stub
		if(Srcrbm==null)
			return false;
		PowerDevice pd=Srcrbm.getPd();
		if(pd==null)
			return false;
		if(pd.getDeviceStatus().equals(Srcrbm.getEndState())){
			return true;
		}
		
		boolean isExcSuss=true; //判断是否执行自定义规则，返回值
		CustomCodexDao ccdd = new CustomCodexDao();
		UserRulebaseInf rb=null; 
		List<String> laterClass=new ArrayList<String>();
		
		//前置设备判断
		List<String[]> preCondition=ccdd.getUserPreCondition(pd.getPowerDeviceID(), pd.getDeviceStatus(), Srcrbm.getStateCode());
		String[] tempStr=new String[4];
		for (int i = 0; i < preCondition.size(); i++) {
			tempStr= preCondition.get(i);
			PowerDevice pdN = CBSystemConstants.getPowerDevice(tempStr[0],
					tempStr[1]);
			if(pdN==null){ //变电站没有打开
				DeviceSVGPanelUtil.openSVGPanel(tempStr[0]);
				
				pdN = CBSystemConstants.getPowerDevice(tempStr[0],
						tempStr[1]);
			}
			String tempstatus = tempStr[2];
			String isnegated =tempStr[3];
			String prestatus=pdN.getDeviceStatus();
			
			
			/*
			RuleBaseMode rbm=new RuleBaseMode();
			rbm.setPd(pdN);
			rbm.setBeginStatus(prestatus);
			rbm.setEndState(tempstatus);
			rbm.setMessageList(Srcrbm.getMessageList());
			rbm.setCodeList(Srcrbm.getCodeList());
			rbm.setSrcSide(Srcrbm.getSrcSide());
			rbm.setDeviceConnectStations(Srcrbm.getDeviceConnectStations());
			RulebaseInf rbi=null;
			if(pd.getDeviceType().equals(SystemConstants.SwitchSeparate)
				||pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
				rbi=new BasisCompOperate();
			}else{
				rbi=new BasisDeviceOperate();
			}
			if(!rbi.execute(rbm)){
				return false;
			}
			isExcSuss=true;
			Srcrbm.setNeedChoose(rbm.isNeedChoose());
			*/
			
			if(isnegated.equals("0")){
			    if(!prestatus.equals(tempstatus)){
			    	return false;
			    }
			}else{
				if(prestatus.equals(tempstatus)){
					if(CBSystemConstants.cardbuildtype.equals("0")){
						/*
						RuleBaseMode rbm=new RuleBaseMode();
						rbm.setPd(pdN);
						rbm.setBeginStatus(prestatus);
						rbm.setEndState("1");
						RulebaseInf rbi=null;
						if(pd.getDeviceType().equals(SystemConstants.SwitchSeparate)
							||pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
							rbi=new BasisCompOperate();
						}else{
							rbi=new BasisDeviceOperate();
						}
						if(!rbi.execute(rbm)){
							return false;
						}*/
						RuleUtil.deviceStatusSet(pdN, prestatus, "1");
						return true;
					}else{
						ShowMessage.view("必须先拉开["+pdN.getPowerDeviceName()+"]");
						return false;
					}
					
				}
			}
			
			isExcSuss=true;
			
		}
		
		
		//一、执行前接口
		List<String[]> ExecuteInfClass=ccdd.getUserRuleJudgeClass(pd.getPowerDeviceID(), pd.getDeviceStatus(), Srcrbm.getEndState());
		String[] infClasss=new String[2];
		for (int j = 0; j < ExecuteInfClass.size(); j++) {
			infClasss=ExecuteInfClass.get(j);
			if("1".equals(infClasss[0].trim())){
				laterClass.add(infClasss[1].trim()); //执行设备后执行的接口集合
				continue;
			}
			try {
			    rb=  (UserRulebaseInf)Class.forName(infClasss[1].trim()).newInstance();
				if(!rb.execute(Srcrbm))
					return false;
			} catch (ClassNotFoundException e) {
				ShowMessage.view("不存在自定义规则接口："+infClasss[1].trim());
				return false;
			} catch (Exception e) {
				e.printStackTrace();
				ShowMessage.view("自定义规则接口报错："+infClasss[1].trim());
				return false;
			}
			isExcSuss=true;
		}
		
		
		//二、执行设备
		List<String[]> ExecuteDevs=ccdd.getUserRuleExecuteEquip(pd.getPowerDeviceID(), pd.getDeviceStatus(), Srcrbm.getEndState());
		tempStr=new String[4];
		for (int i = 0; i < ExecuteDevs.size(); i++) {
			tempStr= ExecuteDevs.get(i);
			PowerDevice pdN = CBSystemConstants.getPowerDevice(tempStr[1],
					tempStr[0]);
			if(pdN==null){ //变电站没有打开
				DeviceSVGPanelUtil.openSVGPanel(tempStr[1]);
				
				pdN = CBSystemConstants.getPowerDevice(tempStr[1],
						tempStr[0]);
			}
			String tempstatus = tempStr[2];
			String tempState =tempStr[3];
			if(!tempstatus.equals(pdN.getDeviceStatus())){
				continue;
			}
			RuleBaseMode rbm=new RuleBaseMode();
			rbm.setPd(pdN);
			rbm.setBeginStatus(tempstatus);
			rbm.setEndState(tempState);
			rbm.setMessageList(Srcrbm.getMessageList());
			rbm.setCodeList(Srcrbm.getCodeList());
			rbm.setSrcSide(Srcrbm.getSrcSide());
			rbm.setDeviceConnectStations(Srcrbm.getDeviceConnectStations());
			RulebaseInf rbi=null;
			if(pd.getDeviceType().equals(SystemConstants.SwitchSeparate)
				||pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
				rbi=new BasisCompOperate();
			}else{
				rbi=new BasisDeviceOperate();
			}
			if(!rbi.execute(rbm)){
				return false;
			}
			isExcSuss=true;
			Srcrbm.setNeedChoose(rbm.isNeedChoose());
		}
		
		//执行后接口
		String classStr="";
		for (int j = 0; j < laterClass.size(); j++) {
			classStr=laterClass.get(j);
			try {
			    rb=  (UserRulebaseInf)Class.forName(classStr).newInstance();
				if(!rb.execute(Srcrbm))
					return false;
			} catch (ClassNotFoundException e) {
				ShowMessage.view("不存在自定义规则接口："+classStr.trim());
				return false;
			} catch (Exception e) {
				e.printStackTrace();
				ShowMessage.view("自定义规则接口报错："+classStr.trim());
				return false;
			}
			isExcSuss=true;
		}
		
		return isExcSuss;
	}


}
