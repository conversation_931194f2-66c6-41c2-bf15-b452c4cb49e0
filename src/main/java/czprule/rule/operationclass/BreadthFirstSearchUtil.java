package czprule.rule.operationclass;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.graphic.BreadthFirstPaths;
import czprule.graphic.InitLine;
import czprule.graphic.SymbolGraph;
import czprule.model.DeviceNode;
import czprule.model.PowerDevice;
import czprule.model.PowerDeviceAndPath;
import czprule.model.PowerDeviceListAndMapPaths;

/**
 *
 * <AUTHOR>
 * 该类的目标是： RuleUtil.java
 * 该类调用 BreadthFirstPaths实现具体功能，非特殊说明，保证不返回null
 */
public class BreadthFirstSearchUtil {

	/**
	 *
	 * @param lineId ,如果是线路id的话，返回该线路通过出线开关，能访问的所有线路。如果是奇奇怪怪的id至少会返回本身。（传入station_id，就会返回本身）
	 * @return HashMap<String ,HashSet<String>> key 有两个一个line,一个station
	 */
	public static  HashMap<String ,HashSet<String>>  getLineAndStationIds(String lineId){
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		return breadthFirstPaths.getLineAndStationIds(lineId);
	}

	/**
	 *
	 * @param zxSwitch_id,任何一个有拓扑关系的设备id,返回连接最近的配网设备,所在线路id
	 * @return 注意 没找到是返回Null,而不是空字符串。
	 */
	public static String getLineId(String zxSwitch_id){
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		return breadthFirstPaths.getLineId(zxSwitch_id);
	}

	/**
	 *
	 * @param zxSwitch_id,任何一个有拓扑关系的设备id,返回连接最近的配网设备,所在线路id
	 * @return 注意 没找到是返回Null,而不是空字符串。
	 */
	public static String getLineName(String zxSwitch_id){
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		String line_id = breadthFirstPaths.getLineId(zxSwitch_id);
		return InitLine.contains( line_id)? InitLine.line_info.get(InitLine.index(line_id)).getLineName() : "";
	}
	/**
	 * 搜索潜在的导供开关，既允许1或0个开关断开的情况下，能搜到的出线开关。
	 *
	 */
	public static ArrayList<PowerDevice> getDGSwitchs(String id, String BreakDeviceId){
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		return breadthFirstPaths.getDGSwitchs(id,BreakDeviceId);
	}
	/**
	 * 搜索潜在的导供开关，既允许1或0个开关断开的情况下，能搜到的出线开关。
	 *
	 */
	public static ArrayList<PowerDeviceAndPath> getDGSwitchAndPaths(String id, String BreakDeviceId){
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		return breadthFirstPaths.getDGSwitchAndPaths(id,BreakDeviceId);
	}
	/**
	 * 搜索配电变压器
	 */
	public static ArrayList<PowerDevice> getPDBYQs(String id){
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		return breadthFirstPaths.getPDBYQs(id);
	}
	/**
	 * 查找设备，只查找直接相连的设备
	 *
	 * @param pd
	 *            搜索起始设备
	 * @param tagDevtype
	 *            目标设备类型
	 * @return
	 */
	public static List<PowerDevice> getDeviceDirectList(PowerDevice pd,
			String tagDevtype) {
		List<PowerDevice> returnList = new ArrayList<PowerDevice>();
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		returnList = breadthFirstPaths.getDeviceDirectList(pd, tagDevtype);
		return returnList;
	}

	/**
	 * 查找设备，传入所有参数
	 *
	 * @param pd
	 *            搜索起始设备
	 * @param excDev
	 *            排除设备对象  null,则无
	 * @param tagTypes
	 *            目标设备类型多类型用,分割
	 * @param excTypes
	 *            排除设备类型多类型用,分割
	 * @param tagRunTypes
	 *            目标安装类型多类型用,分割
	 * @param excRunTypes
	 *            排除安装类型多类型用,分割
	 * @param isSearchDirectOnly
	 *            是否只搜索直接连接设备
	 * @param isSearchOff
	 *            是否搜索断开路径
	 * @param isStopOnBus
	 *            是否遇母线停止搜索 母线指主网母线
	 * @param isStopOnTagType
	 *            是否遇目标设备类型停止往下搜索，note:不代表只会返回一共设备。
	 * @return 目标设备集合
	 */
	public static List<PowerDevice> getDeviceList(PowerDevice pd,
			PowerDevice excDev, String tagTypes, String excTypes,
			String tagRunTypes, String excRunTypes, boolean isSearchDirectOnly,
			boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType) {
		List<PowerDevice> returnList = new ArrayList<PowerDevice>();

		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		returnList = breadthFirstPaths.getDeviceList(pd, excDev, tagTypes, excTypes, tagRunTypes, excRunTypes, isSearchDirectOnly, isSearchOff, isStopOnBus, isStopOnTagType);
		return returnList;
	}
	public static List<PowerDevice> getDeviceList(String id,
												  PowerDevice excDev, String tagTypes, String excTypes,
												  String tagRunTypes, String excRunTypes, boolean isSearchDirectOnly,
												  boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType) {
		List<PowerDevice> returnList = new ArrayList<PowerDevice>();

		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		returnList = breadthFirstPaths.getDeviceList(id, excDev, tagTypes, excTypes, tagRunTypes, excRunTypes, isSearchDirectOnly, isSearchOff, isStopOnBus, isStopOnTagType);
		return returnList;
	}
	public static List<PowerDevice> getDeviceList(String pdId,
												  String excDevIds, String tagTypes, String excTypes,
												  String tagRunTypes, String excRunTypes, boolean isSearchDirectOnly,
												  boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType, boolean isStopOnDiffVolt, int validPort, int resultNum) {
		List<PowerDevice> returnList = new ArrayList<PowerDevice>();

		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		returnList = breadthFirstPaths.getDeviceList(pdId, excDevIds, tagTypes, excTypes, tagRunTypes, excRunTypes, isSearchDirectOnly, isSearchOff, isStopOnBus, isStopOnTagType,isStopOnDiffVolt,validPort,resultNum);
		return returnList;
	}
	/**
	 * 查找设备，传入所有参数
	 *
	 * @param pd
	 *            搜索起始设备
	 * @param excDev
	 *            排除设备对象  null,则无
	 * @param tagTypes
	 *            目标设备类型多类型用,分割
	 * @param excTypes
	 *            排除设备类型多类型用,分割
	 * @param tagRunTypes
	 *            目标安装类型多类型用,分割
	 * @param excRunTypes
	 *            排除安装类型多类型用,分割
	 * @param isSearchDirectOnly
	 *            是否只搜索直接连接设备
	 * @param isSearchOff
	 *            是否搜索断开路径
	 * @param isStopOnBus
	 *            是否遇母线停止搜索 母线指主网母线
	 * @param isStopOnTagType
	 *            是否遇目标设备类型停止往下搜索，note:不代表只会返回一共设备。
	 * @param printPaths 控制台打印路径
	 * @return 目标设备集合
	 */
	public static List<PowerDevice> getDeviceList(PowerDevice pd,
			PowerDevice excDev, String tagTypes, String excTypes,
			String tagRunTypes, String excRunTypes, boolean isSearchDirectOnly,
			boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType,boolean printPaths) {
		List<PowerDevice> returnList = new ArrayList<PowerDevice>();
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		returnList = breadthFirstPaths.getDeviceList(pd, excDev, tagTypes, excTypes, tagRunTypes, excRunTypes, isSearchDirectOnly, isSearchOff, isStopOnBus, isStopOnTagType,printPaths);
		return returnList;
	}

	public static List<PowerDevice> getPathByDevice(PowerDevice pd,
			PowerDevice tag, String excTypes, String excRunTypes,
			boolean isSearchOff, boolean isStopOnBus) {
		List<PowerDevice> returnList = new ArrayList<PowerDevice>();
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		returnList = breadthFirstPaths.getPathByDevice(pd, tag, excTypes, excRunTypes, isSearchOff, isStopOnBus);
		return returnList;
	}

	public static List<PowerDevice> getPathByDevice(String pdId,
			String tagId, String excTypes, String excRunTypes,
			boolean isSearchOff, boolean isStopOnBus) {
		List<PowerDevice> returnList = new ArrayList<PowerDevice>();
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		returnList = breadthFirstPaths.getPathByDevice(pdId, tagId, excTypes, excRunTypes, isSearchOff, isStopOnBus);
		return returnList;
	}

	/**
	 * 查找设备，只查找直接相连的设备
	 *
	 * @param pd
	 *            搜索起始设备 
	 * @param tagDevtype
	 *            目标设备类型 开关，地刀，或刀闸
	 * @return
	 */
	public static List<PowerDevice> getDevicePwDirectList(PowerDevice pd,
			String tagDevtype) {
		List<PowerDevice> result = new ArrayList<PowerDevice>();
		List<PowerDevice> temp = new ArrayList<PowerDevice>();
		List<PowerDevice> temp1 = new ArrayList<PowerDevice>();
		List<PowerDevice> temp2 = new ArrayList<PowerDevice>();
		//开关与地刀直接互找，中间要经过夹着的刀闸
		if((pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)&&tagDevtype.equals(SystemConstants.Switch))
				||(pd.getDeviceType().equals(SystemConstants.Switch)&&tagDevtype.equals(SystemConstants.SwitchFlowGroundLine))
				){
			//要是找到了就返回
			result.addAll(getDeviceDirectList(pd,tagDevtype));
			if(result.size()>0){
				return result;
			}else{
				//通过附件的电气连接线找目标设备
				temp = getDeviceDirectList(pd,SystemConstants.ElectricConnection);
				for(PowerDevice pdd : temp){
					result.addAll(getDeviceDirectList(pdd,tagDevtype));
				}
				if(result.size()>0){
					return result;
				}
				//没找到就先找附件的刀闸。temp 存放附件的刀闸。
				temp = getDeviceDirectList(pd,SystemConstants.SwitchSeparate);
				if(temp == null || temp.size()==0){
					temp1 =  getDeviceDirectList(pd,SystemConstants.ElectricConnection);
					if(temp1 == null || temp1.size()==0){
						return result; //旁边又没刀闸，又没电气连接线 说明没找到。
					}else{
						//没找到就通过附件的刀闸找相邻的地刀
						for (PowerDevice pdd :temp1){
							temp.addAll(getDeviceDirectList(pdd,SystemConstants.SwitchSeparate));
						}
						if(temp == null || temp.size()==0){
							return result; //旁边通过电气连接线也没找到刀闸，说明没找到。
						}
						//通过刀闸找直接相连的目标设备
						for(PowerDevice pdd : temp){
							result.addAll(getDeviceDirectList(pdd,tagDevtype));
						}
						if(result.size()>0){
							return result;
						}
						//通过刀闸找附件的电气连接线
						for(PowerDevice pdd : temp){
							temp2.addAll(getDeviceDirectList(pdd,SystemConstants.ElectricConnection));
						}
						if(temp2==null || temp2.size()==0){
							return result ;//没找到
						}
						for(PowerDevice pdd : temp2){
							result.addAll(getDeviceDirectList(pdd,tagDevtype));
						}
						return result;
					}
				}else{
					//通过刀闸找直接相连的目标设备
					for(PowerDevice pdd : temp){
						result.addAll(getDeviceDirectList(pdd,tagDevtype));
					}
					if(result.size()>0){
						return result;
					}
					//通过刀闸找附件的电气连接线
					for(PowerDevice pdd : temp){
						temp2.addAll(getDeviceDirectList(pdd,SystemConstants.ElectricConnection));
					}
					if(temp2==null || temp2.size()==0){
						return result ;//没找到
					}
					for(PowerDevice pdd : temp2){
						result.addAll(getDeviceDirectList(pdd,tagDevtype));
					}
					return result;
				}

			}

		}else{
			result.addAll(getDeviceDirectList(pd,tagDevtype));
			if(result.size()>0){
				return result;
			}else{
				temp = getDeviceDirectList(pd,SystemConstants.ElectricConnection);
				for(PowerDevice pdd : temp){
					result.addAll(getDeviceDirectList(pdd,tagDevtype));// 这里会将一个设备同时插入两次~但好像不影响功能。
				}
				if(result.size()>0){
					return result;
				}
			}
		}
		return result;
	}

	public static PowerDeviceListAndMapPaths getPowerDeviceListAndMapPaths(PowerDevice pd,
																		   ArrayList<PowerDevice> excDevs, String tagTypes, String excTypes,
																		   String tagRunTypes, String excRunTypes, boolean isSearchDirectOnly,
																		   boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType , boolean isStopOnDiffVolt, int validPort, boolean isPrintPath) {
		PowerDeviceListAndMapPaths ret = new PowerDeviceListAndMapPaths();
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		ret = breadthFirstPaths.getPowerDeviceListAndMapPaths(pd, excDevs, tagTypes, excTypes, tagRunTypes, excRunTypes, isSearchDirectOnly, isSearchOff, isStopOnBus, isStopOnTagType,isStopOnDiffVolt, validPort,isPrintPath);
		return ret;
	}

	public static PowerDeviceListAndMapPaths getPowerDeviceListAndMapPaths(String pdId,
																		   String excDevIds, String tagTypes, String excTypes,
																		   String tagRunTypes, String excRunTypes, boolean isSearchDirectOnly,
																		   boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType , boolean isStopOnDiffVolt, int validPort, boolean isPrintPath,int resultNum) {
		PowerDeviceListAndMapPaths ret = new PowerDeviceListAndMapPaths();
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		ret = breadthFirstPaths.getPowerDeviceListAndMapPaths(pdId, excDevIds, tagTypes, excTypes, tagRunTypes, excRunTypes, isSearchDirectOnly, isSearchOff, isStopOnBus, isStopOnTagType,isStopOnDiffVolt, validPort,isPrintPath,resultNum);
		return ret;
	}




}
