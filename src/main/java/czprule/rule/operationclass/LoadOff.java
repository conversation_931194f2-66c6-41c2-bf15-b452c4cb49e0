package czprule.rule.operationclass;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.view.EquipListView;
import czprule.system.CBSystemConstants;

/**
 * 负荷停电算法
 * */
public class LoadOff {
    public static int click=0;
	public boolean execute(List<PowerDevice> loadMap) {
		if(loadMap.size()==0||CBSystemConstants.cardbuildtype.equals("2")){
			return true;
		}
		List<PowerDevice> lineLoadList = new ArrayList<PowerDevice>();
		for (PowerDevice dev : loadMap) {
			if(dev.getDeviceType().equals(SystemConstants.InOutLine))
				lineLoadList.add(dev);
		}
		//ConfirmLoadDialog dialog=new ConfirmLoadDialog(loadMap);
		EquipListView dialog = new EquipListView(SystemConstants.getMainFrame(), true, lineLoadList, "以下负荷将会失电，是否继续操作？");
		if(dialog.isOK()){
			//负荷转冷备用
			for (PowerDevice dev : loadMap) {
				//dev.setDeviceStatus("1"); //避免重复调用算法
				if(!RuleUtil.deviceStatusChange(dev,dev.getDeviceStatus(), "2")){
					return false;
				}
			}
			return true;
		}
		return false;
	}

}
