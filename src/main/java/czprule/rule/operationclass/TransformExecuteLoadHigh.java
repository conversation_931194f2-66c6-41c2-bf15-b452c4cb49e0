package czprule.rule.operationclass;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import tbp.common.config.constants.SysConstants;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 主变电源侧状态转换执行类
 * 作    者: 郑柯
 * 开发日期: 2013年10月26日  上午10:09:35 
 */
public class TransformExecuteLoadHigh implements RulebaseInf {

	private String sideType = "high";
	
	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		boolean result = true;
		PowerDevice pd = rbm.getPd();
		
		if(rbm.getBeginStatus().equals("0")) { //运行转热备用
			setZXDOn(pd);
			ConvertSwitchOnToHot(pd);
//			int ok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), "主变停电后，是否拉开中性点地刀？",
//					"提示", JOptionPane.YES_NO_OPTION);
//			if (ok == JOptionPane.YES_OPTION) {
//				setZXDOff(pd); //201508, cq
//			}
			setZXDOff(pd); //201508, cq
		}
		else if(rbm.getBeginStatus().equals("1") && rbm.getEndState().equals("2")) { //热备用转冷备用
			ConvertSwitchHotToCold(pd);
		}
		else if(rbm.getBeginStatus().equals("2") && rbm.getEndState().equals("1")) { //冷备用转热备用
			result = ConvertSwitchColdToHot(pd);
			if(!result)
				return result;
		}
		else if(rbm.getBeginStatus().equals("3") && rbm.getEndState().equals("2")) { //检修转冷备用
			List<PowerDevice> swList = RuleExeUtil.getTransformerSwitchSource(pd);
			for(PowerDevice sw:swList){
				if(sw.getDeviceStatus().equals("3")){	
					RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "2");
				}
			}
			
		}
		else if(rbm.getEndState().equals("0")) { //热备用转运行
			setZXDOn(pd);
			result = ConvertSwitchHotToOn(pd);
			if(!result)
				return result;
//			int ok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), "主变停电后，是否拉开中性点地刀？",
//					"提示", JOptionPane.YES_NO_OPTION);
//			if (ok == JOptionPane.YES_OPTION) {
//				setZXDOff(pd);
//			}
			setZXDOff(pd);
		}
		
		return true;
	}
	
	public boolean ConvertSwitchOnToHot(PowerDevice pd) {
		List<PowerDevice> swList = new ArrayList();
		// 判断参数类型 ，主变取电源侧开关；电源侧开关添加进类型列；
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)) {
			swList.add(pd);
		} else {
			swList = RuleExeUtil.getTransformerSwitchSource(pd);
		}
		

		boolean isSwitchTagOn = false;
		
		
		boolean isChangeSourchSwithOn = false;//是否合了开关改变电源
		
		for (PowerDevice sw : swList) {
			if (sw.getDeviceStatus().equals("0")) {
				
				if(RuleUtil.isTransformerNQ(pd)){//内桥接线判断开关所带主变负荷
					List<PowerDevice> zbList = RuleExeUtil.getDeviceList(sw, SystemConstants.PowerTransformer, "", false, false, true);
					zbList.remove(pd);
					if(zbList.size()>0){
						List<PowerDevice>  sourceSw = RuleExeUtil.getTransformerSwitchHigh(zbList.get(0));
						for(PowerDevice dev:sourceSw){
							if(!dev.getDeviceStatus().equals("0")){
								RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
								break;
							}
						}
					}
					if(CBSystemConstants.LineTagStatus.get(sw)==null){//如果没传开关状态默认还原
						CBSystemConstants.LineTagStatus.put(sw, sw.getDeviceStatus());
					}
				}
			
				
				
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelFourCornerLine)&&CBSystemConstants.isCurrentSys){
					List<PowerDevice> lineList = RuleExeUtil.getDeviceList(sw, SystemConstants.InOutLine, SystemConstants.Switch, true, true, true);
					
					for(Iterator<PowerDevice> itor = lineList.iterator();itor.hasNext(); ){
						PowerDevice dev = itor.next();
						
						if(dev.getPowerDeviceName().contains("等值负荷")){
							itor.remove();
						}
					}
					
					for(PowerDevice line : lineList){
						List<PowerDevice> kgList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch, SystemConstants.Switch, true, true, true);

						for(PowerDevice kg : kgList){
							if(!kg.getPowerDeviceID().equals(sw.getPowerDeviceID())&&!kg.getDeviceStatus().equals("0")){
								ShowMessage.view("断开"+CZPService.getService().getDevName(sw)+"会导致"+CZPService.getService().getDevName(line)+"断电");
							}
						}
					}
				}
				// 拉开主变电源侧开关
				RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "1");

			
				
//				String tagStatus = CBSystemConstants.LineTagStatus.get(sw);
//				if (tagStatus != null && tagStatus.equals("0"))
//					isSwitchTagOn = true;
//
//				if (!isSwitchTagOn) {
//					PowerDevice circuit = null;
//					List<PowerDevice> circuits = RuleExeUtil.getDeviceList(sw, SystemConstants.InOutLine, null, null, CBSystemConstants.RunTypeSideMother, false, true, true, true, "0");
//					if (circuits.size() > 0) {
//						circuit = circuits.get(0);
//						PowerDevice otheresidecircuit = null;
//						List<PowerDevice> othersidecircuits = RuleExeUtil.getLineOtherSideList(circuit);
//						if (othersidecircuits.size() > 0) {
//							otheresidecircuit = othersidecircuits.get(0);
//							// 对侧厂站开关
//							PowerDevice othersideswith = null;
//							List<PowerDevice> othersideswiths = RuleExeUtil.getDeviceList(otheresidecircuit, SystemConstants.Switch, null, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true, "0");
//							if (othersideswiths.size() > 0) {
//								othersideswith = othersideswiths.get(0);
//								RuleExeUtil.deviceStatusChange(othersideswith, othersideswith.getDeviceStatus(), "1");
//							}
//						}
//					}
//				}
			}
		}
		
		return true;
	}
	
	public boolean ConvertSwitchHotToOn(PowerDevice pd) {
		List<PowerDevice> swList = new ArrayList();
		// 判断参数类型 ，主变取电源侧开关；电源侧开关添加进类型列；
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)) {
			swList.add(pd);
		} else {
			swList = RuleExeUtil.getTransformerSwitchSource(pd);
		}
		
		for (PowerDevice sw : swList) {
			if (sw.getDeviceStatus().equals("1")) {
				// 合上主变电源侧开关
				String tagStatus = CBSystemConstants.LineTagStatus.get(sw);
				if (tagStatus == null || tagStatus.equals("0")) {
					boolean result = RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "0");
					if (!result)
						return false;
				}
			}
		}
		
		//内桥接线断回线路开关
		if(RuleUtil.isTransformerNQ(pd)){
			Map<Integer, DispatchTransDevice> dtds= CBSystemConstants.getDtdMap();
			for (DispatchTransDevice dtd : dtds.values()) {
				if(dtd.getTransDevice().getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
						&&!RuleExeUtil.getDeviceBeginStatus(dtd.getTransDevice()).equals("0")){
					RuleExeUtil.deviceStatusExecute(dtd.getTransDevice(), dtd.getTransDevice().getDeviceStatus(), "1");
					break;
				}
			}
		}
		return true;
	}
	
	public boolean ConvertSwitchHotToCold(PowerDevice pd) {
		List<PowerDevice> swList = new ArrayList();
		
		// 拉开主变刀闸
		List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
		for (PowerDevice kf : kfList) {
			RuleExeUtil.deviceStatusExecute(kf, kf.getDeviceStatus(), "1");
		}
		
		// 判断参数类型 ，主变取电源侧开关；电源侧开关添加进类型列；
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)) {
			swList.add(pd);
		} else {
			swList = RuleExeUtil.getTransformerSwitchSource(pd);
		}
		
		for (PowerDevice sw : swList) {
			if (sw.getDeviceStatus().equals("1")) {
				
				String tagStatus = CBSystemConstants.LineTagStatus.get(sw);
				if (tagStatus != null 
//						&& tagStatus.equals("0")
						)
					RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), tagStatus); // 合上主变电源侧开关
				else
					RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "2");
			}
		}
		
		//内桥接线断回线路开关
		if(RuleUtil.isTransformerNQ(pd)){
			Map<Integer, DispatchTransDevice> dtds= CBSystemConstants.getDtdMap();
			for (DispatchTransDevice dtd : dtds.values()) {
				if(dtd.getTransDevice().getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
						&&!RuleExeUtil.getDeviceBeginStatus(dtd.getTransDevice()).equals("0")){
					RuleExeUtil.deviceStatusExecute(dtd.getTransDevice(), dtd.getTransDevice().getDeviceStatus(), "1");
					break;
				}
			}
		}
		
		return true;
	}
	
	public boolean ConvertSwitchColdToHot(PowerDevice pd) {
		boolean result = true;
		List<PowerDevice> swList = new ArrayList();
		// 判断参数类型 ，主变取电源侧开关；电源侧开关添加进类型列；
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)) {
			swList.add(pd);
		} else {
			swList = RuleExeUtil.getTransformerSwitchSource(pd);
		}
		// 如果有线路开关，放后面
		for (PowerDevice sw : swList) {
			if (sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
				swList.remove(sw);
				swList.add(sw);
				break;
			}
		}
		
		for (PowerDevice sw : swList) {
			if(RuleUtil.isTransformerNQ(pd)){//内桥接线判断开关所带主变负荷
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(sw, SystemConstants.PowerTransformer, "", false, false, true);
				zbList.remove(pd);
				if(zbList.size()>0){
					List<PowerDevice>  sourceSw = RuleExeUtil.getTransformerSwitchHigh(zbList.get(0));
					for(PowerDevice dev:sourceSw){
						if(!dev.getDeviceStatus().equals("0")){
							RuleExeUtil.deviceStatusExecute(dev, dev.getDeviceStatus(), "0");
							break;
						}
					}
				}
				if(CBSystemConstants.LineTagStatus.get(sw)==null){//如果没传开关状态默认还原
					CBSystemConstants.LineTagStatus.put(sw, sw.getDeviceStatus());
				}
			}
			if (sw.getDeviceStatus().equals("0")) {
				// 拉开主变电源侧开关
				RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "1");
			}
		}
		// 合上主变刀闸
		List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
		for (PowerDevice kf : kfList) {
			if (kf.getDeviceStatus().equals("1"))
				RuleExeUtil.deviceStatusChange(kf, kf.getDeviceStatus(), "0");
		}
		
		for (PowerDevice sw : swList) {
			if (sw.getDeviceStatus().equals("2"))
				result = RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "1");
			if (!result)
				return result;
		}
		return true;
	}
	
	public boolean setZXDOn(PowerDevice pd) {
		List<PowerDevice> gdList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
		RuleExeUtil.swapLowDeviceList(gdList);
		for(PowerDevice gd : gdList) {
			RuleExeUtil.deviceStatusExecute(gd, gd.getDeviceStatus(), "0");
		}
		return true;
	}
	
	/**
	 * 分列运行主变高压侧中性点地刀拉开
	 * @param pd
	 * @return
	 */
	public boolean setZXDOff(PowerDevice pd) {
		if(!RuleExeUtil.isTransformerBL(pd)) {
			List<PowerDevice> gdList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine, "", CBSystemConstants.RunTypeGroundZXDDD, "", true, true, true, true);
			for(PowerDevice gd : gdList) {
				if(gd.getPowerVoltGrade() == pd.getPowerVoltGrade()) {
					RuleExeUtil.deviceStatusChange(gd, "0", "1");
				}
			}
		}
		return true;
	}

}
