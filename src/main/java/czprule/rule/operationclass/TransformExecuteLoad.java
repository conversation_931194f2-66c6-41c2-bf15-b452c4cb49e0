package czprule.rule.operationclass;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map.Entry;

import javax.swing.JOptionPane;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 主变状态转换工具类
 * 作    者: 郑柯
 * 开发日期: 2013年10月26日  上午10:17:52 
 */
public class TransformExecuteLoad {
	
	private static HashMap<String, PowerDevice> mlLoadMap = new HashMap<String, PowerDevice>();
	private static HashMap<String, String> mlLoadFlagMap = new HashMap<String, String>();
	private static boolean isTwoML = false; //是否需要分片倒负荷，否则直接按电压等级倒
	private static boolean isOneTF = false; //是否只倒到一台主变
	private static HashMap<PowerDevice, List<PowerDevice>> mlSwitchMap = new HashMap<PowerDevice, List<PowerDevice>>();
	private static List<PowerDevice> mlList = new ArrayList<PowerDevice>();;
	
	/**
	 * 由用户选择主变的负荷侧母线倒负荷的目标主变
	 * @param pd
	 * @return
	 */
	public boolean judgeTransformLoad(PowerDevice pd) {
		
		//获得主变负荷侧母线
		mlList = RuleExeUtil.getMotherLineLoad(pd);
		RuleExeUtil.swapDeviceList(mlList);
		for(PowerDevice ml : mlList) {
			PowerDevice sw = null;
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, false, true, true);
			if(swList.size() > 0)
				sw = swList.get(0);
			
			//排除已经连接到其他主变的母线
			List<PowerDevice> linkTfList = RuleExeUtil.getDeviceList(ml, sw, SystemConstants.PowerTransformer, "", "", "", false, false, false, true);
			linkTfList.remove(pd);
			if(linkTfList.size() > 0) {
				mlLoadFlagMap.put(ml.getPowerDeviceID(), "1");
				continue;
			}
			
			List<PowerDevice> tfList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> mlSwitchList = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, true, true);
			for(PowerDevice mlSwitch : mlSwitchList) {
				PowerDevice tf = null;
				HashMap<PowerDevice, ArrayList<PowerDevice>> pathMap = RuleExeUtil.getPathByDevice(mlSwitch, ml, SystemConstants.PowerTransformer, "", "", "", false, true, false, true);
				
				//比较最短路径
				int minDistance = 0;
				for(Iterator it = pathMap.entrySet().iterator();it.hasNext();) {  
		            Entry e = (Entry)it.next();  
		            PowerDevice p = (PowerDevice)e.getKey();
		            if(!p.getDeviceStatus().equals("0"))
		            	continue;
		            ArrayList<PowerDevice> path = (ArrayList<PowerDevice>)e.getValue();
		            if(path.size() < minDistance) {
		            	tf = p;
		            	minDistance = path.size();
		            }
		            else if(minDistance == 0) {
		            	tf = p;
	            		minDistance = path.size();
		            }
				}
				if(tf != null&& !tfList.contains(tf))
					tfList.add(tf);
			}

//			for(Iterator it = tfList.iterator();it.hasNext();) {   
//				PowerDevice tf = (PowerDevice)it.next();
//				if(tf.equals(pd))
//					it.remove();
//				else if(!tf.getDeviceStatus().equals("0"))
//					it.remove();
//			}
			if(tfList.size() == 0)
				continue;
			PowerDevice tf = null;
			if(tfList.size() > 1) {
				//RuleExeUtil.swapDeviceList(tfList);
				int r = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择["+ml.getPowerStationName()+"]["+ml.getPowerDeviceName()+"]上负荷倒至哪台主变", "选择代供主变", JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE, null, tfList.toArray(), tfList.toArray()[0]);
				if (r == -1)
					return false;
				tf = tfList.get(r);
			}
			else if(tfList.size() == 1)
				tf = tfList.get(0);
			else {
				List<PowerDevice> load = RuleExeUtil.getDeviceList(ml, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, true, true);
				new LoadOff().execute(load);
			}
			mlLoadMap.put(ml.getPowerDeviceID(), tf);
			mlLoadFlagMap.put(ml.getPowerDeviceID(), "0");
		}
		judgeIsOneTF(pd);
		judgeIsDoubleMotherLine(pd);	
		return true;
	}
	
	/**
	 * 判断主变的负荷侧母线当前的代供主变
	 * @param pd
	 * @return
	 */
	public boolean judgeTransformLoadBack(PowerDevice pd) {
		
		//获得主变负荷侧母线
		mlList = RuleExeUtil.getMotherLineLoadAll(pd);
		for(PowerDevice ml : mlList) {
			List<PowerDevice> tfList = RuleExeUtil.getDeviceList(ml, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, false, true);
			if(tfList.size() == 0)
				continue;
			PowerDevice tf = tfList.get(0);
			mlLoadMap.put(ml.getPowerDeviceID(), tf);
			mlLoadFlagMap.put(ml.getPowerDeviceID(), "0");
		}
		judgeIsOneTF(pd);
		judgeIsDoubleMotherLine(pd);
				
		return true;
	}
	
	public boolean judgeIsDoubleMotherLine(PowerDevice pd) {
		//识别是否存在不同电压等级母线连接同一主变
		HashMap<PowerDevice, ArrayList<Double>> tfMap = new HashMap<PowerDevice, ArrayList<Double>>();
		 for(Iterator it = mlLoadMap.entrySet().iterator();it.hasNext();) {  
	            Entry e = (Entry)it.next();  
	            String mlID = (String)e.getKey(); 
	            PowerDevice dev = (PowerDevice)e.getValue();
	            PowerDevice ml = CBSystemConstants.getPowerDevice(dev.getPowerStationID(), mlID);
	            if(!tfMap.containsKey(dev))
	            	tfMap.put(dev, new ArrayList());
	            if(!tfMap.get(dev).contains(ml.getPowerVoltGrade()))
	            	tfMap.get(dev).add(ml.getPowerVoltGrade());
		 }
		 int count = 0;
		 for(Iterator it = tfMap.values().iterator();it.hasNext();) {   
			 ArrayList<Double> volList = (ArrayList<Double>)it.next();
			 if(volList.size() >= 2)
				 count++;
		 }
		 isTwoML = (count>=2);
		 return isTwoML;
	}
	
	public boolean judgeIsOneTF(PowerDevice pd) {
		//识别是否倒到同一主变
		ArrayList<PowerDevice> tfList = new ArrayList<PowerDevice>();
		 for(Iterator it = mlLoadMap.entrySet().iterator();it.hasNext();) {  
	            Entry e = (Entry)it.next();  
	            PowerDevice dev = (PowerDevice)e.getValue();
	            if(!tfList.contains(dev))
	            	tfList.add(dev);
		 }
		
		 isOneTF = (tfList.size()==1);
		 return isOneTF;
	}
	
	
	//合上高压侧母联开关
	public boolean setMLSwitchSourceOn(PowerDevice pd, PowerDevice tf) {
		List<PowerDevice> devList = new ArrayList<PowerDevice>();
		devList = RuleExeUtil.getTransformersLinkDevice(pd, tf);
		if(devList != null) {
			for (PowerDevice dev : devList) {
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) && !dev.getDeviceStatus().equals("0")) {
					RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "0");	
				}
			}
		}
		return true;
	}
	
	
	//拉开高压侧母联开关
	public boolean setMLSwitchSourceOff(PowerDevice pd, PowerDevice tf) {
		List<PowerDevice> devList = new ArrayList<PowerDevice>();
		devList = RuleExeUtil.getTransformersLinkDevice(pd, tf);
		if(devList != null) {
			for (PowerDevice dev : devList) {
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) && dev.getDeviceStatus().equals("0")) {
					RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "1");	
				}
			}
		}
		return true;
	}
	
	public boolean setMLSwitchLoadOn(PowerDevice pd, PowerDevice tf, PowerDevice ml) {
		//合上母线与目标主变间的母联开关
		boolean result = true;
		if(ml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {
			RuleExeUtil.doubleMotherLineMLSwitchToOn(ml,tf);
		}
		else {
			PowerDevice sw = RuleExeUtil.getTransformerSwitch(pd, ml.getPowerVoltGrade());
			List<PowerDevice> path = RuleExeUtil.getPathByDevice(ml, sw, tf, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL, true, false);
			if(path==null)
				return result;
			for (PowerDevice dev : path) {
				if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate) && !RuleExeUtil.isKnifeLinkSwitch(dev) && !dev.getDeviceStatus().equals("0")) {
					result = RuleExeUtil.deviceStatusChange(dev, "1", "0");
					if(!result)
						return false;
				}
			}
			for (PowerDevice dev : path) {
				if(dev.getDeviceType().equals(SystemConstants.Switch) && !dev.getDeviceStatus().equals("0")) {
					result = RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "0");
					if(!result)
						return false;
				}
			}
		}
		return result;
	}
	
	
	public boolean setMLSwitchLoadOff(PowerDevice pd, PowerDevice tf, PowerDevice ml) {
		//断开母线与目标主变间的母联开关
		boolean result = true;
		if(!ml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {
			PowerDevice sw = RuleExeUtil.getTransformerSwitch(pd, ml.getPowerVoltGrade());
			List<PowerDevice> path = RuleExeUtil.getPathByDevice(ml, sw, tf, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL, true, false);
			for (PowerDevice dev : path) {
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) && dev.getDeviceStatus().equals("0")) {
					result = RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "1");
					if(!result)
						return false;
				}
			}
		}
		return true;
	}


	/**
	 * 母线倒到其他主变
	 * @param pd 主变
	 * @param ml 母线
	 * @return
	 */
	public boolean loadMotherLine(PowerDevice pd, PowerDevice ml) {
		boolean result = true;
		//获得母线将要倒负荷的目标主变
		PowerDevice tf = mlLoadMap.get(ml.getPowerDeviceID());
		if(tf == null) {
			if(!mlLoadFlagMap.containsKey(ml.getPowerDeviceID()) || !mlLoadFlagMap.get(ml.getPowerDeviceID()).equals("1")) {
				List<PowerDevice> swList = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, false, true, true);
				for(PowerDevice dev : swList) {
					if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC))
						RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "2");
				}
				swList = RuleExeUtil.getDeviceDirectList(ml, SystemConstants.SwitchSeparate);
				for(PowerDevice dev : swList) {
					if(!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX))
						RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "1");
				}
			}
			//获得母线与源主变间的主变开关
			List<PowerDevice> tfSwitchList = new ArrayList<PowerDevice>();
			List<PowerDevice> path2 = RuleExeUtil.getPathByDevice(ml, pd, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, true, false);
			if(path2 != null) {
				for (PowerDevice dev : path2) {
					if(dev.getDeviceType().equals(SystemConstants.Switch) && dev.getDeviceStatus().equals("0"))
						tfSwitchList.add(dev);
				}
			}
			
			//断开母线与源主变间的主变开关
			for (PowerDevice dev : tfSwitchList) {
				if(dev.getDeviceType().equals(SystemConstants.Switch) && dev.getDeviceStatus().equals("0"))
					RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "1");
			}
			
			return true;
		}
		
		//合上高压侧母联开关
		setMLSwitchSourceOn(pd, tf);
		
		
		
		//获得母线与源主变间的主变开关
		List<PowerDevice> tfSwitchList = new ArrayList<PowerDevice>();
		List<PowerDevice> path2 = RuleExeUtil.getPathByDevice(ml, pd, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, true, false);
		if(path2==null)
			return result;
		for (PowerDevice dev : path2) {
			if(dev.getDeviceType().equals(SystemConstants.Switch) && dev.getDeviceStatus().equals("0"))
				tfSwitchList.add(dev);
		}
		
		//合上负荷侧母联开关
		setMLSwitchLoadOn(pd, tf, ml);
		
		
		//判断是否存在相连母线连接多个接地变情况，若存在则断开接地变开关
		result = operateEarthSwitch(ml, "1");
		if(!result)
			return false;
		
		//断开母线与源主变间的主变开关
		for (PowerDevice dev : tfSwitchList) {
			if(dev.getDeviceType().equals(SystemConstants.Switch) && dev.getDeviceStatus().equals("0"))
				RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "1");
		}
		
		
		//标识母线倒负荷完成
		mlLoadFlagMap.put(ml.getPowerDeviceID(), "1");
		
		if(!isTwoML()) {
			boolean isAllLoadComplete = true;
	       for(Iterator it = mlLoadMap.entrySet().iterator();it.hasNext();) {  
	            Entry e = (Entry)it.next();  
	            String mlID = (String)e.getKey(); 
	            PowerDevice dev = (PowerDevice)e.getValue();
	            if(dev.equals(tf) && mlLoadFlagMap.get(mlID).equals("0")) {
	            	isAllLoadComplete = false;
	            	break;
	            }
	       }
	       //高压侧母联开关转热备用
	       List<PowerDevice> mlSwitchList = mlSwitchMap.get(tf);
	       if(isAllLoadComplete && mlSwitchList!= null) {
	    	   for (PowerDevice mlSwitch : mlSwitchList) {
		    	   if(mlSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) && 
		    			   mlSwitch.getDeviceStatus().equals("0") &&
		    			   RuleExeUtil.isMLSwitchCanOff(mlSwitch))
		   				RuleExeUtil.deviceStatusChange(mlSwitch, mlSwitch.getDeviceStatus(), "1");
	    	   }
	       }
		}
		return true;
	}
	
	/**
	 * 母线倒回原主变
	 * @param pd 主变
	 * @param ml 母线
	 * @return
	 */
	public boolean loadBackMotherLine(PowerDevice pd, PowerDevice ml) {
		boolean result = true;
		//获得母线当前连接的主变
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(ml, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, false, true);
		if(tfList.size() == 0) {
			
			//获得母线与源主变间的主变开关
			List<PowerDevice> tfSwitchList = new ArrayList<PowerDevice>();
			List<PowerDevice> path2 = RuleExeUtil.getPathByDevice(ml, pd, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, true, false);
			for (PowerDevice dev : path2) {
				if(dev.getDeviceType().equals(SystemConstants.Switch) && !dev.getDeviceStatus().equals("0"))
					tfSwitchList.add(dev);
			}
			
			//合上母线与源主变间的主变开关
			for (PowerDevice dev : tfSwitchList) {
				if(dev.getDeviceType().equals(SystemConstants.Switch) && !dev.getDeviceStatus().equals("0"))
					RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "0");
			}
			return true;
		}
		PowerDevice tf = tfList.get(0);
		
		//合上高压侧母联开关
		setMLSwitchSourceOn(pd, tf);
		
		//合上接地变开关
		result = operateEarthSwitch(ml, "0");
		if(!result)
			return false;
		
		//获得母线与源主变间的主变开关
		List<PowerDevice> tfSwitchList = new ArrayList<PowerDevice>();
		List<PowerDevice> path2 = RuleExeUtil.getPathByDevice(ml, pd, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, true, false);
		for (PowerDevice dev : path2) {
			if(dev.getDeviceType().equals(SystemConstants.Switch) && !dev.getDeviceStatus().equals("0"))
				tfSwitchList.add(dev);
		}
				
		//合上母线与源主变间的主变开关
		for (PowerDevice dev : tfSwitchList) {
			if(dev.getDeviceType().equals(SystemConstants.Switch) && !dev.getDeviceStatus().equals("0"))
				RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "0");
		}
		
		
		//断开母线与目标主变间的母联开关
		if(!isTwoML() && !isOneTF()) {
			setMLSwitchLoadOff(pd, tf, ml);
		}
		
		
		//标识母线倒负荷完成
		mlLoadFlagMap.put(ml.getPowerDeviceID(), "1");
		
		if(!isTwoML()) {
			boolean isAllLoadComplete = true;
	       for(Iterator it = mlLoadMap.entrySet().iterator();it.hasNext();) {  
	            Entry e = (Entry)it.next();  
	            String mlID = (String)e.getKey(); 
	            PowerDevice dev = (PowerDevice)e.getValue();
	            if(dev.equals(tf) && mlLoadFlagMap.get(mlID).equals("0")) {
	            	isAllLoadComplete = false;
	            	break;
	            }
	       }
	       
	       //高压侧母联开关转热备用
	       List<PowerDevice> mlSwitchList = mlSwitchMap.get(tf);
	       if(!isOneTF()) {
		       if(isAllLoadComplete && !CBSystemConstants.isParallelRun  && mlSwitchList!= null) {
		    	   for (PowerDevice mlSwitch : mlSwitchList) {
			    	   if(mlSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) && 
			    			   mlSwitch.getDeviceStatus().equals("0") &&
			    			   RuleExeUtil.isMLSwitchCanOff(mlSwitch))
			   				RuleExeUtil.deviceStatusChange(mlSwitch, mlSwitch.getDeviceStatus(), "1");
		    	   }
		       }
	       }
		}
		return true;
	}
	
	/**
	 * 操作接地变开关
	 * @param ml
	 * @param status
	 */
	public boolean operateEarthSwitch(PowerDevice ml, String status) {
		boolean reault = true;
		List<PowerDevice> allETList = RuleExeUtil.getDeviceList(ml, SystemConstants.EarthingTransformer, SystemConstants.PowerTransformer, "", CBSystemConstants.RunTypeSideMother, false, true, false, false);
		List<PowerDevice> curETlist = RuleExeUtil.getDeviceList(ml, SystemConstants.EarthingTransformer, SystemConstants.PowerTransformer, "", CBSystemConstants.RunTypeSideMother, false, false, true, false);
		allETList.removeAll(curETlist);
		if(allETList.size() >= 1) {
			for (PowerDevice dev : curETlist) {
				PowerDevice sw = RuleExeUtil.getDeviceSwitch(dev);
				if(sw != null && !sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)) {
					reault = RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), status);
					if(!reault)
						return false;
				}
			}
		}
		return reault;
	}


	public HashMap<String, PowerDevice> getMlLoadMap() {
		return mlLoadMap;
	}


	public HashMap<String, String> getMlLoadFlagMap() {
		return mlLoadFlagMap;
	}
	

	public HashMap<PowerDevice, List<PowerDevice>> getMlSwitchMap() {
		return mlSwitchMap;
	}

	public void setMlSwitchMap(HashMap<PowerDevice, List<PowerDevice>> mlSwitchMap) {
		this.mlSwitchMap = mlSwitchMap;
	}

	/**
	 * 是否需要分片倒负荷，否则直接按电压等级倒
	 * @return
	 */
	public static boolean isTwoML() {
		return isTwoML;
	}

	public static boolean isOneTF() {
		return isOneTF;
	}
	
	public List<PowerDevice> getMLListByVol(double vol) {
		List<PowerDevice> mlVolList = new ArrayList<PowerDevice>();
		for(PowerDevice ml : mlList) {
			if(ml.getPowerVoltGrade() == vol)
				mlVolList.add(ml);
		}
		return mlVolList;
	}
	
}
