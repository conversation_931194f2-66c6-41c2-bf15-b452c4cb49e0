package czprule.rule.operationclass;

import java.util.*;
import java.util.Map.Entry;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.view.EquipCheckChoose;
import czprule.rule.view.EquipRadioChoose;
import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 主变负荷侧状态转换执行类
 * 作    者: 郑柯
 * 开发日期: 2013年11月1日 上午11:29:50 
 */
public class TransformExecuteLoadLowMiddle implements RulebaseInf {
	
	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		PowerDevice pd = rbm.getPd();
		boolean result = true;
		//判断传入参数类型，电源侧开关（全停）进行，将关联变压器赋值
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
			List<PowerDevice> ptwList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, null, true, true, true);
			if(ptwList.size()>0) pd= ptwList.get(0);
		}
		if(rbm.getBeginStatus().equals("0")) {
			if(pd.getPowerVoltGrade() >= 500) {
				List<PowerDevice> devList = RuleExeUtil.getDeviceDirectList(pd, "");
				for(PowerDevice dev : devList) {
					if(dev.getPowerVoltGrade() == 35 || dev.getPowerVoltGrade()==66) {
						List<PowerDevice> mlList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
						for(PowerDevice ml : mlList) {
							RuleExeUtil.deviceStatusChange(ml, "0", "3");
						}
					}
				}
			}
			List<SwitchInfo> siList = initSwitchInfoOn(pd);
			if(siList == null)
				return false;
			// 按电压等级从低到高排序
			Collections.sort(siList, new Comparator<SwitchInfo>() {
			    @Override
			    public int compare(SwitchInfo si1, SwitchInfo si2) {
			        double voltage1 = si1.getLoadSwitch().getPowerVoltGrade();
			        double voltage2 = si2.getLoadSwitch().getPowerVoltGrade();
			        return Double.compare(voltage1, voltage2);
			    }
			});

			result = convertLoadOnToHot(pd, siList);
			if(!result)
				return false;
			
		}
		else if(rbm.getBeginStatus().equals("1") && rbm.getEndState().equals("2")) {
			convertLoadHotToCold(pd);
		}
		else if(rbm.getBeginStatus().equals("2") && rbm.getEndState().equals("1")) {
			convertLoadColdToHot(pd);
		}
		if(rbm.getEndState().equals("0")) {
			List<SwitchInfo> siList = initSwitchInfoOff(pd);
			if(siList == null)
				return false;
			convertLoadHotToOn(siList);
			if(pd.getPowerVoltGrade() >= 500) {
				List<PowerDevice> devList = RuleExeUtil.getDeviceDirectList(pd, "");
				for(PowerDevice dev : devList) {
					if(dev.getPowerVoltGrade() == 35 || dev.getPowerVoltGrade()==66) {
						List<PowerDevice> mlList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
						for(PowerDevice ml : mlList) {
							RuleExeUtil.deviceStatusChange(ml, ml.getDeviceStatus(), "0");
						}
					}
				}
			}
			
			//内桥接线没母联开关直接搜其他开关
			if(siList.size()==0
					&&RuleUtil.isTransformerNQ(pd)){
				List<PowerDevice> nqswList = RuleExeUtil.getDeviceList(pd,null, SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true, true);
				
				for(PowerDevice nqsw:nqswList){//断开线路开关
					if(nqsw.getDeviceStatus().equals("1")&&nqsw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						RuleExeUtil.deviceStatusExecuteJB(nqsw, "1", "0");
					}
				}
				for(PowerDevice nqsw:nqswList){//合上内桥开关
					if(nqsw.getDeviceStatus().equals("0")&&!nqsw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						RuleExeUtil.deviceStatusExecuteJB(nqsw, "0", "1");
					}
				}
				
			}
		}
		return result;
	}
	
	/**
	 * 运行转热备用
	 * @param siList
	 * @return
	 */
	public boolean convertLoadOnToHot(PowerDevice pd, List<SwitchInfo> siList) {
		boolean result = true;
		List<SwitchInfo> siopList = new ArrayList<SwitchInfo>();
		for(Iterator it = siList.iterator(); it.hasNext();) {
			SwitchInfo si = (SwitchInfo)it.next();
			it.remove();
			if(si.getTransformer() == null) {
				if(siopList.size() > 0) {
					operateSIOnToHot(siopList, siList);
					siopList.clear();
				}
				siopList.add(si);
				result = operateSIOnToHot(siopList, siList);
				if(!result)
					return false;
				siopList.clear();
			}
			else if(siopList.size() == 0 || si.getTransformer().equals(siopList.get(siopList.size()-1).getTransformer())) {
				siopList.add(si);
			}
			else {
				operateSIOnToHot(siopList, siList);
				siopList.clear();
				siopList.add(si);
			}
		}
		if(siopList.size() > 0){
			//内桥接线没母联开关直接搜其他开关
			if(siopList.get(0).getSourceMLSwitchList().size()==0
					&&RuleUtil.isTransformerNQ(pd)){
				List<PowerDevice> nqswList = RuleExeUtil.getDeviceList(pd,null, SystemConstants.Switch,
						SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchQT, "", false, true, true, true, true);
				for(PowerDevice nqsw:nqswList){//合上内桥开关
					if(nqsw.getDeviceStatus().equals("1")&&!nqsw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						RuleExeUtil.deviceStatusExecuteJB(nqsw, "1", "0");
					}
				}
				for(PowerDevice nqsw:nqswList){//断开线路开关
					if(nqsw.getDeviceStatus().equals("0")&&nqsw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
						RuleExeUtil.deviceStatusExecuteJB(nqsw, "0", "1");
					}
				}
			}
			
			operateSIOnToHot(siopList, siList);
		}
			
		return true;
	}
	
	public boolean operateSIOnToHot(List<SwitchInfo> siopList, List<SwitchInfo> siList) {
		if(siopList.get(0).getTransformer() == null) {
			if(siopList.get(0).isMLNeedOperate() && siopList.get(0).getLoadMLLine() != null) {
				List<PowerDevice> load = RuleExeUtil.getDeviceList(siopList.get(0).getLoadMLLine(), SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, true, true);
				boolean result = new LoadOff().execute(load);
				if(!result)
					return false;
			}
			//先操作母线相连的开关以外的开关
			if(siopList.get(0).getLoadMLLine()!=null
					&&siopList.get(0).getLoadSwitch().getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				List<PowerDevice> mxswList = RuleExeUtil.getDeviceList(siopList.get(0).getLoadMLLine(), SystemConstants.Switch,
						SystemConstants.PowerTransformer, true, true, true);
				mxswList.remove(siopList.get(0).getLoadSwitch());
				for(PowerDevice mxsw: mxswList){
					if(mxsw.getDeviceStatus().equals("0")){
						if(!RuleExeUtil.deviceStatusExecuteJB(mxsw, "0", "1"))
							return false;
					}
				}
			}
			
			
			
			//点图票规则操作变低开关
			boolean result = RuleExeUtil.deviceStatusExecute(siopList.get(0).getLoadSwitch(), siopList.get(0).getLoadSwitch().getDeviceStatus(), "1");
			
			if(!result){
				return false;
			}
			
			return true;
		}
		
		//合上高压侧母联开关
		for(PowerDevice sw : siopList.get(0).getSourceMLSwitchList()) {
			if(sw.getDeviceStatus().equals("1"))
				RuleExeUtil.deviceStatusExecuteJB(sw, sw.getDeviceStatus(), "0");
		}
		
		
		//电压等级由高到低合上母联开关
		for(int i = siopList.size()-1; i >= 0; i--) {
			RuleExeUtil.deviceStatusExecuteJB(siopList.get(i).getLoadMLSwitch(), siopList.get(i).getLoadMLSwitch().getDeviceStatus(), "0");
		}
		
		
		//电压等级由低到高
		for(int i = 0; i < siopList.size(); i++) {
			//拉开接地变开关
			if(siopList.get(i).getEarthSwitch() != null) {
				List<PowerDevice> tfList = new ArrayList<PowerDevice>();
				tfList.add(siopList.get(i).getEarthSwitch());
				tfList.add(siopList.get(i).getLoadSwitch());
//				int r = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择先拉开哪个开关？", "选择", JOptionPane.DEFAULT_OPTION, JOptionPane.INFORMATION_MESSAGE, null, tfList.toArray(), tfList.toArray()[0]);
//				if (r == -1)
//					return false;
				//int r = JdbSwitchOrder.execute(siopList.get(i).getLoadSwitch());
				int r = 0;
				RuleExeUtil.deviceStatusExecute(tfList.get(r), tfList.get(r).getDeviceStatus(), "1");
				tfList.remove(r);
				RuleExeUtil.deviceStatusExecute(tfList.get(0), tfList.get(0).getDeviceStatus(), "1");
			}
			else
				//拉开主变开关
				RuleExeUtil.deviceStatusExecute(siopList.get(i).getLoadSwitch(), siopList.get(i).getLoadSwitch().getDeviceStatus(), "1");
		}
		
		//如果高压侧母联开关原先在分位则拉开
		for(PowerDevice sw : siopList.get(0).getSourceMLSwitchList()) {
			//如果开关后面需要保持合上则不拉开
			boolean isSwitchNeed = false;
			for(SwitchInfo si : siList) {
				if(si.getSourceMLSwitchList().contains(sw)) {
					isSwitchNeed = true;
					break;
				}
			}
			if(isSwitchNeed)
				continue;
			if(RuleExeUtil.isDeviceChanged(sw))
				RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "1");
		}
		return true;
	}
	
	/**
	 * 热备用转运行
	 * @param siList
	 * @return
	 */
	public boolean convertLoadHotToOn(List<SwitchInfo> siList) {
		
		List<SwitchInfo> siopList = new ArrayList<SwitchInfo>();
		for(Iterator it = siList.iterator(); it.hasNext();) {
			SwitchInfo si = (SwitchInfo)it.next();
			it.remove();
			if(si.getTransformer() == null) {
				if(siopList.size() > 0) {
					operateSIHotToOn(siopList, siList);
					siopList.clear();
				}
				siopList.add(si);
				operateSIHotToOn(siopList, siList);
				siopList.clear();
			}
			else if(siopList.size() == 0 || si.getTransformer().equals(siopList.get(siopList.size()-1).getTransformer())) {
				siopList.add(si);
			}
			else {
				operateSIHotToOn(siopList, siList);
				siopList.clear();
				siopList.add(si);
			}
		}
		if(siopList.size() > 0) {
			operateSIHotToOn(siopList, siList);
		}
		return true;
	}
	
	public boolean operateSIHotToOn(List<SwitchInfo> siopList, List<SwitchInfo> siList) {
		if(siopList.get(0).getTransformer() == null) {
			
			if(StringUtils.ObjToString(CBSystemConstants.LineTagStatus.get(siopList.get(0).getLoadSwitch())).equals("")
					||("0").contains(CBSystemConstants.LineTagStatus.get(siopList.get(0).getLoadSwitch()))){//开关状态选择运行或不选择的
				RuleExeUtil.deviceStatusExecute(siopList.get(0).getLoadSwitch(), siopList.get(0).getLoadSwitch().getDeviceStatus(), "0");
			}
		
			
			
			//先操作母线相连的开关以外的开关
			if(siopList.get(0).getLoadMLLine()!=null
					&&siopList.get(0).getLoadSwitch().getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)
					&&!CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.InOutLine)){
				List<PowerDevice> mxswList = RuleExeUtil.getDeviceList(siopList.get(0).getLoadMLLine(), SystemConstants.Switch,
						SystemConstants.PowerTransformer, true, true, true);
				mxswList.remove(siopList.get(0).getLoadSwitch());
				List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
				if(CBSystemConstants.isCurrentSys){
					EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, mxswList, "请选择需要合上的开关：");
					chooseEquips=ecc.getChooseEquip();
				}
				
				for(PowerDevice choosedev:chooseEquips){
					RuleExeUtil.deviceStatusExecuteJB(choosedev, choosedev.getDeviceStatus(), "0");
				}
			}
			
			return true;
		}
		
		//合上高压侧母联开关
		for(PowerDevice sw : siopList.get(0).getSourceMLSwitchList()) {
			if(sw.getDeviceStatus().equals("1"))
				RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "0");
		}
		//电压等级由高到低
		for(int i = 0; i < siopList.size(); i++) {
			
			if(!StringUtils.ObjToString(CBSystemConstants.LineTagStatus.get(siopList.get(i).getLoadSwitch())).equals("")
					&&!("0").contains(CBSystemConstants.LineTagStatus.get(siopList.get(i).getLoadSwitch()))){//开关状态选择非运行状态的，不做处理
				continue;
			}
			//合上接地变开关
			if(siopList.get(i).getEarthSwitch() != null) {
				List<PowerDevice> tfList = new ArrayList<PowerDevice>();
				tfList.add(siopList.get(i).getLoadSwitch());
				tfList.add(siopList.get(i).getEarthSwitch());
//				int r = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择先合上哪个开关？", "选择", JOptionPane.DEFAULT_OPTION, JOptionPane.INFORMATION_MESSAGE, null, tfList.toArray(), tfList.toArray()[0]);
//				if (r == -1)
//					return false;
				//int r = JdbSwitchOrder.execute(siopList.get(i).getLoadSwitch());
				int r = 0;
				RuleExeUtil.deviceStatusExecute(tfList.get(r), tfList.get(r).getDeviceStatus(), "0");
				tfList.remove(r);
				RuleExeUtil.deviceStatusExecute(tfList.get(0), tfList.get(0).getDeviceStatus(), "0");
			}
			else
				//合上主变开关
				RuleExeUtil.deviceStatusExecute(siopList.get(i).getLoadSwitch(), siopList.get(i).getLoadSwitch().getDeviceStatus(), "0");
		}
		//电压等级由低到高拉开非双母接线的母联开关
		for(int i = siopList.size()-1; i >= 0; i--) {
			if(siopList.get(i).getLoadMLSwitch()!=null && !siopList.get(i).getLoadMLSwitch().getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
				RuleExeUtil.deviceStatusExecute(siopList.get(i).getLoadMLSwitch(), siopList.get(i).getLoadMLSwitch().getDeviceStatus(), "1");
		}
		//如果高压侧母联开关原先在分位则拉开
		for(PowerDevice sw : siopList.get(0).getSourceMLSwitchList()) {
			//如果开关后面需要保持合上则不拉开
			boolean isSwitchNeed = false;
			for(SwitchInfo si : siList) {
				if(si.getSourceMLSwitchList().contains(sw)) {
					isSwitchNeed = true;
					break;
				}
			}
			if(isSwitchNeed)
				continue;
			if(CBSystemConstants.LineTagStatus.containsKey(sw) && CBSystemConstants.LineTagStatus.get(sw).equals("0"))
				continue;
			if(RuleExeUtil.isDeviceChanged(sw))
				RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "1");
		}
		return true;
	}
	
	/**
	 * 热备用转冷备用
	 * @param pd
	 * @return
	 */
	public boolean convertLoadHotToCold(PowerDevice pd) {
		
		// 拉开主变刀闸
		List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeLoad(pd);
		for (PowerDevice kf : kfList) {
			RuleExeUtil.deviceStatusExecute(kf, kf.getDeviceStatus(), "1");
		}
				
		List<PowerDevice> swList = RuleExeUtil.getTransformerSwitchLoad(pd);
		RuleExeUtil.swapDeviceList(swList);
		
		for(PowerDevice sw : swList) {
			//接地变开关由热备用转冷备用
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(sw, SystemConstants.MotherLine, SystemConstants.PowerTransformer, "", CBSystemConstants.RunTypeSwitchFHC, false, true, true, true);
			for(PowerDevice ml : mlList) {
				List<PowerDevice> etswlist = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchJDB, "", false, true, true, false);
				for(PowerDevice etsw : etswlist) {
					RuleExeUtil.deviceStatusExecute(etsw, etsw.getDeviceStatus(), "2");
				}
			}
			String tagStatus = CBSystemConstants.LineTagStatus.get(sw);
			//主变开关优先转换成设备选择器选择状态
			if (tagStatus != null)
				RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), tagStatus); 
			else
				RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "2");

		}
		return true;
	}
	
	/**
	 * 冷备用转热备用
	 * @param pd
	 * @return
	 */
	public boolean convertLoadColdToHot(PowerDevice pd) {
		
		// 合上主变刀闸
		List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeLoad(pd);
		for (PowerDevice kf : kfList) {
			RuleExeUtil.deviceStatusExecute(kf, kf.getDeviceStatus(), "0");
		}
				
		List<PowerDevice> swList = RuleExeUtil.getTransformerSwitchLoad(pd);
		RuleExeUtil.swapLowDeviceList(swList);
		for(PowerDevice sw : swList) {
			if(!StringUtils.ObjToString(CBSystemConstants.LineTagStatus.get(sw)).equals("")
					&&("2,3").contains(CBSystemConstants.LineTagStatus.get(sw))){//开关状态选择为冷备用或者检修的，不做处理
				continue;
			}
			//电压等级由高到低将主变开关由冷备用转热备用
			RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "1");
		}
		for(PowerDevice sw : swList) {
			//接地变开关由冷备用转热备用
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(sw, SystemConstants.MotherLine, SystemConstants.PowerTransformer, "", CBSystemConstants.RunTypeSwitchFHC, false, true, true, true);
			for(PowerDevice ml : mlList) {
				List<PowerDevice> etswlist = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchJDB, "", false, true, true, false);
				for(PowerDevice etsw : etswlist) {
					RuleExeUtil.deviceStatusExecute(etsw, etsw.getDeviceStatus(), "1");
				}
			}
		}
		return true;
	}
	
	public List<SwitchInfo> initSwitchInfoOn(PowerDevice pd) {
		List<SwitchInfo> siList = new ArrayList<SwitchInfo>();
		List<PowerDevice> swList = RuleExeUtil.getTransformerSwitchLoad(pd);
		RuleExeUtil.swapDeviceList(swList);
		
		for(int i = 0; i < swList.size(); i++) {
			PowerDevice sw = swList.get(i);
			SwitchInfo si = new SwitchInfo();
			si.setLoadSwitch(swList.get(i));
			
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(sw, SystemConstants.MotherLine, SystemConstants.PowerTransformer, "", CBSystemConstants.RunTypeSwitchFHC, false, false, false, false);
			if(mlList.size() > 0)
				si.setLoadMLLine(mlList.get(0));
			siList.add(si);
		}
		
		for(int i = 0; i < siList.size(); i++) {
			SwitchInfo si = siList.get(i);
			PowerDevice sw = si.getLoadSwitch();
			PowerDevice ml = si.getLoadMLLine();
			if(ml == null)
				continue;
			
			//排除已经连接到其他主变的母线
			List<PowerDevice> linkTfList = RuleExeUtil.getDeviceList(ml, sw, SystemConstants.PowerTransformer, "", "", "", false, false, false, true);
			linkTfList.remove(pd);
//			if(linkTfList.size() > 0) {
//				si.setMLNeedOperate(false);
//				continue;
//			}
			List<PowerDevice> tfList = new ArrayList<PowerDevice>();
			List<PowerDevice> mlswLoadList = new ArrayList<PowerDevice>();
			
			List<PowerDevice> mlSwitchList = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, true, true);
			
			//根据跳投关系自动选择母联开关
			PowerDevice mupd=RuleExeUtil.getSwitchBackupSwitch(sw); //关联的母联
			if(mupd != null && mlSwitchList.contains(mupd)) {
				mlSwitchList.clear();
				mlSwitchList.add(mupd);
			}
			
			for(PowerDevice mlSwitch : mlSwitchList) {
				PowerDevice tf = null;
				HashMap<PowerDevice, ArrayList<PowerDevice>> pathMap = RuleExeUtil.getPathByDevice(mlSwitch, ml, SystemConstants.PowerTransformer, "", "", "", false, true, false, true);
				
				//比较最短路径
				int minDistance = 0;
				for(Iterator it = pathMap.entrySet().iterator();it.hasNext();) {  
		            Entry e = (Entry)it.next();  
		            PowerDevice p = (PowerDevice)e.getKey();
		            if(!p.getDeviceStatus().equals("0"))
		            	continue;
		            ArrayList<PowerDevice> path = (ArrayList<PowerDevice>)e.getValue();
		            if(path.size() < minDistance) {
		            	tf = p;
		            	minDistance = path.size();
		            }
		            else if(minDistance == 0) {
		            	tf = p;
	            		minDistance = path.size();
		            }
				}
				if(tf != null&& !tfList.contains(tf)){
					tfList.add(tf);
					mlswLoadList.add(mlSwitch);
				}
			}

			if(tfList.size() == 0) {
				//RuleExeUtil.deviceStatusExecute(ml, ml.getDeviceStatus(), "2");
				continue;
			}
			int sel = 0;
			if(tfList.size() > 1) {
				if(pd.getPowerVoltGrade() < 500) {
					if(CBSystemConstants.isCurrentSys) {
						// 图形开票启动时显示弹窗
						sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择["+ml.getPowerStationName()+"]["+ml.getPowerDeviceName()+"]上负荷倒至哪台主变", "选择代供主变", JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE, null, tfList.toArray(), tfList.toArray()[0]);
						if (sel == -1)
							return null;
					} else {
						// 非图形开票启动时默认选择第一项
						sel = 0;
					}
				}
			}
			else if(tfList.size() == 1) {
				sel = 0;
			}
			
			si.setLoadMLSwitch(mlswLoadList.get(sel));
			
			si.setTransformer(tfList.get(sel));
			
			List<PowerDevice> devList = new ArrayList<PowerDevice>();
			devList = RuleExeUtil.getTransformersLinkDevice(pd, tfList.get(sel));
			if(devList != null) {
				for (PowerDevice dev : devList) {
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
						si.getSourceMLSwitchList().add(dev);
					}
				}
			}
		}
		
//		for(int i = siList.size()-1; i >= 0; i--) {
//			if(siList.get(i).getTransformer() == null)
//				siList.remove(i);
//		}
		
		
		//将低压侧代供主变较远的排前面
		for(int i = 0; i < siList.size(); i++) {
			SwitchInfo si = siList.get(i);
			if(si.getLoadSwitch().getPowerVoltGrade() > siList.get(0).getLoadSwitch().getPowerVoltGrade())
				continue;
			if (si.getSourceMLSwitchList().size() > 1) {
				siList.remove(si);
				siList.add(0, si);
			}
		}
		
		//将低压侧连接了接地变的开关排后面
		for(int i = siList.size()-1; i >= 0; i--) {
			SwitchInfo si = siList.get(i);
			if(si.getLoadSwitch().getPowerVoltGrade() > siList.get(0).getLoadSwitch().getPowerVoltGrade())
				continue;
			PowerDevice ml = si.getLoadMLLine();
			if(ml == null)
				continue;
			List<PowerDevice> etswlist = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchJDB, "", false, true, true, false);
			if (etswlist.size() > 0) {
				si.setEarthSwitch(etswlist.get(0));
				for(int j = 0; j < siList.size(); j++) {
					if(siList.get(j).getLoadMLLine().getPowerVoltGrade() == ml.getPowerVoltGrade()) {
						siList.remove(si);
						siList.add(j, si);
					}
				}
			}else{//没有接地变再判断是否有消弧线圈开关
				List<PowerDevice> xhxqswlist = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXHXQ, "", false, true, true, false);
				if (xhxqswlist.size() > 0) {
					si.setEarthSwitch(xhxqswlist.get(0));
					for(int j = 0; j < siList.size(); j++) {
						if(siList.get(j).getLoadMLLine().getPowerVoltGrade() == ml.getPowerVoltGrade()) {
							siList.remove(si);
							siList.add(j, si);
						}
					}
				}
			}
		}
		//将转供主变相同的排前面
		for(int i = 0; i < siList.size(); i++) {
			for(int j = siList.size()-1; j > i; j--) {
				SwitchInfo si = siList.get(j);
				PowerDevice tf = si.getTransformer();
				if(tf == null || siList.get(i).getTransformer()==null)
					continue;
				if(tf.equals(siList.get(i).getTransformer())) {
					siList.remove(si);
					siList.add(i+1, si);
				}
			}
		}
		return siList;
	}
	
	public List<SwitchInfo> initSwitchInfoOff(PowerDevice pd) {
		List<SwitchInfo> siList = new ArrayList<SwitchInfo>();
		List<PowerDevice> swList = RuleExeUtil.getTransformerSwitchLoad(pd);
		RuleExeUtil.swapDeviceList(swList);
		
		for(int i = 0; i < swList.size(); i++) {
			PowerDevice sw = swList.get(i);
			SwitchInfo si = new SwitchInfo();
			si.setLoadSwitch(swList.get(i));
			
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(sw, SystemConstants.MotherLine, SystemConstants.PowerTransformer, "", CBSystemConstants.RunTypeSwitchFHC, false, false, false, false);
			if(mlList.size() > 0)
				si.setLoadMLLine(mlList.get(0));
			siList.add(si);
		}
		
		for(int i = 0; i < siList.size(); i++) {
			SwitchInfo si = siList.get(i);
			PowerDevice ml = si.getLoadMLLine();
			
			if(ml == null)
				continue;
			
			List<PowerDevice> tfList = RuleExeUtil.getDeviceList(ml, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, false, true);

			if(tfList.size() != 1)
				continue;
			PowerDevice tf = tfList.get(0);
			
			si.setTransformer(tf);
			
//			List<PowerDevice> mlswLoadList = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, true, true);
//			for (PowerDevice dev : mlswLoadList) {
//				if(dev.getDeviceStatus().equals("0"))
//					si.setLoadMLSwitch(dev);
//			}
			
			List<PowerDevice> mlswLoadList = new ArrayList<PowerDevice>();
			List<PowerDevice> pathList = RuleExeUtil.getPathByDevice(ml, tf, SystemConstants.PowerTransformer, "", false, false);
			for(PowerDevice dev:pathList){
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					mlswLoadList.add(dev);
				}
			}
			
			if(mlswLoadList.size()==1){
				si.setLoadMLSwitch(mlswLoadList.get(0));
			}else if(mlswLoadList.size()>1){
				EquipRadioChoose dcd = new EquipRadioChoose(SystemConstants.getMainFrame(), true, mlswLoadList, "请选择需要操作的分段开关.");
				PowerDevice device = dcd.getChooseEquip();
				if(device!=null){
					si.setLoadMLSwitch(device);
				}
			}
			
				
			
			List<PowerDevice> devList = new ArrayList<PowerDevice>();
			devList = RuleExeUtil.getTransformersLinkDevice(pd, tf);
			if(devList != null) {
				for (PowerDevice dev : devList) {
					if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
						si.getSourceMLSwitchList().add(dev);
					}
				}
			}
		}
//		for(int i = siList.size()-1; i >= 0; i--) {
//			if(siList.get(i).getTransformer() == null)
//				siList.remove(i);
//		}
		
		//将低压侧代供主变较远的排前面
		for(int i = 0; i < siList.size(); i++) {
			SwitchInfo si = siList.get(i);
			if(si.getLoadSwitch().getPowerVoltGrade() > siList.get(0).getLoadSwitch().getPowerVoltGrade())
				continue;
			if (si.getSourceMLSwitchList().size() > 1) {
				siList.remove(si);
				siList.add(0, si);
			}
		}
		
		//将低压侧连接了接地变的开关排后面
		for(int i = siList.size()-1; i >= 0; i--) {
			SwitchInfo si = siList.get(i);
			if(si.getLoadSwitch().getPowerVoltGrade() > siList.get(0).getLoadSwitch().getPowerVoltGrade())
				continue;
			PowerDevice ml = si.getLoadMLLine();
			if(ml == null)
				continue;
			List<PowerDevice> etswlist = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchJDB, "", false, true, true, false);
			if (etswlist.size() > 0) {
				si.setEarthSwitch(etswlist.get(0));
				for(int j = 0; j < siList.size(); j++) {
					if(siList.get(j).getLoadMLLine().getPowerVoltGrade() == ml.getPowerVoltGrade()) {
						siList.remove(si);
						siList.add(j, si);
					}
				}
			}else{//没有接地变再判断是否有消弧线圈开关
				List<PowerDevice> xhxqswlist = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXHXQ, "", false, true, true, false);
				if (xhxqswlist.size() > 0) {
					si.setEarthSwitch(xhxqswlist.get(0));
					for(int j = 0; j < siList.size(); j++) {
						if(siList.get(j).getLoadMLLine().getPowerVoltGrade() == ml.getPowerVoltGrade()) {
							siList.remove(si);
							siList.add(j, si);
						}
					}
				}
			}
		}
		//将转供主变相同的排前面
		for(int i = 0; i < siList.size(); i++) {
			for(int j = siList.size()-1; j > i; j--) {
				SwitchInfo si = siList.get(j);
				PowerDevice tf = si.getTransformer();
				if(tf == null || siList.get(i).getTransformer()==null)
					continue;
				if(tf.equals(siList.get(i).getTransformer())) {
					siList.remove(si);
					siList.add(i+1, si);
				}
			}
		}
		
		
//		//将代供主变较远的排后面
//		for(int i = 0; i < siList.size(); i++) {
//			SwitchInfo si = siList.get(i);
//			PowerDevice ml = si.getLoadMLLine();
//			if (si.getSourceMLSwitchList().size() > 1) {
//				for(int j = 0; j < siList.size(); j++) {
//					if(siList.get(j).getLoadMLLine().getPowerVoltGrade() == ml.getPowerVoltGrade()) {
//						siList.remove(si);
//						siList.add(j, si);
//					}
//				}
//			}
//		}
//		//将连接了接地变的开关排前面
//		for(int i = 0; i < siList.size(); i++) {
//			SwitchInfo si = siList.get(i);
//			PowerDevice ml = si.getLoadMLLine();
//			List<PowerDevice> etswlist = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchJDB, "", false, true, true, false);
//			if (etswlist.size() > 0) {
//				si.setEarthSwitch(etswlist.get(0));
//				siList.remove(si);
//				siList.add(0, si);
//			}
//		}
//		//将转供主变相同的排前面
//		for(int i = 0; i < siList.size(); i++) {
//			for(int j = siList.size()-1; j > i; j--) {
//				SwitchInfo si = siList.get(j);
//				PowerDevice tf = si.getTransformer();
//				if(tf.equals(siList.get(i).getTransformer())) {
//					siList.remove(si);
//					siList.add(i+1, si);
//				}
//			}
//		}
		Collections.reverse(siList);
		return siList;
	}
	
	

}

class SwitchInfo {
	private PowerDevice loadSwitch = null; //主变开关
	private PowerDevice loadMLLine = null; //主变开关连接的母线
	private PowerDevice loadMLSwitch = null; //倒负荷的母联开关
	private List<PowerDevice> sourceMLSwitchList = new ArrayList<PowerDevice>(); //高压侧母联开关
	private PowerDevice transformer = null; //倒负荷的主变
	private PowerDevice earthSwitch = null; //接地变开关
	private boolean isMLNeedOperate; //母线是否需要操作
	
	
	public PowerDevice getLoadSwitch() {
		return loadSwitch;
	}
	public void setLoadSwitch(PowerDevice loadSwitch) {
		this.loadSwitch = loadSwitch;
	}
	public PowerDevice getLoadMLLine() {
		return loadMLLine;
	}
	public void setLoadMLLine(PowerDevice loadMLLine) {
		this.loadMLLine = loadMLLine;
	}
	public PowerDevice getLoadMLSwitch() {
		return loadMLSwitch;
	}
	public void setLoadMLSwitch(PowerDevice loadMLSwitch) {
		this.loadMLSwitch = loadMLSwitch;
	}
	
	public List<PowerDevice> getSourceMLSwitchList() {
		return sourceMLSwitchList;
	}
	public void setSourceMLSwitchList(List<PowerDevice> sourceMLSwitchList) {
		this.sourceMLSwitchList = sourceMLSwitchList;
	}
	public PowerDevice getEarthSwitch() {
		return earthSwitch;
	}
	public void setEarthSwitch(PowerDevice earthSwitch) {
		this.earthSwitch = earthSwitch;
	}
	public PowerDevice getTransformer() {
		return transformer;
	}
	public void setTransformer(PowerDevice transformer) {
		this.transformer = transformer;
	}
	public boolean isMLNeedOperate() {
		return isMLNeedOperate;
	}
	public void setMLNeedOperate(boolean isMLNeedOperate) {
		this.isMLNeedOperate = isMLNeedOperate;
	}
	
}
