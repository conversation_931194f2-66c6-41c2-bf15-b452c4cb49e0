package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-12-18 上午11:41:04   
* 修改人：郑柯   
* 修改备注：   拉刀闸与开关闭锁规则
* @version    
*    
*/
public class CheckKnifeOffWithSwitchSequence  implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd = rbm.getPd();
		List<PowerDevice> listSwitch = null;
		listSwitch = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);//获取刀闸连接的开关
		if(listSwitch == null || listSwitch.size() == 0)//如果直接连接的设备中没有开关则返回true
			return true;
		else{
			PowerDevice switchTemp = listSwitch.get(0);
			if(!switchTemp.getDeviceStatus().equals("0"))//如果开关在分位则返回true
				return true;
			else{
				
				//20141218 双母接线方式下如果本侧存在其他合上的刀闸，则本刀闸可以拉开
				if(switchTemp.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {//如果是双母接线方式
					PowerDevice otherKnife = RuleExeUtil.getKnifeOtherML(pd);
					if(otherKnife != null && otherKnife.getDeviceStatus().equals("0"))
						return true;
				}
				CheckMessage cm=new CheckMessage();
				List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
				pdlist.add(switchTemp);
				cm.setPd(pdlist);
				//不能带负荷拉合刀闸
				cm.setBottom("111");
				CBSystemConstants.lcm.add(cm);
				return true;
			}
		}
	}

}
