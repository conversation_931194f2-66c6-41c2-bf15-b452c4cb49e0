package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**
 * 其他开关拉开判断算法
 * 
 * <AUTHOR>
 * 
 */
public class CheckQTSwitchOff implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		if (!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)
				&& !pd.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchFHC)) {
			return true;
		} else {
			// 判断开关所在间隔是否存在接地变，判断是否接地电阻开关
			List<PowerDevice> pdList = RuleExeUtil.getDeviceList(pd,
					SystemConstants.EarthingTransformer, null, false, true,
					true);
			if (pdList.size() < 0) {
				List<PowerDevice> kgList = RuleExeUtil.getDeviceList(pdList
						.get(0), CBSystemConstants.RunTypeSwitchJDB, null,
						false, true, true);
				if (kgList.size() > 0) {
					//查找开关连接的运行的母线，判断是否存在
					List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(
							pd, SystemConstants.MotherLine);
					PowerDevice cmx = null;
					for (PowerDevice mx : mxList) {
						if (mx.getDeviceStatus().equals("0")) {
							cmx = mx;
							break;
						}
					}
					if (cmx != null) {
						//判断母线上是否有其他连通的接地变
						List<PowerDevice> jdList = RuleExeUtil.getDeviceList(
								cmx, CBSystemConstants.RunTypeSwitchJDB, null,
								false, true, true);
						if(jdList.size()>1){
							return true;
						}else{
							CheckMessage cm = new CheckMessage();
							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
							pdlist.add(pd);
							cm.setBottom("153");
							cm.setPd(pdlist);
							CBSystemConstants.lcm.add(cm);
							return true;
						}
					} else {
						return true;
					}
				} else {
					return true;
				}
			} else {
				return true;
			}
		}
	}
}
