package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-11 上午10:20:28   
* 修改人：FU   
* 修改备注：   旁路开关在合位，旁路刀闸不可拉合
* @version    
*    
*/
public class judgePLDZPublic implements RulebaseInf{

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null){
			return true;
		}
		List<CheckMessage> cml=rbm.getMessage().get("sbxx");
		if(cml==null){
			cml=new ArrayList<CheckMessage>();
			rbm.getMessage().put("sbxx", cml);
		}
		//目标状态
		String rbmendstate=rbm.getEndState();
		//设备
		PowerDevice pd=rbm.getPd();
		//操作状态为拉开
		if(rbmendstate.equals("0")){
			return true;
		}
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)){
			return true;
		}
		//旁路开关
		PowerDevice pl=null;
		List<PowerDevice> pllist=RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, null, CBSystemConstants.RunTypeSwitchPL, null, false, true, false, true);
		if(pllist.size()>0){
			pl=pllist.get(0);
		}
		if(pl!=null){
			if(pl.getDeviceStatus().equals("0")){
//				String sbtx="旁路开关在合位，旁路刀闸不能拉合";
//				CheckMessage cm=new CheckMessage();
//				cm.setMessage(sbtx);
//				cm.setPd(pl);
//				cm.setBottom("2");
//				cml.add(cm);
			}else{
				return true;
			}
		}
		return true;
	}

}
