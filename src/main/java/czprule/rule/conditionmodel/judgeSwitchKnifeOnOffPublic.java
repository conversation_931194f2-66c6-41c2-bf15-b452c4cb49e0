package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.EMSService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-10 下午02:19:29   
* 修改人：FU   
* 修改备注：   停送电开关刀闸顺序规则
* @version    
*    
*/
public class judgeSwitchKnifeOnOffPublic implements RulebaseInf{

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null) {
			return false;
		}
		List<CheckMessage> cml=rbm.getMessage().get("sbxx");
		if(cml==null){
			cml=new ArrayList<CheckMessage>();
			rbm.getMessage().put("sbxx", cml);
		}
		//目标状态
		String rbmendstate=rbm.getEndState();
		//设备
		PowerDevice pd=rbm.getPd();
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
			//线路开关
			if(rbmendstate.equals("0")){
				//目标状态闭合
				PowerDevice line=null;
				List<PowerDevice> linelist=RuleExeUtil.getDeviceList(pd,SystemConstants.InOutLine,null,null,null,false,true,true,true);
				if(linelist.size()==0){
					return true;
				}else{
					line=linelist.get(0);
				}
				if(line!=null){
					//潮流判断
					String cl=EMSService.getService().getLineFlow(line.getPowerStationID(), line.getPowerDeviceID());
					if(cl.equals("-1")||cl.equals("0")){
						//无数据或无功
						return true;
					}else if(cl.equals("1")){
						//进线
						return true;
					}else{
						//出线
						//对侧厂站线路
						PowerDevice otheresideline=null;
						List<PowerDevice> othersidelinelist=RuleExeUtil.getLineOtherSideList(line);
						if(othersidelinelist.size()==0){
							return true;
						}else{
							otheresideline=othersidelinelist.get(0);
						}
						//对侧厂站开关
						if(otheresideline!=null){
							PowerDevice othersideswith=null;
							List<PowerDevice> othersideswiths=RuleExeUtil.getDeviceList(otheresideline, SystemConstants.Switch, null, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true);
							if(othersideswiths.size()==0){
								return true;
							}else{
								othersideswith=othersideswiths.get(0);
							}
							if(othersideswith!=null){
								if(!othersideswith.getDeviceStatus().equals("0")){
//									String sbtx="停电操作，请先拉开负荷侧开关";
//									CheckMessage cm=new CheckMessage();
//									cm.setMessage(sbtx);
//									cm.setPd(othersideswith);
//									cm.setBottom("1");
//									cml.add(cm);
								}
							}
						}
					}
				}
			}else{
				//目标状态拉开
				PowerDevice line=null;
				List<PowerDevice> linelist=RuleExeUtil.getDeviceList(pd,SystemConstants.InOutLine,null,null,null,false,true,true,true);
				if(linelist.size()==0){
					return true;
				}else{
					line=linelist.get(0);
				}
				if(line!=null){
					//潮流判断
					String cl=EMSService.getService().getLineFlow(line.getPowerStationID(), line.getPowerDeviceID());
					if(cl.equals("-1")||cl.equals("0")){
						//无数据或无功
						return true;
					}else if(cl.equals("2")){
						//出线
						return true;
					}else{
						//进线
						//对侧厂站线路
						PowerDevice otheresideline=null;
						List<PowerDevice> othersidelinelist=RuleExeUtil.getLineOtherSideList(line);
						if(othersidelinelist.size()==0){
							return true;
						}else{
							otheresideline=othersidelinelist.get(0);
						}
						//对侧厂站开关
						if(otheresideline!=null){
							PowerDevice othersideswith=null;
							List<PowerDevice> othersideswiths=RuleExeUtil.getDeviceList(otheresideline, SystemConstants.Switch, null, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true);
							if(othersideswiths.size()==0){
								return true;
							}else{
								othersideswith=othersideswiths.get(0);
							}
							if(othersideswith!=null){
								if(othersideswith.getDeviceStatus().equals("0")){
//									String sbtx="送电操作，请先合上电源侧开关";
//									CheckMessage cm=new CheckMessage();
//									cm.setMessage(sbtx);
//									cm.setPd(othersideswith);
//									cm.setBottom("1");
//									cml.add(cm);
								}
							}
						}
					}
				}
			}
		}else if(pd.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			//刀闸
			if(rbmendstate.equals("0")){
				//目标状态闭合
				//判断刀闸的电源负荷侧
				String dyfh=RuleExeUtil.JudgeknifeIsPowerSide(pd);
				if(dyfh.equals("1")){
					//电源侧
					return true;
				}else if(dyfh.equals("2")){
					//负荷侧
					PowerDevice kg=null;
					List<PowerDevice> kglist=RuleExeUtil.getKnifeRelateSwitch(pd);
					if(kglist.size()>0){
						kg=kglist.get(0);
					}
					if(kg!=null){
						PowerDevice dcdz=null;
						List<PowerDevice> zjlist=RuleExeUtil.getDeviceDirectList(kg, SystemConstants.SwitchSeparate);
						for(int i=0;i<zjlist.size();i++){
							PowerDevice dz=zjlist.get(i);
							if(dz.equals(pd)){
								zjlist.remove(dz);
								i--;
							}
							if(dz.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
								zjlist.remove(dz);
								i--;
							}
						}
						if(zjlist.size()>0){
							dcdz=zjlist.get(0);
						}
						if(dcdz!=null){
							if(dcdz.getDeviceStatus().equals("0")){
								return true;
							}else{
//								String sbtx="送电操作，请先合上电源侧刀闸";
//								CheckMessage cm=new CheckMessage();
//								cm.setMessage(sbtx);
//								cm.setPd(dcdz);
//								cm.setBottom("1");
//								cml.add(cm);
							}
						}
					}
				}else{
					//无法判断
					return true;
				}
			}else{
				//目标状态拉开
				//判断刀闸的电源负荷侧
				String dyfh=RuleExeUtil.JudgeknifeIsPowerSide(pd);
				if(dyfh.equals("1")){
					//电源侧
					PowerDevice kg=null;
					List<PowerDevice> kglist=RuleExeUtil.getKnifeRelateSwitch(pd);
					if(kglist.size()>0){
						kg=kglist.get(0);
					}
					if(kg!=null){
						PowerDevice dcdz=null;
						List<PowerDevice> zjlist=RuleExeUtil.getDeviceDirectList(kg, SystemConstants.SwitchSeparate);
						for(int i=0;i<zjlist.size();i++){
							PowerDevice dz=zjlist.get(i);
							if(dz.equals(pd)){
								zjlist.remove(dz);
								i--;
							}
							if(dz.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
								zjlist.remove(dz);
								i--;
							}
						}
						if(zjlist.size()>0){
							dcdz=zjlist.get(0);
						}
						if(dcdz!=null){
							if(dcdz.getDeviceStatus().equals("0")){
//								String sbtx="停电操作，请先拉开负荷侧刀闸";
//								CheckMessage cm=new CheckMessage();
//								cm.setMessage(sbtx);
//								cm.setPd(dcdz);
//								cm.setBottom("1");
//								cml.add(cm);
							}else{
								return true;
							}
						}
						int n=0;
					}
				}else if(dyfh.equals("2")){
					//负荷侧
					return true;
				}else{
					//无法判断
					return true;
				}
			}
		}else{
			return true;
		}
		return true;
	}

}
