package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-16 上午11:58:44   
* 修改人：FU   
* 修改备注：   刀闸与设备闭锁规则
* @version    
*    
*/
public class CheckKnifeWithPowerDevice implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd = rbm.getPd();
		
		List<PowerDevice> listSwitch = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);//获取刀闸连接的开关
		if(listSwitch.size() > 0)
			return true;
		
		
		
		//判断是否为旁路刀闸
		if( pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL))
			return true;
		
		//判断是否为10kV代线路开关刀闸
		if(pd.getPowerVoltGrade()==10&& pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXLS))
			return true;
		
		//识别设备类型判断设备是否是隔直刀闸
		if(pd.getPowerDeviceName().contains("隔直")){
			return true;
		}
		
		List<PowerDevice> listOtherDevice = null;
		listOtherDevice = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.InOutLine+","+SystemConstants.PowerTransformer);
		
		if(listOtherDevice.size() > 0) {
			for (PowerDevice switchTemp : listOtherDevice) {
				if(switchTemp.getDeviceStatus().equals("0")) {
					CheckMessage cm=new CheckMessage();
					List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
					pdlist.add(switchTemp);
					cm.setPd(pdlist);
					//不能带负荷拉合刀闸
					if(rbm.getEndState().equals("0"))
						cm.setBottom("132");
					else
						cm.setBottom("112");
					CBSystemConstants.lcm.add(cm);
					return true;
				}
				else {
					List<PowerDevice>  listSrc = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine+","+SystemConstants.PowerTransformer+","+SystemConstants.MotherLine, SystemConstants.InOutLine+","+SystemConstants.PowerTransformer+","+SystemConstants.MotherLine, false, true, true);
					if(listSrc.contains(switchTemp))
						listSrc.remove(switchTemp);
					for(PowerDevice src : listSrc) {
						if(src.getDeviceStatus().equals("0")) {
							CheckMessage cm=new CheckMessage();
							List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
							pdlist.add(src);
							cm.setPd(pdlist);
							//不能带负荷拉合刀闸
							if(rbm.getEndState().equals("0"))
								cm.setBottom("132");
							else
								cm.setBottom("112");
							CBSystemConstants.lcm.add(cm);
							return true;
						}
					}
					
				}
			}
		}
		
		List<PowerDevice> listDz = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate);//获取刀闸连接的刀闸
		if(listDz.size() > 0) {
			listSwitch = RuleExeUtil.getDeviceDirectList(listDz.get(0), SystemConstants.Switch);//获取刀闸连接的开关
			if(listSwitch.size() > 0)
				return true;
		}
		/*
		listPTandArrester = RuleExeUtil.getDeviceDirectList(pd, "Arrester,PT,BusbarSection");
		listSwitch = RuleExeUtil.getDeviceDirectList(pd, "Breaker,Disconnector,GroundDisconnector");
		
		if (listPTandArrester != null && listPTandArrester.size() > 0)// 如果存在避雷器和电压互感器则可以拉、合刀闸，返回true
			return true;
		else {
			if (listOtherDevice != null && listOtherDevice.size() > 0) {
				for (PowerDevice switchTemp : listOtherDevice) {
					if (switchTemp.getDeviceType().equals(SystemConstants.Switch) && !switchTemp.getDeviceStatus().equals("0"))
						return true;
				}
				for (PowerDevice switchTemp : listOtherDevice) {
					if (listPTandArrester.contains(switchTemp) || listSwitch.contains(switchTemp))
						continue;
					if (switchTemp.getDeviceType().equals(SystemConstants.Switch) && !switchTemp.getDeviceStatus().equals("0"))
						return true;
					if (switchTemp.getDeviceStatus().equals("0")) {// 如果连接的设备有处于合位的，则不可以操作刀闸，返回false
						CheckMessage cm=new CheckMessage();
						List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
						pdlist.add(switchTemp);
						cm.setPd(pdlist);
						//不能带负荷拉合刀闸
						if(rbm.getEndState().equals("0"))
							cm.setBottom("132");
						else
							cm.setBottom("112");
						CBSystemConstants.lcm.add(cm);
						return true;
					}
				}
				return true;
			}
		}
		*/
		return true;
	}
}
