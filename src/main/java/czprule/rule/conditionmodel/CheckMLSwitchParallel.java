package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

public class CheckMLSwitchParallel  implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)) {
			if(!pd.getPowerDeviceName().contains("母联"))
				return true;
		}
		else if (!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))//判断是否为母联开关
			return true;
		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))//判断是否为双母接线
			return true;
		double switchPowerVoltGrade = pd.getPowerVoltGrade();// 获取开关的电压等级
		String powerStationID = pd.getPowerStationID();// 获取变电站ID
		PowerDevice powerStation = CBSystemConstants.getMapPowerStation().get(
				powerStationID);// 获取变电站
		double powerStationPowerVoltGrade = powerStation.getPowerVoltGrade();// 获取变电站电压等级
		if (switchPowerVoltGrade == powerStationPowerVoltGrade)
			return true;
		List<PowerDevice> transformerList1 = RuleExeUtil.getDeviceList(pd,
				SystemConstants.PowerTransformer, SystemConstants.InOutLine,
				"", "", false, false, false, true, "1");//向开关的一侧搜索主变
		List<PowerDevice> transformerList2 = RuleExeUtil.getDeviceList(pd,
				SystemConstants.PowerTransformer, SystemConstants.InOutLine,
				"", "", false, false, false, true, "2");//向开关的另一侧搜索主变
		if(transformerList1.size() <=0 || transformerList2.size() <= 0)
			return true;
		
		List<PowerDevice> lineList1 = RuleExeUtil.getDeviceList(transformerList1.get(0),
				SystemConstants.InOutLine, SystemConstants.PowerTransformer,
				true, true, true);//向开关的另一侧搜索主变
		List<PowerDevice> lineList2 = RuleExeUtil.getDeviceList(transformerList2.get(0),
				SystemConstants.InOutLine, SystemConstants.PowerTransformer,
				true, true, true);//向开关的另一侧搜索主变
		if(lineList1.size() > 0 || lineList2.size() > 0)
			return true; //线变组
		
		List<PowerDevice> blTransformerList = null;
		for(PowerDevice transformer1Temp : transformerList1){
			blTransformerList = RuleExeUtil.getTransformerBL(transformer1Temp);
			if(blTransformerList.size() > 0){
				for(PowerDevice transformer2Temp : transformerList2){
					if(blTransformerList.contains(transformer2Temp))
						return true;//搜索是否存在与开关一侧的主变并列运行的开关另一侧的主变，只要存在，则返回true
				}
			}
		}
		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
		pdlist.add(transformerList1.get(0));
		pdlist.add(transformerList2.get(0));
		CheckMessage cm = new CheckMessage();
		cm.setBottom("277");
		cm.setPd(pdlist);
		CBSystemConstants.lcm.add(cm);
		
		return true;
	}

}
