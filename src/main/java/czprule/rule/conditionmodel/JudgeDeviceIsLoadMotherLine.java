package czprule.rule.conditionmodel;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 判断母线或者开关是否可以进行导母操作
 * 作    者 : 张余平
 * 开发日期 : 2011-07-21
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.DeviceSearchManager;
import czprule.system.ShowMessage;

public class JudgeDeviceIsLoadMotherLine implements RulebaseInf{

	/**
	 * 开关导母判断：开关是双母接线方式，双母之间存在处于运行的母联开关或母联刀闸
	 * 母线导母判断：双母接线方式，并且和另一条母线间存在处于运行的母联开关
	 */
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		
		if(!pd.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
		    return false;
		}
		
		CommonSearch cs=new CommonSearch();
        Map<String,Object> inPara = new HashMap<String,Object>();
        Map<String,Object> outPara = new HashMap<String,Object>();
        PowerDevice tempDev=null;
        
        List<PowerDevice> expDevs=new ArrayList<PowerDevice>();
		List<PowerDevice> searchDevs=null;
		
		if(pd.getDeviceType().equals(SystemConstants.Switch)){
			   
			   PowerDevice linkMotherLine=null; //开关连接的母线  通路是合上的，双母接线方式
			   PowerDevice notLinkMotherLine=null;  //开关连接的母线 通路是断开的，双母接线方式
			
			   //搜索闭合连接的母线
			   inPara.put("oprSrcDevice", pd);
		       inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
		       inPara.put("isSearchOffPath", "false"); //搜索闭合通路
		       inPara.put("excDevType", SystemConstants.PowerTransformer+","+SystemConstants.Switch); //排除主变  ,排除开关主要是防止桥式接线方式连接到单母分段的另一段母线 
		       cs.execute(inPara, outPara);
		       searchDevs = (ArrayList) outPara.get("linkedDeviceList");
		       for (int i = 0; i < searchDevs.size(); i++) {
				   tempDev=searchDevs.get(i);
				   if(tempDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)
						   &&tempDev.getDeviceStatus().equals("0")){
					   linkMotherLine=tempDev;
					   break;
		    	    
				   }
			   }
		       if(linkMotherLine==null)
		    	   return false;
		       //搜索开关断开连接的母线
		       inPara.put("isSearchOffPath", "true"); 
		       expDevs.add(linkMotherLine);
		       inPara.put("excDevList", expDevs);
		       cs.execute(inPara, outPara);
		       inPara.clear();
		       expDevs.clear();
		       searchDevs = (ArrayList) outPara.get("linkedDeviceList");
		       for (int i = 0; i < searchDevs.size(); i++) {
				   tempDev=searchDevs.get(i);
				   if(tempDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)
						   &&tempDev.getDeviceStatus().equals("0")){
					   notLinkMotherLine=tempDev;
					   break;
				   }
			   }
		       if(notLinkMotherLine==null)
		    	   return false;

			   DeviceSearchManager dsm=new DeviceSearchManager();
			   expDevs.add(linkMotherLine);
			   List<PowerDevice> searchMLS=dsm.getMotherLinesByML(linkMotherLine, expDevs, false);
		       if(searchMLS.contains(notLinkMotherLine))
		            return true;
		       else
		    	    return false;
		}
		
		if(pd.getDeviceType().equals(SystemConstants.MotherLine)){
			
			   List<PowerDevice> motherLines=new ArrayList<PowerDevice>(); //母线搜索的其他母线集合
			   DeviceSearchManager dsm=new DeviceSearchManager();
			   expDevs.add(pd);
			   List<PowerDevice> searchMLS=dsm.getMotherLinesByML(pd, expDevs, true);
		       for (int i = 0; i < searchMLS.size(); i++) {
		    	   tempDev=(PowerDevice)searchMLS.get(i);
		    	   if(CBSystemConstants.RunModelDoubleMotherLine.equals(tempDev.getDeviceRunModel())
		    			   &&tempDev.getDeviceStatus().equals("0")){
		    		   motherLines.add(tempDev);
		    	   }
			   }
		       if(motherLines.size()==0){
		    	   ShowMessage.view("["+pd.getPowerDeviceName()+"]当前接线方式不满足导母条件！");
		    	   return false;
		       }
		       /////
		       boolean isExistRunDevice = false;
		       inPara.put("oprSrcDevice", pd);
		       inPara.put("tagDevType", SystemConstants.Switch); //目标设备开关
		       inPara.put("excDevType", SystemConstants.PowerTransformer); //排除主变  
		       inPara.put("isSearchOffPath",false);	//不搜索断开路径
		       cs.execute(inPara, outPara);
		       inPara.clear();
		       searchDevs = (ArrayList) outPara.get("linkedDeviceList");
		       for(Object obj:searchDevs){
		    	   tempDev=(PowerDevice) obj;
					if(!tempDev.getDeviceStatus().equals("0") && !tempDev.getDeviceStatus().equals("1"))
						continue;	
					if(!tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
						&&!tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)
						  &&!tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC))
						continue;
					if(!tempDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
						continue;
					isExistRunDevice = true;
					break;
		       }
		       if(!isExistRunDevice){
		    	   ShowMessage.view("["+pd.getPowerDeviceName()+"]上没有处于运行或热备用状态的开关！");
		    	   return false;
		       }
		    	/////
		    	   
		       //搜索母线连接的开关
		       List<PowerDevice> oneSwitchs=new ArrayList<PowerDevice>();
			   inPara.put("oprSrcDevice", pd);
		       inPara.put("tagDevType", SystemConstants.Switch); //目标设备开关
		       inPara.put("excDevType", SystemConstants.PowerTransformer); //排除主变  
		       cs.execute(inPara, outPara);
		       searchDevs = (ArrayList) outPara.get("linkedDeviceList");
		       for (int i = 0; i < searchDevs.size(); i++) {
				   tempDev=searchDevs.get(i);
				   if(!tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
							&&!tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)
							  &&!tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC))
						continue;
				   if(tempDev.getDeviceStatus().equals("0"))
					   oneSwitchs.add(tempDev);
			   }
		       
		       List twoSwitchs=new ArrayList();
		       for (int i = 0; i < motherLines.size(); i++) {
		    	   tempDev=motherLines.get(i);
		    	   inPara.put("oprSrcDevice", tempDev);
			       cs.execute(inPara, outPara);
			       twoSwitchs.addAll((ArrayList) outPara.get("linkedDeviceList"));
			   }
		       if(oneSwitchs.size()==0||twoSwitchs.size()==0)
		    	   return false;
		       
		       for (int i = 0; i < oneSwitchs.size(); i++) {
				   tempDev=oneSwitchs.get(i);
				   if(twoSwitchs.contains(tempDev))
					   return true;
			   }
		       return false;
		}
		return false;
	}

}
