package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
/**
 * 母联开关合上特殊规则
 * <AUTHOR>
 *
 */
public class CheckMLSwitchOnSpecial implements RulebaseInf {
	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			return false;
		}
		// 设备目标状态
		String rbmendstate = rbm.getEndState();
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			return false;
		}
		if (!pd.getDeviceSetType().equals(CBSystemConstants.RunTypeSwitchML))
			return true;
		List<PowerDevice> mxlist = RuleExeUtil.getDeviceList(pd,
				SystemConstants.MotherLine, null, true, true, true);
		PowerDevice mx = null;
		if (mxlist.size() > 0) {
			mx = mxlist.get(0);
		}
		if (mx != null) {

			// 搜索是否还有其他接地变
			List<PowerDevice> jddzlist = RuleExeUtil.getDeviceList(mx,
					SystemConstants.Switch, null,
					CBSystemConstants.RunTypeSwitchJDB, null, false, false,
					false, false);
			int n = 0;
			//判断相连母线的设备是否存在接地变
			if (jddzlist.size() > 0) {
				int i = 0;
				// 判断接地变的运行数量是否大于1
				for (PowerDevice device : jddzlist) {
					if (device.getDeviceStatus().equals("0")) {
						i++;
					}
					if (i > 1) {
						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
						pdlist.add(mx);
						CheckMessage cm = new CheckMessage();
						cm.setBottom("225");
						cm.setPd(pdlist);
						CBSystemConstants.lcm.add(cm);
						return true;
					}
				}

			} else {
				return true;
			}

		}
		return true;
	}
}
