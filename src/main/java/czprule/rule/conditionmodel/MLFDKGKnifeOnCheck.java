package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import javax.print.attribute.standard.PDLOverrideSupported;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2022-10-09 上午11:20:00   
* 修改人：WJQ   
* 修改备注：   母联/分段开关刀闸合上校核算法
* @version    
*    
*/
public class MLFDKGKnifeOnCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd=rbm.getPd();
		CheckMessage cm=new CheckMessage();
		List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch,CBSystemConstants.RunTypeSwitchML);
		if(swList.size()==0){
			return true;
		}
		List<PowerDevice> pdlist= new ArrayList<PowerDevice>();
		List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.MotherLine);
		List<PowerDevice> ddList = new ArrayList<PowerDevice>();

		//母联开关及地刀
		for(PowerDevice sw : swList){
			if(sw.getDeviceStatus().equals("0")){
				pdlist.add(sw);
			}
			ddList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchFlowGroundLine);
			for(PowerDevice dd : ddList){
				if(dd.getDeviceStatus().equals("0")){
					pdlist.add(dd);
				}
			}
		}
		
		if(mxList.size()>0){
			ddList=RuleExeUtil.getDeviceDirectList(mxList.get(0), SystemConstants.SwitchFlowGroundLine);
			for(PowerDevice dd:ddList){
				if(dd.getDeviceStatus().equals("0")&&!pdlist.contains(dd)){
					pdlist.add(dd);
				}
			}
		}
	
		if(pdlist.size() > 0){
			cm.setPd(pdlist);
			cm.setBottom("1001");
			CBSystemConstants.lcm.add(cm);
		}
		return true;
	}

}
