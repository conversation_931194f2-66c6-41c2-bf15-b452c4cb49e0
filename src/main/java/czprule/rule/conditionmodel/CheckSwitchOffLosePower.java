package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.sun.java.help.search.Rule;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**
 * 开关断开失电算法。 The switch off and lose power arithmetic.
 * 
 * <AUTHOR>
 * 
 */
public class CheckSwitchOffLosePower implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
			return true;
		}
		if (!pd.getDeviceType().equals(SystemConstants.Switch)) {
			return true;
		}
		
		if(rbm.getPd().getDeviceType().equals(SystemConstants.InOutLine)){
			if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelFourCornerLine)){
				List<PowerDevice> swList = isSharedSwitch(pd,rbm.getPd());
				
				if(swList.size()>0){
					CheckMessage cm = new CheckMessage();
					cm.setBottom("251");
					cm.setPd(swList);
					CBSystemConstants.lcm.add(cm);
					return true;
				}
			}
		}
		
		if (RuleExeUtil.isSourceSide(pd) && pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)) {
			List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd,SystemConstants.PowerTransformer, "","", CBSystemConstants.RunTypeSwitchDYC,false, true, true,true);
			if(tfList.size() > 0) {
				if(RuleExeUtil.getTransformerVol(tfList.get(0)).size() == 3)
					return true;
			}
			
		}
		// 判断是否为线路开关
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
//			judgeLine( pd);
			
//			//线路解环提醒
//			List<PowerDevice> xlList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer,
//					true, true, true);
//			if(xlList.size()>0){
//				List<PowerDevice> othersideXL = RuleExeUtil.getLineOtherSideList(xlList.get(0));
//				boolean allyx = true;
//				for(PowerDevice otherxl:othersideXL){
//					List<PowerDevice> swlist = RuleExeUtil.getDeviceList(otherxl, SystemConstants.Switch, SystemConstants.PowerTransformer
//							, true, true, true);
//					for(PowerDevice sw:swlist){
//						if(!sw.getDeviceStatus().equals("0")){
//							allyx = false;
//							break;
//						}
//					}
// 				}
//				if(allyx){
//					CheckMessage cm = new CheckMessage();
//					cm.setBottom("611");
//					cm.setPd(xlList);
//					CBSystemConstants.lcm.add(cm);
//					return true;
//				}
//			}
			
			
			
			
			//关联母线失电提醒
			List<PowerDevice> devList = RuleExeUtil.getDeviceList(pd,
					SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true,
					true);
			List<PowerDevice> lossList = new ArrayList<PowerDevice>();
			for (PowerDevice dev : devList) {
				//设备类型为母线的，判断是否连接其他运行状态母线，如没有，则判断是否连接运行的主变或者线路，有则认为失电会导致影响
					if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
						List<PowerDevice> motherList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
						for(int i=0;i<motherList.size();i++){
							if(!motherList.get(i).getDeviceStatus().equals("0")){
								motherList.remove(i);
								i--;
							}
						}
						if(motherList.size()==0){
							List<PowerDevice> srcList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer+","+SystemConstants.InOutLine, SystemConstants.PowerTransformer
									, false, true, true);
							if(srcList.size()>0){
								lossList.add(dev);
							}
						}
					}else{//双母或3/2接线，所有开关都断开，则母线失电
						List<PowerDevice> swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, false, true, true);
						swList.remove(pd);
						for(int i=0;i<swList.size();i++){
							if(!swList.get(i).getDeviceStatus().equals("0")){
								swList.remove(i);
								i--;
							}
						}
						if(swList.size()==0){
							lossList.add(dev);
						}
					}
				
			}
			if(lossList.size()>0){
				CheckMessage cm = new CheckMessage();
				cm.setBottom("251");
				cm.setPd(lossList);
				CBSystemConstants.lcm.add(cm);
				return true;
			}
	
			
			
		} else {
			// 判断是否电源侧
			if (RuleExeUtil.isSourceSide(pd)) {
				// 查找会失电的负荷
				List<PowerDevice> fhlist = getLossDevice(pd,
						SystemConstants.PowerTransformer + ","
								+ SystemConstants.MotherLine,
						SystemConstants.InOutLine);
				if (fhlist.size() > 0) {
					if(!RuleExeUtil.isTFOnLoad(fhlist.get(0))){
						return true;
					}
					CheckMessage cm = new CheckMessage();
					cm.setBottom("251");
					cm.setPd(fhlist);
					CBSystemConstants.lcm.add(cm);
					return true;
				} else {
					return true;
				}
			} else {
				// 负荷侧开关类型判断
				if(judgeSwitchType(pd)){
					List<PowerDevice> fhlist = getLossDevice(pd,
							SystemConstants.MotherLine,
							SystemConstants.PowerTransformer);
					if (fhlist.size() > 0) {
						CheckMessage cm = new CheckMessage();
						cm.setBottom("251");
						cm.setPd(fhlist);
						CBSystemConstants.lcm.add(cm);
						return true;
					} else {
						return true;
					}
				}else{
					return true;
				}
				
			}
		}
		return true;
	}

	/**
	 * 判断线路
	 * 
	 * @param lcm
	 * @param pd
	 * @return
	 */
	public boolean judgeLine(PowerDevice pd) {
		PowerDevice circuit = null;
		List<PowerDevice> circuits = RuleExeUtil.getDeviceList(pd,
				SystemConstants.InOutLine, null, null,
				CBSystemConstants.RunTypeSideMother, false, true, true, true,
				"0");
		if (circuits.size() == 0) {
			return false;
		} else {
			circuit = circuits.get(0);
		}
		// 获取线路各侧线路
		List<PowerDevice> stationlines = RuleExeUtil.getLineAllSideList(circuit);
		// 判断是否存在对侧线路
		if (stationlines == null || stationlines.isEmpty()
				|| stationlines.size() < 2) {
			return true;
		} else {
			// 判断是进线还是出线
			String jc = RuleExeUtil.getLineFlow(circuit);
			if (jc.equals("1")) {
				List<PowerDevice> fhlist = getLossDevice(pd,
						SystemConstants.PowerTransformer + ","
								+ SystemConstants.MotherLine,
						SystemConstants.InOutLine);
				if (fhlist.size() > 0) {
					CheckMessage cm = new CheckMessage();
					cm.setBottom("251");
					cm.setPd(fhlist);
					CBSystemConstants.lcm.add(cm);
					return true;
				} else {
					return true;
				}
			} 
			
			else if(jc.equals("2")) {
				PowerDevice dcLine = null;
				for (PowerDevice stationline : stationlines) {
					if(stationline.equals(circuit))
						continue;
					PowerDevice sw = RuleExeUtil.getDeviceSwitch(stationline);
					if(sw == null || !sw.getDeviceStatus().equals("0"))
						continue;
					List<PowerDevice> fhlist = getLossDevice(sw,
							SystemConstants.PowerTransformer + ","
									+ SystemConstants.MotherLine,
							SystemConstants.InOutLine);
					if (fhlist.size() > 0) {
						CheckMessage cm = new CheckMessage();
						cm.setBottom("251");
						cm.setPd(fhlist);
						CBSystemConstants.lcm.add(cm);
						return true;	

					}
				}
				return true;
			}
			else {
				return true;
			}
		}

	}

	
	private List<PowerDevice> isSharedSwitch(PowerDevice pd,PowerDevice lineSource) {
		List<PowerDevice> list = new ArrayList<PowerDevice>();
		
		List<PowerDevice> lineList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.Switch, true, true, true);
		List<PowerDevice> lineswList = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

		for(Iterator<PowerDevice> itor = lineList.iterator();itor.hasNext(); ){
			PowerDevice dev = itor.next();
			
			if(dev.getPowerDeviceName().contains("等值负荷")){
				itor.remove();
			}
		}
		
		if(lineList.size() == 2){
			for(PowerDevice line : lineList){
				List<PowerDevice> swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				
				if(swList.size()>0){
					for(PowerDevice sw : swList){
						if(!lineswList.contains(sw)){
							if(!sw.getDeviceStatus().equals("0")){
								list.add(sw);
							}
						}
					}
				}
			}
		}
		
		return list;
	}
	
	/**
	 * 查找会失电的负荷
	 * 
	 * @param pd
	 *            要断开的开关
	 * @param loadDeviceType
	 *            负荷设备类型
	 * @param sourceDeviceType
	 *            电源设备类型
	 * @return
	 */
	public List<PowerDevice> getLossDevice(PowerDevice pd,
			String loadDeviceType, String sourceDeviceType) {
		List<PowerDevice> lossList = new ArrayList<PowerDevice>();
		List<PowerDevice> devList = RuleExeUtil.getDeviceList(pd,
				loadDeviceType, SystemConstants.PowerTransformer, false, false,
				true);
		for (PowerDevice dev : devList) {
			if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&dev.getDeviceStatus().equals("0")){//设备类型为母线的，判断是否连接其他运行状态母线，如没有，则判断是否连接运行的主变或者线路，有则认为失电会导致影响
				if(dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
					List<PowerDevice> motherList = RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
					for(int i=0;i<motherList.size();i++){
						if(!motherList.get(i).getDeviceStatus().equals("0")){
							motherList.remove(i);
							i--;
						}
					}
					if(motherList.size()==0){
						List<PowerDevice> srcList = RuleExeUtil.getDeviceList(dev, SystemConstants.PowerTransformer+","+SystemConstants.InOutLine, SystemConstants.PowerTransformer
								, false, true, true);
						if(srcList.size()>0){
							lossList.add(dev);
						}
					}
				}else{//双母或3/2接线，所有开关都断开，则母线失电
					List<PowerDevice> swList = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer, false, true, true);
					swList.remove(pd);
					for(int i=0;i<swList.size();i++){
						if(!swList.get(i).getDeviceStatus().equals("0")){
							swList.remove(i);
							i--;
						}
					}
					if(swList.size()==0){
						lossList.add(dev);
					}
				}
				
				
			}else{
				if (!RuleExeUtil.isDeviceOnLoad(dev))
					continue;
				List<PowerDevice> srcList = RuleExeUtil.getDeviceList(dev, pd,
						sourceDeviceType, SystemConstants.PowerTransformer, "", "",
						false, false, false, true, true);
				for (Iterator it = srcList.iterator(); it.hasNext();) { // 去掉出线
					PowerDevice src = (PowerDevice) it.next();
					if (src.getDeviceType().equals(SystemConstants.InOutLine)
							&& RuleExeUtil.getLineFlow(src).equals("2"))
						it.remove();
				}
				if (srcList.size() == 0) {
					lossList.add(dev);
				}
			}
			
		}
		return lossList;
	}

	/**
	 * 判断开关类型是否负荷侧主变开关，母联开关，旁路开关，母联兼旁路开关中的一种
	 * 
	 * @param curPd
	 * @return
	 */
	public boolean judgeSwitchType(PowerDevice curPd) {
		String Devicetype = CBSystemConstants.RunTypeSwitchFHC + ","
				+ CBSystemConstants.RunTypeSwitchML + ","
				+ CBSystemConstants.RunTypeSwitchPL + ","
				+ CBSystemConstants.RunTypeSwitchMLPL;
		if (Devicetype.indexOf(curPd.getDeviceRunType()) >= 0) {
			return true;
		}
		return false;

	}
}
