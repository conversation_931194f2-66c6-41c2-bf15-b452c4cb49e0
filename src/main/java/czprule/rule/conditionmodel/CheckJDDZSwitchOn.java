package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**
 * 接地电阻开关合上规则
 * 
 * <AUTHOR>
 * 
 */
public class CheckJDDZSwitchOn implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			return false;
		}
		/**
		 * 识别设备类型判断设备是否是接地电阻 开关
		 */
		if (!pd.getDeviceType().equals(SystemConstants.Switch)) {
			return true;
		}
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB)) {
			// 获取开关连接的母线
			List<PowerDevice> mxlist = RuleExeUtil.getDeviceDirectList(pd,
					SystemConstants.MotherLine);
			for (PowerDevice mx : mxlist) {
				// 搜索是否还有其他接地电阻
				List<PowerDevice> jddzlist = RuleExeUtil.getDeviceList(mx,
						SystemConstants.Switch, null,
						CBSystemConstants.RunTypeSwitchJDB, null, true, false,
						false, false);
				// 母线上是否存在接地电阻
				if (jddzlist.size() > 0) {
					int i = 0;
					// 是否投运
					for (PowerDevice device : jddzlist) {
						if (device.getDeviceStatus().equals("0")) {
							i++;
						}
					}
					if (i > 1) {
						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
						pdlist.add(pd);
						CheckMessage cm = new CheckMessage();
						cm.setPd(pdlist);
						cm.setBottom("274");
						CBSystemConstants.lcm.add(cm);
						return true;

					}
				}
			}
		}
		return true;
	}
}
