package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

public class CheckSwitchLoop implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		if(!pd.getDeviceType().equals(SystemConstants.Switch)){
			return true;
		}
		String runtype=pd.getDeviceRunType();
		//判断是否为线路开关
		if(!runtype.equals(CBSystemConstants.RunTypeSwitchXL)){
			//判断是否为母联或母联兼旁路开关
			if(runtype.equals(CBSystemConstants.RunTypeSwitchML)||runtype.equals(CBSystemConstants.RunTypeSwitchMLPL)){
				List<PowerDevice> mxlist=RuleExeUtil.getDeviceList(pd,null,SystemConstants.MotherLine,null,null,null,false,true,false,true);
				//必须两侧有主变
				if(mxlist.size()<2){
					return true;
				}
				List<List<PowerDevice>> allmx=new ArrayList<List<PowerDevice>>();
				//判断母线连接变压器是否为运行状态
				for(int i=0;i<mxlist.size();i++){
					List<PowerDevice> zblist=RuleExeUtil.getDeviceList(mxlist.get(i),null,SystemConstants.PowerTransformer,null,null,null,false,false,true,true);
					if(zblist.size()>0){
						PowerDevice zb=zblist.get(0);
						if(!zb.getDeviceStatus().equals("0")){
							return true;
						}
						//查找主变联通的母线
						List<PowerDevice> allmxlist=RuleExeUtil.getDeviceList(zb,null,SystemConstants.MotherLine,null,null,null,false,false,true,true);
						if(allmxlist.size()==0){
							return true;
						}
						if(allmxlist.contains(mxlist.get(i))){
							allmxlist.remove(mxlist.get(i));
						}
						allmx.add(allmxlist);
						int nn=0;
					}
				}
				//判断主变其他电压侧是否并列运行
				for(int i=0;i<allmx.size();i++){
					List<PowerDevice> almx=allmx.get(i);
					if(almx.size()>0){
						for(int j=0;j<almx.size();j++){
							PowerDevice amx=almx.get(j);
							for(int n=0;n<allmx.size();n++){
								List<PowerDevice> almxx=allmx.get(n);
								if(almxx.size()>0){
									if(!almxx.equals(almx)){
										for(int m=0;m<almxx.size();m++){
											PowerDevice mxx=almxx.get(m);
											List<PowerDevice> qtmxlist=RuleExeUtil.getDeviceList(amx,null,SystemConstants.MotherLine,null,null,null,false,false,true,true);
											if(qtmxlist.contains(mxx)){
												System.out.println("2710");
												break;
											}
										}
									}
								}
							}
						}
					}
				}
				
				
				//判断母线所在厂站其他侧（高、中、低压侧）母线是否并联（即母联开关是否闭合）
//				List<PowerDevice> mlkglist=RuleExeUtil.getDeviceList(pd,null,SystemConstants.Switch,null,CBSystemConstants.RunTypeSwitchML,null,false,true,false,true);
//				List<PowerDevice> mlplkglist=RuleExeUtil.getDeviceList(pd,null,SystemConstants.Switch,null,CBSystemConstants.RunTypeSwitchMLPL,null,false,true,false,true);
				
				int n=0;

			}else{
				return true;
			}
		}else{
			if(runtype.equals(CBSystemConstants.RunTypeSwitchXL)){
				//查找线路
				PowerDevice circuit=null;
				List<PowerDevice> circuits=RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, null, null, CBSystemConstants.RunTypeSideMother, false, true,true, true,"0");
				if(circuits.size()==0){
					return false;
				}else{
					circuit=circuits.get(0);
				}
				//对侧厂站线路
				PowerDevice otheresidecircuit=null;
				List<PowerDevice> othersidecircuits=RuleExeUtil.getLineOtherSideList(circuit);
				if(othersidecircuits.size()==0){
					return false;
				}else{
					otheresidecircuit=othersidecircuits.get(0);
				}
				//对侧厂站开关
				PowerDevice othersideswith=null;
				List<PowerDevice> othersideswiths=RuleExeUtil.getDeviceList(otheresidecircuit, SystemConstants.Switch, null, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true, "0");
				if(runtype.equals(CBSystemConstants.RunModelCableLine)){
					
				}else{
					if(othersideswiths.size()==0){
						return false;
					}else{
						othersideswith=othersideswiths.get(0);
					}
				}
				//查找线路连接母线
				List<PowerDevice> circuitsmx=RuleExeUtil.getDeviceList(pd,null,SystemConstants.MotherLine,null,null,null,false,false,true,true);
				List<PowerDevice> otheresidecircuitmx=RuleExeUtil.getDeviceList(othersideswith,null,SystemConstants.MotherLine,null,null,null,false,false,true,true);
				if(circuitsmx.size()==0||otheresidecircuitmx.size()==0){
					return true;
				}
				//
				PowerDevice cz=CBSystemConstants.getPowerStation(pd.getPowerStationID());
				PowerDevice dccz=CBSystemConstants.getPowerStation(othersideswith.getPowerStationID());
				//判断一侧是否存在电厂
				if(cz.getDeviceType().equals(SystemConstants.PowerFactory)||dccz.getDeviceType().equals(SystemConstants.PowerFactory)){
					//判断是否都通电
					if(circuitsmx.get(0).getDeviceStatus().equals("0")&&otheresidecircuitmx.get(0).getDeviceStatus().equals("0")){
						System.out.println("277");
					}else{
						return true;
					}
				}else{
					//目前只有双回线
					//查找母线连接的线路
					PowerDevice dclpd=null;
					List<PowerDevice> xllist=RuleExeUtil.getDeviceList(circuitsmx.get(0),null,SystemConstants.InOutLine,null,null,null,false,false,true,true);
					for(int i=0;i<xllist.size();i++){
						PowerDevice lpd=xllist.get(i);
					
						List<PowerDevice> dclpds=RuleExeUtil.getLineOtherSideList(lpd);
						if(othersidecircuits.size()==0){
							continue;
						}else{
							dclpd=dclpds.get(0);
						}
					}
					if(dclpd.getPowerStationID().equals(othersideswith.getPowerStationID())){
						//两侧厂站电压是否一致
						if(cz.getPowerVoltGrade()==dccz.getPowerVoltGrade()){
							System.out.println("276");
						}else{
							System.out.println("2710");
						}
					}
				}
				int nn=0;
			}else{
				return true;
			}
			
		}
		return false;
	}

}
