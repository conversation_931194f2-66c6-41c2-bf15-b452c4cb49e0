package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
/**
 *  禁止带接地合带开关闭锁
 * <AUTHOR>
 *
 */
public class CheckJDSwitchOn  implements RulebaseInf{

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		if (!pd.getDeviceType().equals(SystemConstants.Switch)) {
			return true;
		}
		boolean result = true;
		// 搜索两端直连设备是否有地刀
		List<PowerDevice> sgList = RuleExeUtil.getDeviceDirectList(pd,
				SystemConstants.SwitchFlowGroundLine);
		if (sgList.size() > 0) {
			result = groundknifeswitch(pd, sgList);
		} 
		
//		else {
//			// 是否有接地线
//			List<PowerDevice> jdList = CBSystemConstants
//					.getGroundLineByStationAndDevice(pd.getPowerStationID(), pd
//							.getPowerDeviceID());
//			if (jdList.size() > 0) {	1
//				// List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
//				// pdlist.add(jdList.get(0));
//				CheckMessage cm = new CheckMessage();
//				cm.setBottom("171");
//				cm.setPd(jdList);
//				CBSystemConstants.lcm.add(cm);
//				result = true;
//			} else {
//				result = true;
//			}
//		}
		
		// 搜索开关是否通过线路连接到合上的地刀
		List<PowerDevice> lineList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, true, true);
		if(lineList.size() > 0) {
			for(PowerDevice line : lineList) {
				List<PowerDevice> lineOtherList = RuleExeUtil.getLineOtherSideList(line);
				for(PowerDevice dev : lineOtherList) {
					List<PowerDevice> gdList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
					if(gdList.size() > 0) {
						result = groundknifeswitch(pd,gdList);
					}
				}
			}
		}
		return true;
	}
	/**
	 * 接地刀闸分合情况
	 * 
	 * @param list
	 * @return
	 */
	public boolean groundknifeswitch(PowerDevice pd, List<PowerDevice> list) {
		for (int i = 0; i < list.size(); i++) {
			//是否至少有一个合上
			if (list.get(i).getDeviceStatus().equals("0")) {
				List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
				pdlist.add(list.get(i));
				CheckMessage cm = new CheckMessage();
				cm.setBottom("171");
				cm.setPd(pdlist);
				CBSystemConstants.lcm.add(cm);
				return true;
			}
		}
		return true;
	}
}
