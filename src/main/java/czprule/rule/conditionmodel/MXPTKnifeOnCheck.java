package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import javax.print.attribute.standard.PDLOverrideSupported;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2022-10-09 上午11:20:00   
* 修改人：WJQ   
* 修改备注：   母线PT刀闸合上校核算法
* @version    
*    
*/
public class MXPTKnifeOnCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd=rbm.getPd();
		//判断设备是否是小车
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePT)){
			return true;
		}
		CheckMessage cm=new CheckMessage();
		List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
		List<PowerDevice> ddList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchFlowGroundLine, SystemConstants.MotherLine, true, true, true);

		for(PowerDevice dd : ddList){
			if(dd.getDeviceStatus().equals("0")){
				pdlist.add(dd);
			}
		}
		if(pdlist.size() > 0){
			cm.setPd(pdlist);
			cm.setBottom("1001");
			CBSystemConstants.lcm.add(cm);
		}else{
			return true;
		}
		return true;
	}

}
