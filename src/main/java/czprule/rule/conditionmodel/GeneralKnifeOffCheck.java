package czprule.rule.conditionmodel;

import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

import java.util.ArrayList;
import java.util.List;

/**
*  一般刀闸拉开校核算法
*/
public class GeneralKnifeOffCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null){
			return true;
		}
		PowerDevice pd = rbm.getPd();
		if(pd==null){
			return true;
		}
		//识别设备类型判断设备是否是开关
		if(!pd.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			return true;
		}
		//判断小车则不处理
		if(pd.getPowerDeviceName().contains("小车")||pd.getPowerDeviceName().contains("手车")){
			return true;
		}
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		
		if(CBSystemConstants.lcm.size()>0){
			//除去状态校核之外，还有其他校核错误，则不需再做校验
			if(!(CBSystemConstants.lcm.size()==1&&CBSystemConstants.lcm.get(0).getBottom().equals("301"))){
				return true;
			}
		}	
		CheckMessage cmOn=new CheckMessage();
		CheckMessage cmOff=new CheckMessage();
		List<PowerDevice> pdOnList = new ArrayList<PowerDevice>();
		List<PowerDevice> pdOffList = new ArrayList<PowerDevice>();
		
		List<PowerDevice> swList  =RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);
		if(swList.size()>0){//直接连接开关
			if(swList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				return true;
			}
			
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(swList.get(0), SystemConstants.SwitchSeparate,CBSystemConstants.RunTypeKnifeMX);
			if(swList.get(0).getDeviceStatus().equals("0")){
				pdOnList.add(swList.get(0));
			}
			if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){//如果是母线刀闸
				dzList.remove(pd);
				if(swList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母接线增加判断
					List<PowerDevice> doublemxPdList = new ArrayList<PowerDevice>();
					List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.MotherLine);
					if(mxList.size()>0){
						List<PowerDevice> mlswList= RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,
								CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
						if(mlswList.size()>0){
							if(mlswList.get(0).getDeviceStatus().equals("1")){
								doublemxPdList.add(mlswList.get(0));
							}
							List<PowerDevice> mldzList =RuleExeUtil.getDeviceDirectList(mlswList.get(0), SystemConstants.SwitchSeparate);
							for(PowerDevice mldz:mldzList){
								if(mldz.getDeviceStatus().equals("1")){
									doublemxPdList.add(mldz);
								}
							}
						}

					} 
					if(dzList.size()==1&&dzList.get(0).getDeviceStatus().equals("1")){
						doublemxPdList.add(dzList.get(0));
					}
					if(doublemxPdList.size()==0){
						return true;
					}
				}
				List<PowerDevice> alldzList = RuleExeUtil.getDeviceDirectList(swList.get(0), SystemConstants.SwitchSeparate);
				alldzList.remove(pd);
				for(PowerDevice dz:alldzList){
					if(dz.getDeviceStatus().equals("0")){
						pdOnList.add(dz);
					}
				}
				
			}else{
//				if(dzList.size()==0){//开关搜不到母线刀闸，即搜不到母线（主变开关刀闸）
//				}else 
				if(dzList.size()==1){//开关单母接线
					if(dzList.get(0).getDeviceStatus().equals("1")){
						pdOffList.add(dzList.get(0));
					}
				}else if(dzList.size()==2){//开关双母接线
					if(dzList.get(0).getDeviceStatus().equals("1")&&dzList.get(1).getDeviceStatus().equals("1")){
						pdOffList.addAll(dzList);
					}
				}
			}
		}else{//刀闸直连小车情况，如变低刀闸连主变及变低开关小车
			swList =RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			for(PowerDevice sw:swList){
				if(sw.getDeviceStatus().equals("0")){
					pdOnList.add(sw);
				}
			}
		}
		if(pdOnList.size() > 0){
			cmOn.setPd(pdOnList);
			cmOn.setBottom("1000");
			CBSystemConstants.lcm.add(cmOn);
		}
		if(pdOffList.size() > 0){
			cmOff.setPd(pdOffList);
			cmOff.setBottom("1002");
			CBSystemConstants.lcm.add(cmOff);
		}
		return true;
	}
}

