package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import javax.print.attribute.standard.PDLOverrideSupported;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2022-10-09 上午11:20:00   
* 修改人：WJQ   
* 修改备注：   小车合上校核算法
* @version    
*    
*/
public class XCKnifeOnCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd=rbm.getPd();
		//判断设备是否是小车
		if(!pd.getPowerDeviceName().contains("小车")&&!pd.getPowerDeviceName().contains("手车")){
			return true;
		}
		CheckMessage cm = new CheckMessage();
		CheckMessage cn = new CheckMessage();
		List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
		List<PowerDevice> pd_list= new ArrayList<PowerDevice>();	//离小车最近的地刀
		List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);

		for(PowerDevice sw : swList){
			if(sw.getDeviceStatus().equals("0")){
				pdlist.add(sw);
			}
			List<PowerDevice> ddList = new ArrayList<PowerDevice>();
			List<PowerDevice> zbdzList = RuleExeUtil.getDeviceList(sw, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,
					CBSystemConstants.RunTypeKnifeZB, "", false, true, true, true);
			if(zbdzList.size()>0){//有连接主变刀闸，排除主变刀闸搜地刀
				ddList = RuleExeUtil.getDeviceList(pd, zbdzList.get(0), SystemConstants.SwitchFlowGroundLine, "",
						"", "", false, true, true, true);
				if(ddList.size()>0&&ddList.get(0).getDeviceStatus().equals("0")){
					pdlist.add(ddList.get(0));
				}
			}else{
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(sw, SystemConstants.PowerTransformer, "", true, true, true);
				if(zbList.size()>0){//有连接主变，直接搜主变地刀
					ddList = RuleExeUtil.getDeviceDirectList(zbList.get(0), SystemConstants.SwitchFlowGroundLine);
					
				}else{//不连接主变，根据开关搜地刀
					ddList = RuleExeUtil.getDeviceList(sw, SystemConstants.SwitchFlowGroundLine, "", true, true, true);
				}
				for(PowerDevice dd:ddList){
					if(!dd.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)
							&&dd.getDeviceStatus().equals("0")){
						pdlist.add(dd);
					}
				}
			}
		}
		if(pdlist.size() > 0){
			cm.setPd(pdlist);
			cm.setBottom("1001");
			CBSystemConstants.lcm.add(cm);
		}
	
		return true;
	}

}
