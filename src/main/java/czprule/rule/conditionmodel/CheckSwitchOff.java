package czprule.rule.conditionmodel;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-28 下午04:33:48   
* 修改人：FU   
* 修改备注：   开关拉开防误算法
* @version    
*    
*/
public class CheckSwitchOff implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		
		if(rbm==null){
			return true;
		}
		PowerDevice pd = rbm.getPd();
		if(pd==null){
			return true;
		}
		//识别设备类型判断设备是否是刀闸
		if(!pd.getDeviceType().equals(SystemConstants.Switch)){
			return true;
		}
		//目标状态
		String endstate=rbm.getEndState();
		if(!endstate.equals("0")){
			//返回151拉开关失电闭锁规则111
			if(CBSystemConstants.isRealTime){
				new CheckSwitchOffLosePower().execute(rbm);
			}
			
			//返回152拉主变开关与中性点地刀闭锁
			new CheckZBSwitchZXDChange().execute(rbm);
			//返回153拉开关导致失去接地变闭锁
			new CheckSwitchOffCase().execute(rbm);
			//返回251拉开关失电提醒
			//new CheckSwitchOffWarn().execute(rbm);
			//返回252拉开线路开关操作顺序提醒
			new CheckXLSwitchOffWarn().execute(rbm);
			//返回253拉开主变开关操作顺序提醒
			new CheckZBSwitchDZOffSequence().execute(rbm);
			//返回254拉开主变电源侧开关反送电提醒
			new CheckZBSwitchFSDOff().execute(rbm);
			//返回255拉开3/2接线母线侧开关提醒
			new CheckSwitchOffThreeTwo().execute(rbm);
		}
		return true;
	}

}
