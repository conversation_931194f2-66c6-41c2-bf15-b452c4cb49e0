package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.mainframe.menu.DeviceMenuModel;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
/**
 * 合上关联内桥双母接线 输电线路
 * <AUTHOR>
 *
 */
public class PTransformerOnExecute implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		int flag = 0;
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
			if (pd.getDeviceType().equals(SystemConstants.InOutLine)) { // 为线路操作
				List<PowerDevice> mlList = new ArrayList<PowerDevice>();
				mlList = RuleExeUtil.getDeviceList(pd,
						SystemConstants.MotherLine, null, false, true, true);
				if (mlList.size() == 2) {// 判断是否为内桥双母接线
					if (mlList.get(0).getDeviceRunModel().equals(
							CBSystemConstants.RunModelOneMotherLine)
							&& mlList.get(0).getDeviceRunModel().equals(
									CBSystemConstants.RunModelOneMotherLine)) {
						List<PowerDevice> sList = new ArrayList<PowerDevice>();
						sList = RuleExeUtil.getDeviceList(pd,
								SystemConstants.Switch, null,
								CBSystemConstants.RunTypeSwitchXL, null, false,
								false, true, true);
						if (sList.size() == 2) { // 两枚线路开关
							RuleExecute ruleExecute = new RuleExecute();
							DeviceMenuModel dmm = new DeviceMenuModel();//DeviceMenuModel 是菜单操作时存储信息 （确保设备操作执行对应规则）
							/* 运行对应PT */
							PowerDevice pt = null;
							List<PowerDevice> ptList = new ArrayList<PowerDevice>();
							ptList = RuleExeUtil.getDeviceList(mlList.get(0),
									SystemConstants.VolsbTransformer, null,
									true, true, true);
							if (ptList.size() > 0) {
								pt = ptList.get(0);
								if (!pt.getDeviceStatus().equals("0")
										&& pt != null) { // PT如不在运行状态则执行 
									
									dmm.setPd(pt);
									dmm.setOperatecode("0");
									dmm.setParentcode("0");
									dmm
											.setStatecode("3a6348a3-601e-46de-8dda-c003a761b383");// PT运行
									dmm.setStatename("运行");
									dmm.setStateorder("0");
									dmm.setStatetype("0");
									dmm.setStatevalue("0");
									rbm = getRBM(pt, dmm);
									if (!ruleExecute.execute(rbm)) { // 执行运行PT操作规则
										return false;
									}
								}
							} 
							/* 运行对应主变 */
							PowerDevice ptf = null;
							List<PowerDevice> pList = new ArrayList<PowerDevice>();
							pList = RuleExeUtil.getDeviceList(pd,
									SystemConstants.PowerTransformer, null,
									true, false, true);
							if (pList.size() > 0) {
								ptf = pList.get(0);
								if (ptf.getDeviceStatus().equals("0")
										&& ptf != null) { // 主变在运行状态则返回
									return true;
								}
							} else {
								return true;
							}
							{
								dmm.setPd(ptf);
								dmm.setOperatecode("0");
								dmm.setParentcode("0");
								dmm
										.setStatecode("ef474f34-fd18-4a62-a4ec-f15f380cc5f2");// 主变冷备运行
								dmm.setStatename("运行");
								dmm.setStateorder("0");
								dmm.setStatetype("0");
								dmm.setStatevalue("0");
							}

							/*
							 * DeviceOperate dev = new DeviceOperate();
							 * dev.execute(ptf, dmm);
							 */
							rbm = getRBM(ptf, dmm);
							if (!ruleExecute.execute(rbm)) { // 执行操作规则
								return false;
							}
							
						}
					}
				}
			}

		return true;
	}

	/**
	 * 生成RuleBaseMode
	 * 
	 * @param pd
	 * @param dmm
	 * @return RuleBaseMode
	 */
	public RuleBaseMode getRBM(PowerDevice pd, DeviceMenuModel dmm) {
		RuleBaseMode rbm = new RuleBaseMode();
		rbm.setPd(pd);
		String beginStatus = pd.getDeviceStatus();
		rbm.setBeginStatus(beginStatus);
		rbm.setEndState(dmm.getStatevalue());
		rbm.setStateCode(dmm.getStatecode());
		return rbm;
	}
}
