package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.mainframe.menu.DeviceMenuModel;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.dao.DeviceStateMentManager;

/**
 * 断开关联内桥双母接线 输电线路
 * 
 * <AUTHOR>
 * 
 */
public class PTransformerOffExecute implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		int flag = 0;
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		flag = Integer.valueOf(rbm.getBeginStatus()); // 判断操作类型
		if (flag == 0) {
			if (pd.getDeviceType().equals(SystemConstants.InOutLine)) { // 为线路操作
				List<PowerDevice> mlList = new ArrayList<PowerDevice>();
				mlList = RuleExeUtil.getDeviceList(pd,
						SystemConstants.MotherLine, null, true, true, true);
				if (mlList.size() == 2) {// 判断是否为内桥双母接线
					if (mlList.get(0).getDeviceRunModel().equals(
							CBSystemConstants.RunModelOneMotherLine)
							&& mlList.get(0).getDeviceRunModel().equals(
									CBSystemConstants.RunModelOneMotherLine)) {
						List<PowerDevice> sList = new ArrayList<PowerDevice>();
						sList =RuleExeUtil.getDeviceList(pd,
								SystemConstants.Switch, null,
								CBSystemConstants.RunTypeSwitchXL, null, false,
								true, true, true);
						if (sList.size() == 2) { // 综上述条件+两枚线路开关 即为符合条件设备操作
							RuleExecute ruleExecute = new RuleExecute();
							DeviceMenuModel dmm = new DeviceMenuModel();//DeviceMenuModel 是菜单操作时存储信息 （确保设备操作执行对应规则）
							/* 冷备对应PT */
							PowerDevice pt = null;
							List<PowerDevice> ptList = new ArrayList<PowerDevice>();
							ptList = RuleExeUtil.getDeviceList(mlList.get(0),
									SystemConstants.VolsbTransformer, null,
									true, true, true);
							if (ptList.size() > 0) {
								pt = ptList.get(0);
								if (pt.getDeviceStatus().equals("0")
										&& pt != null) { // PT如在运行状态则执行 
									
									dmm.setPd(pt);
									dmm.setOperatecode("2");
									dmm.setParentcode("0");
									dmm
											.setStatecode("6d718735-5278-4733-b6b8-c631a428894b");// PT冷备
									dmm.setStatename("冷备用");
									dmm.setStateorder("2");
									dmm.setStatetype("0");
									dmm.setStatevalue("2");
									rbm = getRBM(pt, dmm);
									if (!ruleExecute.execute(rbm)) { // 执行冷备PT操作规则
										return false;
									}
								}
							} 
							/* 冷备对应主变 */
							PowerDevice ptf = null;
							List<PowerDevice> pList = new ArrayList<PowerDevice>();
							pList = RuleExeUtil.getDeviceList(pd,
									SystemConstants.PowerTransformer, null,
									false, false, true);
							if (pList.size() > 0) {
								ptf = pList.get(0);
								if (!ptf.getDeviceStatus().equals("0")
										&& ptf != null) { // 主变不在运行状态则返回
									return true;
								}
							} else {
								return true;
							}
							
							
							{
								dmm.setPd(ptf);
								dmm.setOperatecode("2");
								dmm.setParentcode("0");
								dmm
										.setStatecode("f20307c6-edbf-495e-8a83-983d8238a823");// 主变冷备
								dmm.setStatename("冷备用");
								dmm.setStateorder("3");
								dmm.setStatetype("0");
								dmm.setStatevalue("2");
							}

							/*
							 * DeviceOperate dev = new DeviceOperate();
							 * dev.execute(ptf, dmm);
							 */
							rbm = getRBM(ptf, dmm);
							if (!ruleExecute.execute(rbm)) { // 执行变压器操作规则
								return false;
							}
						}
					}
				}
			}
		}
		return true;
	}

	/**
	 * 生成RuleBaseMode
	 * 
	 * @param pd
	 * @param dmm
	 * @return RuleBaseMode
	 */
	public RuleBaseMode getRBM(PowerDevice pd, DeviceMenuModel dmm) {
		RuleBaseMode rbm = new RuleBaseMode();
		rbm.setPd(pd);
		String beginStatus = pd.getDeviceStatus();
		rbm.setBeginStatus(beginStatus);
		rbm.setEndState(dmm.getStatevalue());
		rbm.setStateCode(dmm.getStatecode());
		return rbm;
	}

}
