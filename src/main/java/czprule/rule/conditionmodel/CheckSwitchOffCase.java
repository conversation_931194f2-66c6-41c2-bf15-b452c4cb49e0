package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**
 * 拉开开关导致失去接地变闭锁
 * 
 * <AUTHOR>
 * 
 */
public class CheckSwitchOffCase implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		// 识别设备类型判断设备是否是接地电阻开关
		if (!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB)) {
			// 识别设备类型判断设备是否是母联开关
			if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
				// 开关两侧母线依次排除当前母联搜索其他电源点
				List<PowerDevice> mxl = RuleExeUtil.getDeviceDirectByPortList(
						pd, SystemConstants.MotherLine, "0");
				for (PowerDevice mx : mxl) {
					// 该母线是否存在电源点
					List<PowerDevice> bdList = RuleExeUtil.getDeviceDirectList(
							mx, CBSystemConstants.RunTypeSwitchJDB);
					if (bdList.size() > 0) {
						// 母线上是否可以找到投运的接地电阻
						List<PowerDevice> jdList = RuleExeUtil
								.getDeviceDirectList(mx,
										CBSystemConstants.RunTypeSwitchJDB);
						for (PowerDevice jd : jdList) {
							if (jd.getDeviceStatus().equals("0"))
								return true;
						}
						CheckMessage cm = new CheckMessage();
						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
						pdlist.add(pd);
						cm.setBottom("153");
						cm.setPd(pdlist);
						CBSystemConstants.lcm .add(cm);
						return true;
					}
				}
			}
			return true;
		}
//		// 搜索与开关相连最近的母线
//		List<PowerDevice> mxl = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
//		PowerDevice mx = mxl.get(0);
//		// 母线上是否可以找到其他投运的接地电阻
//		List<PowerDevice> jdList = RuleExeUtil.getDeviceList(mx, pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchJDB, null, false, false, false, true);
//		if (jdList.size() == 0) {
//			CheckMessage cm = new CheckMessage();
//			List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
//			pdlist.add(mx);
//			cm.setBottom("153");
//			cm.setPd(pdlist);
//			CBSystemConstants.lcm .add(cm);
//			return true;
//		}
		return true;
	}

}
