package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import javax.print.attribute.standard.PDLOverrideSupported;

import com.sun.java.help.search.Rule;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2022-10-09 上午11:20:00   
* 修改人：HZ   
* 修改备注：   线路无开关刀闸合上校核算法
* @version    
*    
*/
public class XLKnifeWithoutSwitchOnCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd=rbm.getPd();
		//判断设备是否是内桥主变刀闸
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZBS)){
			return true;
		}
		CheckMessage cm=new CheckMessage();
		List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
		
		List<PowerDevice> xlList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.InOutLine);
		if(xlList.size()>0){//线路其他侧各侧开关需要断开，本侧及其他各侧地刀需要拉开
			List<PowerDevice> ddList = RuleExeUtil.getDeviceDirectList(xlList.get(0), SystemConstants.SwitchFlowGroundLine);
			for(PowerDevice dd:ddList){
				if(dd.getDeviceStatus().equals("0")){
					pdlist.add(dd);
				}
			}
			List<PowerDevice> otherxlList = RuleExeUtil.getLineOtherSideList(pd);
			for(PowerDevice otherxl:otherxlList){
				List<PowerDevice> swList = RuleExeUtil.getDeviceList(otherxl, SystemConstants.Switch, SystemConstants.PowerTransformer,
						false, true, true);
				for(PowerDevice sw:swList){
					if(sw.getDeviceStatus().equals("0")&&!pdlist.contains(sw)){
						pdlist.add(sw);
					}
				}
				ddList = RuleExeUtil.getDeviceDirectList(otherxl, SystemConstants.SwitchFlowGroundLine);
				for(PowerDevice dd:ddList){
					if(dd.getDeviceStatus().equals("0")&&!pdlist.contains(dd)){
						pdlist.add(dd);
					}
				}
			}
		}
	
		if(pdlist.size() > 0){
			cm.setPd(pdlist);
			cm.setBottom("1001");
			CBSystemConstants.lcm.add(cm);
		}
		return true;
	}

}
