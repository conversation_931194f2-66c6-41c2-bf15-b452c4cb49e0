package czprule.rule.conditionmodel;

import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * 主变复电开关合上校核算法
 */
public class ZBRecoverPowerSwitchOnCheck implements RulebaseInf {

    private String[] bottom = new java.lang.String[]{"1215"};//T_A_RULE_ECHO-echoId

    @Override
    public boolean execute(RuleBaseMode rbm) {
        if (rbm == null) {
            return true;
        }
        PowerDevice currentPD = rbm.getPd();
        if (currentPD == null) {
            return true;
        }
        if (!currentPD.getDeviceType().equals(SystemConstants.Switch)) {
            //设备类型是开关，才做校验
            return true;
        }
        
        if(!rbm.getBeginStatus().equals("0")&&!rbm.getEndState().equals("0")){//只校核开关合上或者断开操作
     	   return true;
        }

        
        if (CBSystemConstants.lcm == null) {
            CBSystemConstants.lcm = new ArrayList<CheckMessage>();
        }
        ZBLossPowerSwitchOffCheck zbLossPowerSwitchOffCheck = new ZBLossPowerSwitchOffCheck();
        zbLossPowerSwitchOffCheck.setBottom(bottom);
        zbLossPowerSwitchOffCheck.zbRecoverOrLossPower(currentPD);
        return true;
    }

}
