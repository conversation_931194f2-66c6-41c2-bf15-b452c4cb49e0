package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-16 下午04:29:52   
* 修改人：FU   
* 修改备注：   中低压主变开关拉开顺序判断算法
* @version    
*    
*/
public class CheckZBSwitchDZOffSequence implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd = rbm.getPd();
		/*
		//查找低于当前开关电压等级的各侧主变开关
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, "", true, true, true);
		if(tfList.size() == 0)
			return true;
		PowerDevice tf = tfList.get(0);
		PowerDevice zyc=null;
		PowerDevice dyc=null;
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(tf, SystemConstants.Switch, "", "", CBSystemConstants.RunTypeSideMother+","+CBSystemConstants.RunTypeSwitchML, false, true, false, true);
		for (Iterator<PowerDevice> it = swList.iterator(); it.hasNext();) {
			PowerDevice sw = it.next();
			//20141214如果存在与当前开关电压等级相同且在合位的开关，则退出算法
			if(!sw.equals(pd) && sw.getPowerVoltGrade() == pd.getPowerVoltGrade()){
				return true;
			}
			if(sw.equals(pd) || sw.getPowerVoltGrade() >= pd.getPowerVoltGrade())
				it.remove();
			if(zyc==null){
				zyc=sw;
			}else{
				dyc=sw;
			}
			if(zyc!=null&&dyc!=null){
				if(zyc.getPowerVoltGrade()<dyc.getPowerVoltGrade()){
					PowerDevice temp=null;
					temp=dyc;
					dyc=zyc;
					zyc=temp;
				}
			}
		}
		//判断查找到的开关是否为空
		if(swList.size() == 0)
			return true;
		//判断查找到的开关是否都在拉开状态
		boolean isAllOff = true;
		for(PowerDevice sw : swList) {
			if(sw.getDeviceStatus().equals("0")) {
				isAllOff = false;
				break;
			}
		}
		if(isAllOff)
			return true;
		//判断是否电源侧主变开关
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)) {//判断其他主变开关所属的电压等级是否是两种或以上的电压等级，如果是，则是反充电，可以拉开，否则不可以拉开
			return true;
		}
		else {
			CheckMessage cm=new CheckMessage();
			List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
			pdlist.add(dyc);
			cm.setPd(pdlist);
			//先拉开负荷侧开关
			cm.setBottom("253");
			CBSystemConstants.lcm.add(cm);
			return true;
		}
		*/
		//20141217 测试发现重庆监控的主变停电操作更改中低压侧开关操作顺序后，校验提醒有误
		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
			return true;
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, "", true, true, true);
		if(tfList.size() == 0)
			return true;
		PowerDevice tf = tfList.get(0);
		if(RuleExeUtil.getDeviceList(tf, pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, ""
				, "", false, true, true, true).size()>0){//内桥无母线情况有其他供电线路不提示
			return true;
		}
		List<List<PowerDevice>> swListList = new ArrayList<List<PowerDevice>>();
		
		
		swListList.add(RuleExeUtil.getTransformerSwitchHigh(tf)); //主变高压侧开关
		swListList.add(RuleExeUtil.getTransformerSwitchMiddle(tf)); //主变中压侧开关
		swListList.add(RuleExeUtil.getTransformerSwitchLow(tf)); //主变低压侧开关
		
		boolean isOff = true; //是否断开开关后本侧会停电
		for(List<PowerDevice> swList : swListList) {
			if(swList.contains(pd)) {
				if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)) {
					for(PowerDevice sw : swList) {
						if(!sw.equals(pd) && sw.getDeviceStatus().equals("0")) { //如果是3/2接线且存在其他合上的开关，断开本开关不会导致本侧停电
							isOff = false;
							break;
						}
					}
				}
			}
		}
		if(!isOff)
			return true;
		
		boolean isLower = false; //是否电压等级更低的开关
		for(List<PowerDevice> swList : swListList) {
			if(swList.contains(pd)) {
				isLower = true;
				continue;
			}
			if(isLower) {
				boolean isExistOff = true;
				for(PowerDevice sw : swList) {
					if(!sw.getDeviceStatus().equals("0")) {
						isExistOff = true;
						break;
					}
					else
						isExistOff = false;
				}
				if(!isExistOff) {
					CheckMessage cm=new CheckMessage();
					List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
					pdlist.add(swList.get(0));
					cm.setPd(pdlist);
					//先拉开负荷侧开关
					cm.setBottom("253");
					CBSystemConstants.lcm.add(cm);
					return true;
				}
			}
		}
		
		return true;
	}
}
