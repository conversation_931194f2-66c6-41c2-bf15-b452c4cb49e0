package czprule.rule.conditionmodel;

import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

import java.util.ArrayList;
import java.util.List;

/**
*  一般刀闸合上校核算法
*/
public class GeneralKnifeOnCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null){
			return true;
		}
		PowerDevice pd = rbm.getPd();
		if(pd==null){
			return true;
		}
		//识别设备类型判断设备是否是开关
		if(!pd.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			return true;
		}
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if(CBSystemConstants.lcm.size()>0){
			//除去状态校核之外，还有其他校核错误，则不需再做校验
			if(!(CBSystemConstants.lcm.size()==1&&CBSystemConstants.lcm.get(0).getBottom().equals("301"))){
				return true;
			}
		}
		CheckMessage cmOn=new CheckMessage();
		CheckMessage cmOff=new CheckMessage();
		List<PowerDevice> pdOnList = new ArrayList<PowerDevice>();
		List<PowerDevice> pdOffList = new ArrayList<PowerDevice>();
		
		List<PowerDevice> swList  =RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);
		if(swList.size()>0){//直接连接开关
			if(swList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				return true;
			}
			else if(swList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
				List<PowerDevice> xlList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.InOutLine);
				if(xlList.size()>0){
					List<PowerDevice> allxlList = RuleExeUtil.getLineAllSideList(xlList.get(0));
					for(PowerDevice xl:allxlList){
						List<PowerDevice> xlddList = RuleExeUtil.getDeviceDirectList(xl, SystemConstants.SwitchFlowGroundLine);
						for(PowerDevice xldd:xlddList){
							if(xldd.getDeviceStatus().equals("0")
									&&!pdOnList.contains(xldd)){
								pdOnList.add(xldd);
							}
						}
					}
				}
			}else if(swList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)||swList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
				List<PowerDevice> zbList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
				if(zbList.size()>0){
					List<PowerDevice> zbddList = RuleExeUtil.getDeviceDirectList(zbList.get(0), SystemConstants.SwitchFlowGroundLine);
					for(PowerDevice zbdd:zbddList){
						if(zbdd.getDeviceStatus().equals("0")&&!zbdd.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)
								&&!pdOnList.contains(zbdd)){
							pdOnList.add(zbdd);
						}
					}
				}
			}
			
			
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(swList.get(0), SystemConstants.SwitchSeparate,CBSystemConstants.RunTypeKnifeMX);
			if(swList.get(0).getDeviceStatus().equals("0")){
				pdOnList.add(swList.get(0));
			}
			if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){//如果是母线刀闸
				dzList.remove(pd);
				if(swList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//双母接线增加判断
					List<PowerDevice> doublemxPdList = new ArrayList<PowerDevice>();
					List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.MotherLine);
					if(mxList.size()>0){
						List<PowerDevice> mlswList= RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer,
								CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
						if(mlswList.size()>0){
							if(mlswList.get(0).getDeviceStatus().equals("1")){
								doublemxPdList.add(mlswList.get(0));
							}
							List<PowerDevice> mldzList =RuleExeUtil.getDeviceDirectList(mlswList.get(0), SystemConstants.SwitchSeparate);
							for(PowerDevice mldz:mldzList){
								if(mldz.getDeviceStatus().equals("1")){
									doublemxPdList.add(mldz);
								}
							}
						}

					} 
					if(dzList.size()==1&&dzList.get(0).getDeviceStatus().equals("1")){
						doublemxPdList.add(dzList.get(0));
					}
					if(doublemxPdList.size()==0){
						return true;
					}
				}
				List<PowerDevice> alldzList = RuleExeUtil.getDeviceDirectList(swList.get(0), SystemConstants.SwitchSeparate);
				alldzList.remove(pd);
				for(PowerDevice dz:alldzList){
					if(dz.getDeviceStatus().equals("0")){
						pdOnList.add(dz);
					}
				}
				
			}else{
//				if(dzList.size()==0){//开关搜不到母线刀闸，即搜不到母线（主变开关刀闸）
//				}else 
				if(dzList.size()==1){//开关单母接线
					if(dzList.get(0).getDeviceStatus().equals("1")){
						pdOffList.add(dzList.get(0));
					}
				}else if(dzList.size()==2){//开关双母接线
					if(dzList.get(0).getDeviceStatus().equals("1")&&dzList.get(1).getDeviceStatus().equals("1")){
						pdOffList.addAll(dzList);
					}
				}
			}
			
			//开关相连地刀
			List<PowerDevice> swddList = RuleExeUtil.getDeviceDirectList(swList.get(0), SystemConstants.SwitchFlowGroundLine);
			for(PowerDevice swdd:swddList){
				if(swdd.getDeviceStatus().equals("0")&&!pdOnList.contains(swdd)){
					pdOnList.add(swdd);
				}
			}
			
		}else{//刀闸直连小车情况，如变低刀闸连主变及变低开关小车
			if(RuleExeUtil.getDeviceDirectList(pd, SystemConstants.InOutLine).size()==0){//排除直连线路情况（线路PT刀闸，部分模型识别不到PT，所以不能用类型判断）
				swList =RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				for(PowerDevice sw:swList){
					if(sw.getDeviceStatus().equals("0")){
						pdOnList.add(sw);
					}
					List<PowerDevice> swddList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchFlowGroundLine);
					for(PowerDevice swdd:swddList){
						if(swdd.getDeviceStatus().equals("0")&&!pdOnList.contains(swdd)){
							pdOnList.add(swdd);
						}
					}
				}
			}
			
		}
		
		
		
		//刀闸直接连接主变，主变地刀不能在合位
		List<PowerDevice> zbList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.PowerTransformer);
		if(zbList.size()>0){
			List<PowerDevice> zbddList = RuleExeUtil.getDeviceDirectList(zbList.get(0), SystemConstants.SwitchFlowGroundLine);
			for(PowerDevice zbdd:zbddList){
				if(zbdd.getDeviceStatus().equals("0")&&!zbdd.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)
						&&!pdOnList.contains(zbdd)){
					pdOnList.add(zbdd);
				}
			}
		}
		
		//刀闸直接连接，母线直连地刀不能在合位
		List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.MotherLine);
		if(mxList.size()>0){
			List<PowerDevice> mxddList = RuleExeUtil.getDeviceDirectList(mxList.get(0), SystemConstants.SwitchFlowGroundLine);
			for(PowerDevice mxdd:mxddList){
				if(mxdd.getDeviceStatus().equals("0")
						&&!pdOnList.contains(mxdd)){
					pdOnList.add(mxdd);
				}
			}
		}
		
		
		
		//所有刀闸通用,直接连接的地刀不能在合位（包括母线PT刀闸、线路PT刀闸）
		List<PowerDevice> ddList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchFlowGroundLine);
		for(PowerDevice dd:ddList){
			if(dd.getDeviceStatus().equals("0")&&!pdOnList.contains(dd)){
				pdOnList.add(dd);
			}
		}
		
		
		
		
		
		if(pdOnList.size() > 0){
			cmOn.setPd(pdOnList);
			cmOn.setBottom("1001");
			CBSystemConstants.lcm.add(cmOn);
		}
		if(pdOffList.size() > 0){
			cmOff.setPd(pdOffList);
			cmOff.setBottom("1003");
			CBSystemConstants.lcm.add(cmOff);
		}
		return true;
	}
}

