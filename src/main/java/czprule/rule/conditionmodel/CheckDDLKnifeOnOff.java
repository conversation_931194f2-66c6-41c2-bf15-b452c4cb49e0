package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-16 上午11:23:45   
* 修改人：FU   
* 修改备注：   刀闸拉合大电流空载设备规则（备用算法待定）
* @version    
*    
*/
public class CheckDDLKnifeOnOff implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd = rbm.getPd();
		if (!pd.getPowerDeviceID().equals(
				CBSystemConstants.getParentDev().getPowerDeviceID())) {
			return true;
		}
		List<PowerDevice> pds = RuleUtil.getDirectDevice(pd);
		List<PowerDevice> devList = new ArrayList<PowerDevice>();

		int count = 0;
		for (PowerDevice dev : pds) {
			// 两侧无断路器
			if (dev.getDeviceType().equals(SystemConstants.Switch)) {
				return true;
			}
			
			if (dev.getDeviceType().equals(SystemConstants.InOutLine) || 
					dev.getDeviceType().equals(SystemConstants.PowerTransformer)) {
				
				//查找刀闸操作前设备连接到的母线
				List<PowerDevice> list1 = RuleExeUtil.getDeviceList(dev, null, SystemConstants.MotherLine, SystemConstants.PowerTransformer, null, null, false, false, false, true);
				//查找刀闸操作后设备连接到的母线
				pd.setDeviceStatus(rbm.getEndState());
				List<PowerDevice> list2 = RuleExeUtil.getDeviceList(dev, null, SystemConstants.MotherLine, SystemConstants.PowerTransformer, null, null, false, false, false, true);
				pd.setDeviceStatus(rbm.getBeginStatus());
				//判断刀闸操作后带电状态是否发生变化
				if(list1.size() == 0 && list2.size() > 0 && list2.get(0).getDeviceStatus().equals("0"))
					devList.add(dev);
				else if(list1.size() > 0 && list2.size() == 0 && list1.get(0).getDeviceStatus().equals("0"))
					devList.add(dev);
			}
			
			// 在运行的主变 有一个主变开关在运行的就加入计数
//			if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)
//					&& dev.getDeviceStatus().equals("0")) {
//				List<PowerDevice> list = RuleUtil.getLinkedDeviceByType1(dev,
//						SystemConstants.Switch);
//				for (PowerDevice sw : list) {
//					if (sw.getDeviceStatus().equals("0")) {
//						if(!devList.contains(dev))
//							devList.add(dev);
//						count++;
//						break;
//					}
//				}
//			}
//			if (dev.getDeviceType().equals(SystemConstants.InOutLine)) {
//				if (dev.getDeviceStatus().equals("0")) {
//					if(!devList.contains(dev))
//						devList.add(dev);
//					count++;
//				}
//			}
		}
		// 存在线路或主变在运行 不允许拉刀闸
		if (devList.size() > 0) {
			CheckMessage cm=new CheckMessage();
			cm.setPd(devList);
			//该刀闸两侧无断路器，不能直接拉合
			if(rbm.getEndState().equals("0"))
				cm.setBottom("132");
			else
				cm.setBottom("112");
			CBSystemConstants.lcm.add(cm);
		}
		return true;
	}

}
