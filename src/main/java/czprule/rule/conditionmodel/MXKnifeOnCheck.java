package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import javax.print.attribute.standard.PDLOverrideSupported;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2022-10-09 上午11:20:00   
* 修改人：WJQ   
* 修改备注：   母线刀闸拉开校核算法
* @version    
*    
*/
public class MXKnifeOnCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd=rbm.getPd();
		//判断设备是否是母线刀闸
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
			return true;
		}
		CheckMessage cm=new CheckMessage();
		List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);
		//排除掉母联开关
		if(swList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
			return true;
		}
		List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
		List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.MotherLine);
		List<PowerDevice> dzList = new ArrayList<PowerDevice>();
		List<PowerDevice> ddList = new ArrayList<PowerDevice>();
		PowerDevice mx = mxList.get(0);
		List<PowerDevice> mxddList = RuleExeUtil.getDeviceDirectList(mx, SystemConstants.SwitchFlowGroundLine);
		//通用条件
		for(PowerDevice mxdd : mxddList){
			if(mxdd.getDeviceStatus().equals("0")){
				pdlist.add(mxdd);
			}
		}
		for(PowerDevice sw : swList){
			ddList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchFlowGroundLine);
			if(sw.getDeviceStatus().equals("0")){
				pdlist.add(sw);
			}
			for(PowerDevice dd : ddList){
				if(dd.getDeviceStatus().equals("0")){
					pdlist.add(dd);
				}
			}
		}
		
		//单母接线
		if(mx.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
			dzList = RuleExeUtil.getDeviceDirectList(swList.get(0), SystemConstants.SwitchSeparate, CBSystemConstants.RunTypeKnifeDY);
			for(PowerDevice dz : dzList){
				if(dz.getDeviceStatus().equals("0")){
					pdlist.add(dz);
				}
			}
		}
		//双母接线
		else if(mx.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
			boolean temp = false;
			PowerDevice dydz = null;	//母线对侧刀闸
			PowerDevice mxdz = null;	//另一个母线刀闸
			for(PowerDevice sw : swList){
				dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);
				dzList.remove(pd);
				for(PowerDevice dz : dzList){
					if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeDY)){
						dydz = dz;
					}else if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
						mxdz = dz;
					}
				}
				//判断是否满足第一个条件
				if(!sw.getDeviceStatus().equals("1")){
					if(dydz.getDeviceStatus().equals("1") && mxdz.getDeviceStatus().equals("1")){
						temp = true;
					}
				}
			}
			//第一个条件不满足，判断是否满足第二个条件
			if(!temp){
				List<PowerDevice> mlswList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
				for(PowerDevice ml : mlswList){
					if(RuleExeUtil.isSwitchDoubleML(ml)){
						if(ml.getDeviceStatus().equals("0") && mxdz.getDeviceStatus().equals("0")){
							temp = true;
						}
					}
				}
			}
			//两个条件均不满足
			if(!temp){
				for(PowerDevice sw : swList){
					if(sw.getDeviceStatus().equals("0")){
						pdlist.add(sw);
					}
				}
				for(PowerDevice dz : dzList){
					if(dz.getDeviceStatus().equals("0")){
						pdlist.add(dz);
					}
				}
			}
		}
		if(pdlist.size() > 0){
			cm.setPd(pdlist);
			cm.setBottom("1001");
			CBSystemConstants.lcm.add(cm);
		}else{
			return true;
		}
		return true;
	}

}
