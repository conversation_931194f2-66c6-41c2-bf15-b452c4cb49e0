/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 判断开关拉开是否符合五防规则
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.conditionmodel;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;

public class JudgeSwitchToOffPublic implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		
//		//判断开关是否为中间的开关
//		PowerDevice pd = rbm.getPd();
//		if(CBSystemConstants.getSourceDev().getDeviceType().equals(SystemConstants.InOutLine)) {
//			if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)) {
//				boolean b=RuleExeUtil.isSwMiddleInThreeSecond(pd);
//				if(!b){
//					List<PowerDevice> sws = RuleUtil.getLinkedDeviceByType3(pd, SystemConstants.Switch);
//					for (PowerDevice sw : sws) {
//						if(sw.getDeviceStatus().equals("0")){
//							rbm.getMessageList().add("中间["+sw+"]在合位，不能拉开母线侧["+pd+"]");
//							return false;
//						}
//					}
//				}
//			}
//		}
		return true;
	}
}
