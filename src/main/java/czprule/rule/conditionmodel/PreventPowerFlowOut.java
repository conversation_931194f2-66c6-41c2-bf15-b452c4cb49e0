package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.ShowMessage;

/**
 * Prevent power flow out of range. 防止潮流越限。
 * 
 * <AUTHOR>
 * 
 */
public class PreventPowerFlowOut implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (rbm.getMessage().get("cl") == null) {
			rbm.getMessage().put("cl", new ArrayList<CheckMessage>());
		}
		List<CheckMessage> lcm = rbm.getMessage().get("cl");
		if (rbm == null) {
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			CheckMessage cm = new CheckMessage();
			cm.setMessage("当前操作设备为空");
			lcm.add(cm);
			return true;
		}
		/*
		 * 需要某些方法获取到当前潮流信息 已经当前设备潮流上线 通过计算预计操作后的潮流 （暂无数据）
		 */
		// max power flow
		int maxpl = 2;
		// current
		int curpl = 0;
		// future
		int futpl = 1;
		if (maxpl - futpl >= 0) {
			return true;
		} else {
			CheckMessage cm = new CheckMessage();
//			cm.setPd(pd);
			cm.setBottom("1");
			cm.setMessage("当前操作将导致" + pd.getPowerDeviceName() + "断电");
			lcm.add(cm);
			return true;
		}
	}

}
