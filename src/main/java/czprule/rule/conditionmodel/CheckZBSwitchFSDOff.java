package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-17 上午09:36:28   
* 修改人：FU   
* 修改备注：   主变开关拉开反充电规则
* @version    
*    
*/
public class CheckZBSwitchFSDOff implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd = rbm.getPd();
		/*
		//判断当前开关是否是电源侧开关
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
			return true;
		}
		//查找低于当前开关电压等级的各侧主变开关
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, "", true, true, true);
		if(tfList.size() == 0){
			return true;
		}
		PowerDevice tf = tfList.get(0);
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(tf, SystemConstants.Switch, "", "", CBSystemConstants.RunTypeSideMother+","+CBSystemConstants.RunTypeSwitchML, false, true, false, true);
		for (Iterator<PowerDevice> it = swList.iterator(); it.hasNext();) {
			PowerDevice sw = it.next();
			//20141214如果存在与当前开关电压等级相同且在合位的开关，则退出算法
			if(!sw.equals(pd) && sw.getPowerVoltGrade() == pd.getPowerVoltGrade()){
				return true;
			}
			if(sw.equals(pd) || sw.getPowerVoltGrade() >= pd.getPowerVoltGrade()){
				it.remove();
				continue;
			}
		}
		//判断查找到的开关是否为空
		if(swList.size() == 0)
			return true;
		//判断查找到的开关是否都在拉开状态
		boolean isAllOff = true;
		for(PowerDevice sw : swList) {
			if(!sw.getDeviceStatus().equals("0")) {
				isAllOff = false;
				break;
			}
		}
		if(isAllOff)
			return true;
		
		RuleExeUtil.swapDeviceListNum(swList);
		
		PowerDevice fhc=swList.get(0);
		if(fhc.getDeviceStatus().equals("0")){
			CheckMessage cm=new CheckMessage();
			List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
			pdlist.add(fhc);
			cm.setPd(pdlist);
			//当前操作将导致反充电
			cm.setBottom("254");
			CBSystemConstants.lcm.add(cm);
			return true;
		}
		*/
		//20141217 测试发现重庆监控的主变停电操作更改高压侧开关操作后，校验提醒有误
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, "", true, true, true);
		if(tfList.size() == 0)
			return true;
		PowerDevice tf = tfList.get(0);
		List<PowerDevice> swHighList = RuleExeUtil.getTransformerSwitchHigh(tf); //主变高压侧开关
		if(swHighList.contains(pd)
				&&RuleExeUtil.getDeviceList(tf, pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, ""
						, "", false, true, true, true).size()==0) {
			boolean isHighOff = true; //断开是否会导致高压侧停电
			if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)) {
				for(PowerDevice sw : swHighList) {
					if(!sw.equals(pd) && sw.getDeviceStatus().equals("0")) { //如果是3/2接线且存在其他合上的开关，断开本开关不会导致高压侧停电
						isHighOff = false;
						break;
					}
				}
			}
			if(isHighOff) {
				List<PowerDevice> swLowList = RuleExeUtil.getTransformerSwitchLow(tf); //主变低压侧开关
				for(PowerDevice sw : swLowList) {
					if(sw.getDeviceStatus().equals("0")) { //如果存在合上的低压侧开关，需要给出操作提醒
						
						List<PowerDevice> swMiddleList = RuleExeUtil.getTransformerSwitchMiddle(tf); //主变中压侧开关
						for(PowerDevice sw2 : swMiddleList) {
							if(sw2.getDeviceStatus().equals("0")) { //如果存在合上的中压侧开关，需要给出操作提醒
								CheckMessage cm=new CheckMessage();
								List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
								pdlist.add(sw);
								cm.setPd(pdlist);
								//当前操作将导致反充电
								cm.setBottom("254");
								CBSystemConstants.lcm.add(cm);
								return true;
							}
						}
					}
				}
			}
		}
		return true;
	}

}
