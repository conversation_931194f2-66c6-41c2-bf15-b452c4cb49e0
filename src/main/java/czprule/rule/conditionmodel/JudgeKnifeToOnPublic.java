/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 刀闸开合条件判断
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.baserule.SupplyElecAlgorithm;
import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprulepw.PWSystemConstants;

/**
 * 功能说明 : 判断刀闸是否可以合闸 功能描述 : 搜索当前刀闸送电范围内是否存在接地刀闸且处于合位， 若存在有任意一把接地刀闸处于合位则不允许合当前刀闸，
 * 或者直接连接的设备处于检修不允许和刀闸
 * 
 * @参数1(Map)：输入参数 设备（powerDevice）
 * @参数2(Map): 输出参数 返回0或1,0代表可以合闸，1代表不能合闸
 * <AUTHOR>
 * 
 */
public class JudgeKnifeToOnPublic implements RulebaseInf {

	@SuppressWarnings("unchecked")
	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		if (!pd.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
			rbm.getMessageList().add(
					"刀闸合闸公用算法输入判定对象[" + pd.getPowerDeviceName() + "]非刀闸！");
			return false;
		}
		if (pd.getDeviceStatus().equals("0")&&CBSystemConstants.roleCode.equals("0")) {
			// 已经处于合闸位置
			return true;
		}
		
		if(CBSystemConstants.lcm == null){
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		
		if(CBSystemConstants.roleCode.equals("0")){
			if (!knifeOnOrderCheck(rbm) || !knifeWithSwitch(rbm) || !knifeWithOtherDevice(rbm) || !hotLoad(rbm) || !coldLoad(rbm)
					 || !sideKnifeOn(rbm) || !sideKnifeOnJudgeSwitch(rbm))
				return false;
		}

		SupplyElecAlgorithm sea = new SupplyElecAlgorithm();
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		PowerDevice pDevice = null;

		if(CBSystemConstants.roleCode.equals("1")&&CBSystemConstants.usePwRole&&pd.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			List<PowerDevice> li = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, null, PWSystemConstants.PWRunTypeSwitchZX, null, false, true, false, true ,"1");
        	if(li.size()>0){
        		List<PowerDevice> tanrs = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, null, null, null, false, true, false, true ,"2");
        		
        		if(tanrs.size()>0){
        			li = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, null, PWSystemConstants.PWRunTypeSwitchZX, null, false, true, false, true);
                	if(li.size()>0){
                		List<PowerDevice> list = new ArrayList<PowerDevice>();
                		
                		List<PowerDevice> pathlist = RuleExeUtil.getPathByDevice(pd, li.get(0), "","", true, true);
                 		
                		for(PowerDevice dev : pathlist){
                			if(dev.getDeviceType().equals(SystemConstants.Switch)){
                				list.add(dev);
                			}
                		}
                		
                		if(list.size()>0){
                			boolean flag = true;
                			for(PowerDevice dev:list){
                				if(!dev.getDeviceStatus().equals("0")
                						&&!dev.getDeviceRunType().equals(PWSystemConstants.PWRunTypeSwitchZX)){
                					
                					flag = false;
                				}
                			}
                			
                			if(flag){
                				CheckMessage cm = new CheckMessage();
                        		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
                				pdlist.add(pd);
                				cm.setPd(pdlist);
                				cm.setBottom("103");
                				CBSystemConstants.lcm.add(cm);
                				return true;
                			}
                		}
                	}
        		}
        	}else{
        		 li = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, null, PWSystemConstants.PWRunTypeSwitchZX, null, false, true, false, false ,"2");
        		 if(li.size()>0){
        				List<PowerDevice> tanrs = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, null, null, null, false, true, false, true ,"1");
        				
        				if(tanrs.size()>0){
                			li = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, null, PWSystemConstants.PWRunTypeSwitchZX, null, false, true, false, true);
                        	if(li.size()>0){
                        		List<PowerDevice> list = new ArrayList<PowerDevice>();
                        		
                        		List<PowerDevice> pathlist = RuleExeUtil.getPathByDevice(pd, li.get(0), "","", true, true);
                         		
                        		for(PowerDevice dev : pathlist){
                        			if(dev.getDeviceType().equals(SystemConstants.Switch)){
                        				list.add(dev);
                        			}
                        		}
                        		
                        		if(list.size()>0){
                        			boolean flag = true;
                        			for(PowerDevice dev:list){
                        				if(!dev.getDeviceStatus().equals("0")
                        						&&!dev.getDeviceRunType().equals(PWSystemConstants.PWRunTypeSwitchZX)){
                        					
                        					flag = false;
                        				}
                        			}
                        			
                        			if(flag){
                        				CheckMessage cm = new CheckMessage();
                                		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
                        				pdlist.add(pd);
                        				cm.setPd(pdlist);
                        				cm.setBottom("103");
                        				CBSystemConstants.lcm.add(cm);
                        				return true;
                        			}
                        		}
                        	}
                		}
        		 }
        	}
		}
		
		
		// 一、 送电范围内不能存在合闸的接地刀闸
		List<PowerDevice> devs = sea.execute(pd);
		for (int i = 0; i < devs.size(); i++) {
			pDevice = devs.get(i);
			if (pDevice.getDeviceStatus().equals("0")) {
				// 当前范围内有接地刀闸和接地线路，先判断接地刀闸和接地线路是否处于合位，
				// 如果任意一个处于合位则不允许合闸，如果没有则再判断直连设备状态
				rbm.getMessageList().add(
						"[" + pDevice.getPowerStationName() + pDevice.getPowerDeviceName() + "]处于合位，["
								+ pd.getPowerStationName()
								+ pd.getPowerDeviceName() + "]不能合闸！");
				return false;
			}
		}
		// 二、直接连接的设备处于检修也不允许合闸

		inPara.put("oprSrcDevice", pd);
		inPara.put("isSearchDirectDevice", true);// 搜索直接相连的设备
		Map<Integer, PowerDevice> curDevs = CBSystemConstants
				.getCurOperateDevs();
		int size = curDevs.size();
		List<PowerDevice> excDevs = new ArrayList<PowerDevice>();
		for (int i = 1; i <= size; i++) {
			excDevs.add(curDevs.get(i));
		}
		inPara.put("excDevList", excDevs);
		cs.execute(inPara, outPara);
		devs = (ArrayList<PowerDevice>) outPara.get("linkedDeviceList");
		for (int i = 0; i < devs.size(); i++) {
			pDevice = (PowerDevice) devs.get(i);
			if (pDevice.getDeviceStatus().equals("3")) {
				rbm.getMessageList().add(
						"[" + pDevice.getPowerDeviceName() + "]处于检修，["
								+ pd.getPowerStationName()
								+ pd.getPowerDeviceName() + "]不能合闸！");
				return false;
			}
		}

		return true;
	}

	/**
	 * 刀闸与设备间的闭锁：电网正常时，220KV及以下刀闸可以拉、合电压互感器、避雷器等设备(热倒合除外)
	 * @param rbm
	 * @return
	 */
	private boolean knifeWithOtherDevice(RuleBaseMode rbm) {
		PowerDevice pd = rbm.getPd();
		List<PowerDevice> listOtherDevice = null;
		List<PowerDevice> listPTandArrester = null;
		List<PowerDevice> listSwitch = null;
		listPTandArrester = RuleExeUtil.getDeviceDirectList(pd, "Arrester,PT,BusbarSection");
		listSwitch = RuleExeUtil.getDeviceDirectList(pd, "Breaker,Disconnector,GroundDisconnector");
		List<PowerDevice> listBreaker = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		listOtherDevice = RuleExeUtil.getDeviceDirectList(pd, "");
		
		for(PowerDevice switchTemp : listBreaker){
			if(!switchTemp.getDeviceStatus().equals("0"))
				return true;
		}
		
		if(listPTandArrester != null && listPTandArrester.size() > 0)//如果存在避雷器和电压互感器则可以拉、合刀闸，返回true
			return true;
		else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)) //旁路刀闸连接的设备在运行时可以合上
			return true;
		else {
			if(listOtherDevice != null && listOtherDevice.size() > 0){
				for(PowerDevice switchTemp : listOtherDevice){
					if(listPTandArrester.contains(switchTemp) || listSwitch.contains(switchTemp))
						continue;
					if(switchTemp.getDeviceStatus().equals("0")){//如果连接的设备有处于合位的，则不可以操作刀闸，返回false
						rbm.getMessageList().add(
								"["+pd.getPowerStationName()+"]"+"[" + switchTemp + "]在运行不能合上[" + pd + "]");
						return false;
					}
				}
				return true;
			}
		}
		return true;
	}

	/**
	 * 刀闸与开关闭锁规则，如果开关处于合位，则不允许拉开和合上开关两侧的刀闸(热倒合除外)
	 * @param rbm
	 * @return
	 */
	private boolean knifeWithSwitch(RuleBaseMode rbm) {
		PowerDevice pd = rbm.getPd();
		List<PowerDevice> listSwitch = null;
		listSwitch = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);//获取刀闸连接的开关
		if(listSwitch == null || listSwitch.size() == 0)//如果直接连接的设备中没有开关则返回true
			return true;
		else{
			PowerDevice switchTemp = listSwitch.get(0);
			if(!switchTemp.getDeviceStatus().equals("0"))//如果开关在分位则返回true
				return true;
			else{
				if(switchTemp.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))//如果是双母接线方式则返回true(热倒合判断)，如果不是则返回false
					return true;
				else{
					List<PowerDevice> mxList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					for(int i=0;i<mxList.size();i++){
						if(mxList.get(i).getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
							mxList.remove(i);
							i--;
						}
					}
					if(mxList.size()<2){
						rbm.getMessageList().add(
								"[" + switchTemp + "]在合位不能合上[" + pd + "]");
						return false;
					}else{
						return true;
					}
					
				}
					
					
			}
		}
	}
	
	/**
	 * 刀闸合上顺序闭锁：合上开关两侧刀闸时，应先合电源侧，后合负荷侧。
	 * 
	 * @param rbm
	 * @return
	 */
	private boolean knifeOnOrderCheck(RuleBaseMode rbm) {
		if(CBSystemConstants.isMaxRangeOffTicket)
			return true;
		
		PowerDevice knife = rbm.getPd();
		if(knife.getPowerDeviceName().contains("小车"))
			return true;
		if(knife.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeDY)) {
			// 获取连接的开关
			List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(knife, SystemConstants.Switch);
			if (list.size() > 0) {
				PowerDevice sw = (PowerDevice) list.get(0);
				if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
					return true;
				if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)|| sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL))
					return true;
				List<PowerDevice> kfList1 = RuleExeUtil.getDeviceDirectByPortList(sw, SystemConstants.SwitchSeparate, "1");
				List<PowerDevice> kfList2 = RuleExeUtil.getDeviceDirectByPortList(sw, SystemConstants.SwitchSeparate, "2");
				List<PowerDevice> kfList = null;
				if(kfList1.contains(knife))
					kfList = kfList2;
				else
					kfList = kfList1;
				if(kfList.size() == 0)
					return true;
				boolean isExistOn = false;
				for(PowerDevice kf : kfList) {
					if(!kf.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX) || kf.getDeviceStatus().equals("0")) {
						isExistOn = true;
						break;
					}
				}
				if(!isExistOn) {
					rbm.getMessageList().add(
							"[" + knife.getPowerDeviceName() + "]" + "处在负荷侧,"
									+ "处在电源侧的刀闸未合上");
					return false;
				}
			}
		}
		return true;
	}

	/**
	 * 热倒合原则：间隔母线侧刀闸有一把在合位，另一个母线侧刀闸只有在母联及母联两侧刀闸在合位，才能合闸。
	 * 
	 * @param rbm
	 * @return
	 */
	private boolean hotLoad(RuleBaseMode rbm) {
		PowerDevice pd = rbm.getPd();
		List<PowerDevice> list = null;
		// 非母线侧刀闸规则结束
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX))
			return true;
		// 获取刀闸连接的母线
		list = RuleUtil.getDirectDevice(pd, SystemConstants.MotherLine);
		if (list == null || list.size() == 0) {
			return true;
		}
		PowerDevice motherLine = (PowerDevice) list.get(0);
		// 判断是否双母接线，若不是双母规则结束
		if (!motherLine.getDeviceRunModel().equals(
				CBSystemConstants.RunModelDoubleMotherLine))
			return true;
		// 获取连接的开关
		list = RuleUtil.getDirectDevice(pd, SystemConstants.Switch);
		if (list == null || list.size() == 0) {
			return true;
		}
		PowerDevice directSwitch = (PowerDevice) list.get(0);
		// 判断开关是否在分位，若在分位规则结束
		if (!directSwitch.getDeviceStatus().equals("0"))
			return true;
		// 获取另一母线侧刀闸
		PowerDevice otherSideKnife = RuleExeUtil.getKnifeOtherML(pd);
		if (otherSideKnife == null) {
			return true;
		}
		// 判断另一母线侧刀闸是否在分位 ，若在分位规则结束
		if (!otherSideKnife.getDeviceStatus().equals("0")) // 另一母线侧刀闸在分位
			return true;
		// 获取母线的母联开关或母联刀闸，若在分位提示操作闭锁
		PowerDevice mlOther = RuleExeUtil.getMLDoubleOther(motherLine);
		PowerDevice mlDev = null;
		if(mlOther != null)
			mlDev = RuleExeUtil.getMLSwitch(pd, mlOther);
		if (mlDev == null)
			return true;
		else if (mlDev.getDeviceStatus().equals("0"))
			return true;
		else {
			rbm.getMessageList().add(
					"[" + mlDev.getPowerDeviceName() + "]处于分位，["
							+ pd.getPowerStationName()
							+ pd.getPowerDeviceName() + "]不能合闸！");
			return false;
		}
	}

	/**
	 * 冷倒合原则：间隔母线侧两刀闸一把在合位另一把在分位、开关在分位，此时倒母只能先将在合位的母刀闸拉开，再合另一把母刀闸；若先合上在分位的母刀闸，
	 * 应闭锁。
	 * 
	 * @param rbm
	 * @return
	 */
	private boolean coldLoad(RuleBaseMode rbm) {
		PowerDevice pd = rbm.getPd();
		List<PowerDevice> list = null;
		// 非母线侧刀闸规则结束
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX))
			return true;
		// 获取刀闸连接的母线
		list = RuleUtil.getDirectDevice(pd, SystemConstants.MotherLine);
		if (list == null || list.size() == 0) {
			return true;
		}
		PowerDevice motherLine = (PowerDevice) list.get(0);
		// 判断是否双母接线，若不是双母规则结束
		if (!motherLine.getDeviceRunModel().equals(
				CBSystemConstants.RunModelDoubleMotherLine))
			return true;
		// 获取连接的开关
		list = RuleUtil.getDirectDevice(pd, SystemConstants.Switch);
		if (list == null || list.size() == 0) {
			return true;
		}
		PowerDevice directSwitch = (PowerDevice) list.get(0);
		// 判断开关是否在合位，若在合位规则结束
		if (directSwitch.getDeviceStatus().equals("0"))
			return true;
		// 获取另一母线侧刀闸
		PowerDevice otherSideKnife = RuleExeUtil.getKnifeOtherML(pd);
		if (otherSideKnife == null) {
			return true;
		}
		// 判断另一母线侧刀闸是否在合位 ，若在合位提示操作闭锁
		if (otherSideKnife.getDeviceStatus().equals("0")) { //
			rbm.getMessageList().add(
					"[" + otherSideKnife.getPowerDeviceName() + "]处于合位，["
							+ pd.getPowerStationName()
							+ pd.getPowerDeviceName() + "]不能合闸！");
			return false;
		} else
			return true;

	}

	/**
	 * 旁路刀闸操作规则：旁路开关必须在分位，才允许两个旁路刀闸同时在合位。否则合上旁路刀闸时，若已经有一个旁路刀闸在合位应闭锁。
	 * */
	private boolean sideKnifeOn(RuleBaseMode rbm) {
		PowerDevice pd = rbm.getPd();
		PowerDevice motherLine = RuleUtil.getDirectMotherLine(pd);
		PowerDevice publicswitch;
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)) {
			publicswitch = RuleUtil.getSideSwitch(motherLine);
			if (publicswitch == null) {
				return true;
			}
			// 旁路开关在合位
			if (publicswitch.getDeviceStatus().equals("0")) {
				// 若操作刀闸与旁路开关直接相连 则为公共刀闸 与其他旁路刀闸都有闭锁
				if (RuleUtil.isConnected(publicswitch, pd)) {
					List<PowerDevice> otherDisc = RuleUtil.getDirectDevice(
							motherLine, SystemConstants.SwitchSeparate);
					otherDisc.remove(pd);
					for (PowerDevice dis : otherDisc) {
						if (dis.getDeviceStatus().equals("0")) {
							rbm.getMessageList().add(
									"[" + dis + "]在合位不能合上[" + pd + "]");
							return false;
						}
					}
				} else {
					// 不是公共刀闸则查找公共刀闸并与之闭锁
					List<PowerDevice> publicknife = RuleUtil.getDirectDevice(
							publicswitch, SystemConstants.SwitchSeparate);
					for (PowerDevice knife : publicknife) {
						if (knife.getDeviceRunType().equals(
								CBSystemConstants.RunTypeKnifePL)) {
							if (knife.getDeviceStatus().equals("0")) {
								rbm.getMessageList().add(
										"[" + knife + "]在合位不能合上[" + pd + "]");
								return false;
							}
						}
					}
				}
			}
		}
		return true;
	}

	/**
	 * 旁路刀闸操作规则：合上旁路刀闸时，若此时没有其它的旁路刀闸在合位，旁路开关必须在分位，否则闭锁；拉开该旁路刀闸时，旁路开关也必须在分位，否则闭锁。
	 * */
	private boolean sideKnifeOnJudgeSwitch(RuleBaseMode rbm) {
		PowerDevice pd = rbm.getPd();
		PowerDevice motherLine = RuleUtil.getDirectMotherLine(pd);
		PowerDevice publicswitch;
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)) {

			List<PowerDevice> otherDisc = RuleUtil.getDirectDevice(motherLine,
					SystemConstants.SwitchSeparate);
			otherDisc.remove(pd);
			for (PowerDevice dis : otherDisc) {
				if (dis.getDeviceRunType().equals(
						CBSystemConstants.RunTypeKnifePL)
						&& dis.getDeviceStatus().equals("0")) {
					return true;
				}
			}
			publicswitch = RuleUtil.getSideSwitch(motherLine);
			if (publicswitch == null) {
				return true;
			}
			if (publicswitch.getDeviceStatus().equals("0")) {
				rbm.getMessageList().add(
						"没有其它的旁路刀闸在合位，不能合上[" + pd.getPowerDeviceName() + "]");
				return false;
			}

		}
		return true;
	}

	/**
	 * 判断刀闸是否处在电源侧：根据刀闸连接的开关类型分：主变开关、旁路开关、母联开关、线路开关、母联兼旁路开关和其他开关五种类型
	 * 返回true：电源侧；false：负荷侧
	 * @param rbm
	 * @return
	 */
	public boolean knifeIsPower(RuleBaseMode rbm) {
		PowerDevice pd = rbm.getPd();
		String result = RuleExeUtil.JudgeknifeIsPowerSide(pd);
		if("1".equals(result) || "9".equals(result))
			return true;
		else
			return false;
	}


	/**
	 * 判断操作刀闸所属开关的另一侧刀闸的开合状态，合上刀闸时当且仅当刀闸处于负荷侧时才会进入此方法验证处于电源侧的另一道闸是否处于合位，
	 * 如果不处于合位则不可以合上负荷侧的刀闸
	 * @param rbm
	 * @return
	 */
	public boolean isOtherKnifeOn(RuleBaseMode rbm) {
		
		PowerDevice knifePd = rbm.getPd();
		List<PowerDevice> list = null;
		// 获取连接的开关
		list = RuleUtil.getDirectDevice(knifePd, SystemConstants.Switch);
		if (list == null || list.size() == 0) {
			return true;
		}
		PowerDevice switchPd = (PowerDevice) list.get(0);
		
		List<PowerDevice> devs = RuleExeUtil.getDeviceDirectList(switchPd,
				knifePd.getDeviceType());// 通过开关查找直接连接的设备
		if (devs.contains(knifePd))// 排除当前操作的设备
			devs.remove(knifePd);
		List<PowerDevice> otherKnifeList = new ArrayList<PowerDevice>();
		for (int i = 0; i < devs.size(); i++)
			if (!devs.get(i).getDeviceRunType()
					.equals(CBSystemConstants.RunTypeGroundKnife)) {// 排除接地刀闸
				List<PowerDevice> linkedKnifeList = RuleUtil.getDirectDevice(
						devs.get(i), devs.get(i).getDeviceType());
				if (!linkedKnifeList.contains(knifePd))
					otherKnifeList.add(devs.get(i));// 获取另一个刀闸
			}

		int temp = 0;
		StringBuffer deviceNameStr = new StringBuffer("");
		for (PowerDevice otherKnife : otherKnifeList)
			if (!otherKnife.getDeviceStatus().equals("0")) {// 判断另一个刀闸是否在分位，如果是则返回false，不可以合上
				deviceNameStr.append(otherKnife.getPowerDeviceName() + ",");
				temp++;
			}
		if (deviceNameStr.length() > 0)
			deviceNameStr.deleteCharAt(deviceNameStr.length() - 1);
		if (temp == otherKnifeList.size() && temp != 0) {
			rbm.getMessageList().add(
					"[" + knifePd.getPowerDeviceName() + "]" + "处在负荷侧,"
							+ "处在电源侧的[" + deviceNameStr + "]未合上");
			return false;
		} else
			return true;
	}

}
