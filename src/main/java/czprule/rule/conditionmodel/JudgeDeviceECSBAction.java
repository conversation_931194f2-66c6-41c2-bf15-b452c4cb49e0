package czprule.rule.conditionmodel;



import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.ShowMessage;


/**   
*    
* 创建时间：2019-4-30 上午09:39:40   
* 修改人：Yuanw  
* 修改备注：  二次设备公用算法
* @version    
*    
*/
public class JudgeDeviceECSBAction implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null) {
			return false;
		}
		PowerDevice pd=rbm.getPd();
		//设备
		String endstate=pd.getDeviceStatus();
		if(endstate.equals("2")||endstate.equals("3")){
			ShowMessage.view("一次设备已停电，无法进行二次操作");
			return false;
		}
		return true;
	}
	
}
