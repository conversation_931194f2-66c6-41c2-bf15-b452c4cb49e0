/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 判断开关拉开是否会导致负荷失电
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;


public class JudgeSwitchToOffByLoad implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {

		PowerDevice pd=rbm.getPd();
		List<PowerDevice> lossList = new ArrayList<PowerDevice>();
		//线路开关 根据线路潮流判断进线开关判断失电，出现开关不需要判断。
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) { 
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, true, true);
			for(PowerDevice line : lineList) {
				List<PowerDevice> lineOtherList = RuleExeUtil.getLineOtherSideList(line);
				if(lineOtherList.size() == 0) {
					rbm.getInfoList().add("断开["+pd.getPowerDeviceName()+"]会导致["+CZPService.getService().getDevName(line)+"]失电"); //失电提醒
				}
				else {
					String flow = RuleExeUtil.getLineFlow(line);
					if(flow.equals("2")) {
						for(PowerDevice lineOther : lineOtherList) {
							if(lineOther.getDeviceStatus().equals("0")) {
								rbm.getMessageList().add("断开["+pd+"]会导致["+CZPService.getService().getDevName(lineOther)+"]失电");
								return false;
							}
						}
					}
					else
						lossList = getLossDevice(pd, SystemConstants.PowerTransformer+","+SystemConstants.MotherLine, SystemConstants.InOutLine);
				}
			}
			
		}
		else if(RuleExeUtil.isSourceSide(pd)) { //电源侧的开关电源点是线路
			lossList = getLossDevice(pd, SystemConstants.PowerTransformer+","+SystemConstants.MotherLine, SystemConstants.InOutLine);
		}
		else { //负荷侧的开关电源点是主变
			if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC) || 
					pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) ||
					pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL) ||
					pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)) {
				lossList = getLossDevice(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer);
			}
		}
		if(lossList.size() > 0){
			RuleExeUtil.swapDeviceList(lossList);
			rbm.getMessageList().add("断开["+pd+"]会导致["+CZPService.getService().getDevName(lossList)+"]失电");
			return false;
		}
		else
			return true;

	}
	
	/**
	 * 查找会失电的负荷
	 * @param pd 要断开的开关
	 * @param loadDeviceType 负荷设备类型
	 * @param sourceDeviceType 电源设备类型
	 * @return
	 */
	public List<PowerDevice> getLossDevice(PowerDevice pd, String loadDeviceType, String sourceDeviceType) {
		List<PowerDevice> lossList = new ArrayList<PowerDevice>();
		List<PowerDevice> devList = RuleExeUtil.getDeviceList(pd, loadDeviceType, SystemConstants.PowerTransformer, false, false, true);
		for(PowerDevice dev : devList) {
			
			if(!RuleExeUtil.isDeviceOnLoad(dev))
				continue;
			List<PowerDevice> srcList = RuleExeUtil.getDeviceList(dev, pd, sourceDeviceType, SystemConstants.PowerTransformer, "", "", false, false, false, true, true);
			for(Iterator it = srcList.iterator(); it.hasNext();) { //去掉出线
				PowerDevice src = (PowerDevice)it.next();
				if(src.getDeviceType().equals(SystemConstants.InOutLine) && 
						RuleExeUtil.getLineFlow(src).equals("2"))
					it.remove();
			}
			if(srcList.size() == 0) {
				lossList.add(dev);
			}
		}
		return lossList;
	}
}
