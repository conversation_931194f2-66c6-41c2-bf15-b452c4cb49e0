package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.algorithm.baserule.SupplyElecAlgorithm;
import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprulepw.PWSystemConstants;

/**
 * 接地刀闸合闸判断
 * 
 * <AUTHOR>
 * 
 */
public class JDKnifeCheckOn implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {

		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			return false;
		}
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD))
			return true;
		if (StringUtils.compareStr(pd.getDeviceType(),
				SystemConstants.SwitchFlowGroundLine) == 1) {
			return true;
		}
		// 当前接地刀闸作用范围内没有刀闸时，搜索其他连接设备
		CheckMessage cm = new CheckMessage();
		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
		List<PowerDevice> zbList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.PowerTransformer);
		List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);
		List<PowerDevice> lineList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.InOutLine);
		List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.MotherLine);
		//主变地刀
		if(zbList.size() > 0){
			for(PowerDevice zb : zbList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(zb, SystemConstants.SwitchSeparate);
				for(PowerDevice dz : dzList){
					if(dz.getDeviceStatus().equals("0")){
						if((dz.getPowerDeviceName().contains("小车")||dz.getPowerDeviceName().contains("手车"))&&dz.getPowerDeviceName().endsWith("1")){
							List<PowerDevice> xcswList = RuleExeUtil.getDeviceDirectList(dz, SystemConstants.Switch);
							if(xcswList.size()>0){
								List<PowerDevice> xcdzList = RuleExeUtil.getDeviceDirectList(xcswList.get(0), SystemConstants.SwitchSeparate);
								for(PowerDevice xcdz:xcdzList){
									if(!xcdz.equals(dz)&&xcdz.getDeviceStatus().equals("0")){
										pdlist.add(xcdz);
									}
								}
							}
						}else{
							pdlist.add(dz);
						}
					
					}
				}
			}
		}
		//开关地刀
		else if(swList.size() > 0){
			for(PowerDevice sw : swList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);
				for(PowerDevice dz : dzList){
					if(dz.getDeviceStatus().equals("0")){
						pdlist.add(dz);
					}
				}
			}
		}
		//线路地刀
		else if(lineList.size() > 0){
			PowerDevice line = lineList.get(0);
			List<PowerDevice> ptdzList = RuleExeUtil.getDeviceDirectList(line, SystemConstants.SwitchSeparate, CBSystemConstants.RunTypeKnifePT);
			CheckMessage cmpt = new CheckMessage();
			List<PowerDevice> pdptlist = new ArrayList<PowerDevice>();
			for(PowerDevice ptdz:ptdzList){
				if(ptdz.getDeviceStatus().equals("1")){
					pdptlist.add(ptdz);
				}
			}
			if(pdptlist.size()>0){
				cmpt.setPd(pdptlist);
				cmpt.setBottom("1301");
				CBSystemConstants.lcm.add(cmpt);
			}
			List<PowerDevice> allLineList = RuleExeUtil.getLineAllSideList(line);
			for(PowerDevice allLine : allLineList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(allLine, SystemConstants.SwitchSeparate);
				for(PowerDevice dz : dzList){
					if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeDY) && dz.getDeviceStatus().equals("0")){
						pdlist.add(dz);
					}
				}
			}
		}
		//母线地刀
		else if(mxList.size() > 0){
			for(PowerDevice mx : mxList){
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(mx, SystemConstants.SwitchSeparate);
				for(PowerDevice dz : dzList){
					if(dz.getDeviceStatus().equals("0")){
						pdlist.add(dz);
					}
				}
			}
		}
		//其他地刀
		else{
			List<PowerDevice> zdList = new ArrayList<PowerDevice>();
			List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate);
			List<PowerDevice> kgList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			for(PowerDevice kg : kgList){
				zdList = RuleExeUtil.getDeviceDirectList(kg, SystemConstants.SwitchSeparate);
			}
			dzList.removeAll(zdList);
			dzList.addAll(zdList);
			for(PowerDevice dz : dzList){
				if((dz.getPowerDeviceName().contains("小车")||dz.getPowerDeviceName().contains("手车"))&&dz.getPowerDeviceName().endsWith("1")){
					continue;
				}
				if(dz.getDeviceStatus().equals("0")){
					pdlist.add(dz);
				}
			}
		}
		if (pdlist.size() > 0) {
			cm.setPd(pdlist);
			cm.setBottom("101");
			CBSystemConstants.lcm.add(cm);
			return true;
		}
		return true;
	}

}
