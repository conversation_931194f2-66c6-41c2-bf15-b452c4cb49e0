package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;

/**
 * 主变开关拉合与中性点刀闸规则
 * 
 * <AUTHOR>
 * 
 */
public class CheckZBSwitchZXDChange implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(!CBSystemConstants.isCurrentSys&&!CBSystemConstants.isRealTime){
			return true;
		}
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd = rbm.getPd();

		if(!RuleExeUtil.isSourceSide(pd)){
//			if(pd.getPowerVoltGrade() >= 110){
//				List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd,SystemConstants.PowerTransformer,SystemConstants.PowerTransformer, true, true, true);
//
//				for(PowerDevice dev : tfList){
//					List<PowerDevice> knifes = RuleExeUtil.getDeviceList(dev,SystemConstants.SwitchFlowGroundLine,null, CBSystemConstants.RunTypeGroundZXDDD, null,true, true, true, true);
//					// 是否存在中性点刀闸
//					for (PowerDevice kinfe : knifes) {
//						if (!kinfe.getDeviceStatus().equals("0")&&kinfe.getPowerVoltGrade() == pd.getPowerVoltGrade()) {
//							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
//							pdlist.add(kinfe);
//							CheckMessage cm = new CheckMessage();
//							cm.setBottom("152");
//							cm.setPd(pdlist);
//							cm.setDeviceName(CZPService.getService().getDevName(pd));
//							CBSystemConstants.lcm.add(cm);
//							return true;
//						}
//					}
//				}
//			} 20241112zc修改
			return true;
		}else{
			// 搜索开关连接主变
			List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd,SystemConstants.PowerTransformer,SystemConstants.PowerTransformer, null, null,false, false, false, true);

			for(PowerDevice tf: tfList) {
				if(tf.getPowerVoltGrade() <= 35)
					continue;
				// 主变是否有其他电源
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(tf, pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, null, null, false, false, false, true, false);
				if(lineList.size() > 0)
					continue;
				// 搜索开关连接线圈中性点刀闸
				List<PowerDevice> knifes = RuleExeUtil.getDeviceList(tf,SystemConstants.SwitchFlowGroundLine,null, CBSystemConstants.RunTypeGroundZXDDD, null,true, false, true, true);
				// 是否存在中性点刀闸
				for (PowerDevice kinfe : knifes) {
					if (!kinfe.getDeviceStatus().equals("0")) {
						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
						pdlist.add(kinfe);
						CheckMessage cm = new CheckMessage();
						cm.setBottom("152");
						cm.setPd(pdlist);
						cm.setDeviceName(CZPService.getService().getDevName(pd));
						CBSystemConstants.lcm.add(cm);
						return true;
					}
				}
			}
		}

		return true;
	}

}
