package czprule.rule.conditionmodel;

import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * 主变失电开关断开校核算法
 */
public class ZBLossPowerSwitchOffCheck implements RulebaseInf {

    private String[] bottom = new java.lang.String[]{"1212"};//T_A_RULE_ECHO-echoId

    public void setBottom(String[] bottom) {
        this.bottom = bottom;
    }

    @Override
    public boolean execute(RuleBaseMode rbm) {
        if (rbm == null) {
            return true;
        }
        PowerDevice currentPD = rbm.getPd();
        if (currentPD == null) {
            return true;
        }
        if (!currentPD.getDeviceType().equals(SystemConstants.Switch)) {
            //设备类型是开关，才做校验
            return true;
        }
        if(!rbm.getBeginStatus().equals("0")&&!rbm.getEndState().equals("0")){//只校核开关合上或者断开操作
        	   return true;
        }
        
        if (CBSystemConstants.lcm == null) {
            CBSystemConstants.lcm = new ArrayList<CheckMessage>();
        }
      
        if (zbRecoverOrLossPower(currentPD)) 
        	return true;
        return true;
    }

    /**
     * 【主变失电开关断开校核算法】和【主变复电开关合上校核算法】共用逻辑
     * @param currentPD 当前操作设备
     * @return 无具体意义
     */
    boolean zbRecoverOrLossPower(PowerDevice currentPD) {
        String deviceRunType = currentPD.getDeviceRunType();
        String devRunModel = currentPD.getDeviceRunModel();
        if (CBSystemConstants.RunTypeSwitchDYC.equals(deviceRunType) || CBSystemConstants.RunTypeSwitchFHC.equals(deviceRunType)) {//主变开关，应对非内桥情况
            if (handle1(currentPD)) return true;
        }
        if (devRunModel.equals(CBSystemConstants.RunModelOneMotherLine)&&currentPD.getPowerVoltGrade()==CBSystemConstants.getMapPowerStation().get(currentPD.getPowerStationID()).getPowerVoltGrade()){//针对单母接线，且电压等级与厂站电压等级相等的开关
        	List<PowerDevice> zbList = RuleExeUtil.getDeviceList(currentPD, SystemConstants.PowerTransformer, "", false, false, true);
        	//如果开关搜索联通路径的主变为内桥接线（如果不连通，那么默认内桥主变高压侧刀闸是断开，所以不用做判断）
        	if(zbList.size()>0&&CBSystemConstants.RunModelBridgeLine.equals(zbList.get(0).getDeviceRunModel())){
        		//业务逻辑有点复杂，代码分开判断，方便阅读
        		if(deviceRunType.equals(CBSystemConstants.RunTypeSwitchXL)||deviceRunType.equals(CBSystemConstants.RunTypeSwitchML)){
        			List<PowerDevice> mxList = RuleExeUtil.getDeviceList(currentPD, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, false, false);
        			List<PowerDevice> devList = new ArrayList<PowerDevice>();//判断的主变
        			for(PowerDevice mx:mxList){
        				boolean ret = isOnlySourceSwitch(currentPD,mx);
        				if(ret){
        					List<PowerDevice> linkzbList = RuleExeUtil.getDeviceList(mx, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, false, true);
        					if(linkzbList.size()>0&&!devList.contains(linkzbList.get(0))){
        						devList.add(linkzbList.get(0));
        						commonHandle(currentPD, linkzbList.get(0));
        					}
        					
        				}
        			}
        					
        		}
        	}
        	
        		
        }
        return false;
    }
    //判断开关是否为母线唯一供电开关
    private boolean isOnlySourceSwitch(PowerDevice sw,PowerDevice mx){
    	boolean ret = false;//返回参数
    	List<PowerDevice> xlList =new ArrayList<PowerDevice>();
    	if(sw.getDeviceStatus().equals("0")){//初始合位，要断开关
    		xlList=RuleExeUtil.getDeviceList(mx, SystemConstants.InOutLine,
    					SystemConstants.PowerTransformer, false, false, true);
    	}else{//初始分位，要合开关
    		xlList=RuleExeUtil.getDeviceList(sw, SystemConstants.InOutLine,
					SystemConstants.PowerTransformer, false, false, true);
    	}
    	if(xlList.size()>0){//母线之前有供电线路
			List<PowerDevice> xlexcList = RuleExeUtil.getDeviceList(mx, sw, SystemConstants.InOutLine, SystemConstants.PowerTransformer,
					"", "", false, false, false, true);
			if(xlexcList.size()>0){
				return false;
			}else{
				return true;
			}
		}
    	
    	return ret;
    }
    
    
    
    /**
     * 当前设备为线路开关，主变为桥形接线方式（应对内桥情况）
     *
     * @param currentPD             当前操作设备
     * @param mainTransformerDevice 主变设备
     * @return 如果有校核，返回true；否则返回false
     */
    private boolean handle2(PowerDevice currentPD, PowerDevice mainTransformerDevice) {
        //找到分段开关，并判断是否在断开（运行状态，无需校核）
        List<PowerDevice> switchMLDeviceList = RuleExeUtil.getDeviceList(currentPD, SystemConstants.Switch, SystemConstants.PowerTransformer,
				CBSystemConstants.RunTypeSwitchML, null, false,
                true, false, true);
        ArrayList<PowerDevice> subsectionSwitchList = new ArrayList<PowerDevice>();//分段开关
        for (PowerDevice switchMLDevice : switchMLDeviceList) {
            if (!RuleExeUtil.isSwitchDoubleML(switchMLDevice)) {
                subsectionSwitchList.add(switchMLDevice);
            }
        }
        if (subsectionSwitchList.size() == 0) {
            return true;
        }
        for (PowerDevice subsectionSwitch : subsectionSwitchList) {
            if ("0".equals(subsectionSwitch.getDeviceStatus())) {//开关在运行状态，无需校核
                return true;
            }
        }
        //找到主变刀闸（代替主变开关），并判断是否在合位（在合位才要校核）
        List<PowerDevice> knifeZBSDeviceList = RuleExeUtil.getDeviceList(currentPD, SystemConstants.SwitchSeparate, SystemConstants.Switch,
				CBSystemConstants.RunTypeKnifeZBS, null, false,
                true, false, true);
        if (knifeZBSDeviceList.size() > 0) {
            PowerDevice knifeZBSDevice = knifeZBSDeviceList.get(0);
            if ("1".equals(knifeZBSDevice.getDeviceStatus())) {//刀闸在分位，无需校核
                return true;
            }
        }
        //分段开关不在运行状态，主变刀闸在合位，此时，如果主变中性点接地刀闸在分位，给予提示
        if (commonHandle(currentPD, mainTransformerDevice)) return true;
        return false;
    }

    /**
     * 主变开关对应的处理（应对非内桥情况）
     *
     * @param currentPD 当前操作设备
     * @return 如果有校核，返回true；否则返回false
     */
    private boolean handle1(PowerDevice currentPD) {
        List<PowerDevice> mainTransformerDeviceList = RuleExeUtil.getDeviceList(currentPD, SystemConstants.PowerTransformer, SystemConstants.Switch, true, true,
				true);
        if (mainTransformerDeviceList.size() > 0) {
            PowerDevice mainTransformerDevice = mainTransformerDeviceList.get(0);
            if (commonHandle(currentPD, mainTransformerDevice)) return true;
        }
        return false;
    }

    /**
     * 根据主变查找中性点地刀（需与当前操作设备电压一致），如果查找刀的中性点地刀在分位，则有校核内容
     *
     * @param currentPD             当前操作设备
     * @param mainTransformerDevice 主变设备
     * @return 如果有校核，返回true；否则返回false
     */
    private boolean commonHandle(PowerDevice currentPD, PowerDevice mainTransformerDevice) {
        List<PowerDevice> groundZXDDDDeviceList = RuleExeUtil.getDeviceDirectList(mainTransformerDevice, SystemConstants.SwitchFlowGroundLine,
				CBSystemConstants.RunTypeGroundZXDDD);
        for (PowerDevice groundZXDDevice : groundZXDDDDeviceList) {
            if (currentPD.getPowerVoltGrade() == groundZXDDevice.getPowerVoltGrade()) {//中性点地刀和主变开关电压得一致
                String deviceStatus = groundZXDDevice.getDeviceStatus();
                if ("1".equals(deviceStatus)) {//刀闸在分位
                    ArrayList<PowerDevice> powerDevices = new ArrayList<PowerDevice>();
                    powerDevices.add(groundZXDDevice);
                    RuleExeUtil.lcmAddHandle(powerDevices,bottom[0]);
                    return true;
                }
            }
        }
        return false;
    }

}
