/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 功能说明 : 接地刀闸分闸公用算法
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprulepw.PWSystemConstants;

public class JudgeGroudKnifeToOffPublic implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD))
			return true;
		if (StringUtils.compareStr(pd.getDeviceType(),
				SystemConstants.SwitchFlowGroundLine) == 1) {
			return true;
		}
		if (pd.getDeviceStatus().equals("1")&&CBSystemConstants.roleCode.equals("0")) {
			// 已经处于合闸位置
			return true;
		}
//		Map<Integer, PowerDevice> curDevs = CBSystemConstants
//				.getCurOperateDevs();
//		int size = curDevs.size();
		List<PowerDevice> excDevs = new ArrayList<PowerDevice>();
//		for (int i = 1; i <= size; i++) {
//			excDevs.add(curDevs.get(i));
//		}

		if(CBSystemConstants.lcm == null){
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		
		if(CBSystemConstants.roleCode.equals("1")&&CBSystemConstants.usePwRole&&pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
			List<PowerDevice> li = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, null, PWSystemConstants.PWRunTypeSwitchZX, null, false, true, false, true);
        	if(li.size()>0){
        		List<PowerDevice> list = new ArrayList<PowerDevice>();
        		
        		List<PowerDevice> pathlist = RuleExeUtil.getPathByDevice(pd, li.get(0), "","", true, true);
         		
        		for(PowerDevice dev : pathlist){
        			if(dev.getDeviceType().equals(SystemConstants.Switch)){
        				list.add(dev);
        			}
        		}
        		
        		if(list.size()>0){
        			boolean flag = true;
        			for(PowerDevice dev:list){
        				if(!dev.getDeviceStatus().equals("0")
        						&&!dev.getDeviceRunType().equals(PWSystemConstants.PWRunTypeSwitchZX)){
        					flag = false;
        				}
        			}
        			
        			if(flag){
        				CheckMessage cm = new CheckMessage();
                		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
        				pdlist.add(pd);
        				cm.setPd(pdlist);
        				
        				if(!pd.getGroundLead().equals("")){
        					cm.setBottom("106");
        				}else{
            				cm.setBottom("102");
        				}
        				
        				CBSystemConstants.lcm.add(cm);
        				return true;
        			}
        		}
        	}
		}
		
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		PowerDevice temDev = null;

		// 一、搜索直接连接的线路，如果线路处于运行或者热备用状态不允许拉开接地刀闸
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.InOutLine); // 目标设备线路
		inPara.put("excDevList", excDevs);
		inPara.put("isSearchDirectDevice", true);// 搜索直接相连的设备
		cs.execute(inPara, outPara);
		inPara.clear();
		if (outPara.get("linkedDeviceList") != null) {
			List lines = (ArrayList) outPara.get("linkedDeviceList");
			for (int i = 0; i < lines.size(); i++) {
				temDev = (PowerDevice) lines.get(i);
				if ("012".indexOf(temDev.getDeviceStatus()) >= 0) // 线路热备用运行提示
				{
					rbm.getMessageList().add(
							"[" + temDev.getPowerDeviceName().trim() + "]带电！["
									+ pd.getPowerDeviceName() + "]不能分闸！");
					return false;
				}
				
				List<PowerDevice> lnAllList =RuleExeUtil.getLineAllSideList(temDev);
				for(PowerDevice ln : lnAllList) {
					inPara.put("oprSrcDevice", ln);
					inPara.put("tagDevType", SystemConstants.SwitchSeparate); // 目标设备刀闸
					inPara.put("excDevType", SystemConstants.Switch+","+SystemConstants.PowerTransformer); // 排除开关
					inPara.put("isStopOnBusbarSection", "false"); // 遇母线不停止
					cs.execute(inPara, outPara);
					inPara.clear();
					if (outPara.get("linkedDeviceList") != null) {
						List knifes = (ArrayList) outPara.get("linkedDeviceList");
						for (int j = 0; j < knifes.size(); j++) {

							PowerDevice kf = (PowerDevice) knifes.get(j);
							if (kf.getDeviceStatus().equals("0")) {
								if (CBSystemConstants.isMaxRangeOffTicket
										&& kf.getDeviceRunType().equals(
												CBSystemConstants.RunTypeKnifeMX))
									continue;
								if(kf.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeGYDKQ))
									continue;
								if(!CBSystemConstants.stateOfTheDrawer){
									rbm.getMessageList().add(
											"[" + kf.getPowerStationName()+kf.getPowerDeviceName().trim()
													+ "]处于合位！["+pd.getPowerStationName()+"][" + pd.getPowerDeviceName()
													+ "]不能分闸！");
									return false;
								}
							
							}
						}
					} 
				}
			}
		}

		// 二、搜索接地刀闸作用范围内的刀闸，如果刀闸全部处于分位，可以拉开接地刀闸，否则，刀闸连接的设备不处于运行也可以合上接地刀闸
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.SwitchSeparate); // 目标设备刀闸
		inPara.put("excDevType", SystemConstants.Switch+","+SystemConstants.PowerTransformer); // 排除开关
		inPara.put("isStopOnBusbarSection", "false"); // 遇母线不停止
		cs.execute(inPara, outPara);
		inPara.clear();
		if (outPara.get("linkedDeviceList") != null) {
			List knifes = (ArrayList) outPara.get("linkedDeviceList");
			for (int i = 0; i < knifes.size(); i++) {

				temDev = (PowerDevice) knifes.get(i);
				if (temDev.getDeviceStatus().equals("0")) {
					if (CBSystemConstants.isMaxRangeOffTicket
							&& temDev.getDeviceRunType().equals(
									CBSystemConstants.RunTypeKnifeMX))
						continue;
					if(temDev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeGYDKQ))
						continue;
					if(!CBSystemConstants.stateOfTheDrawer){
						rbm.getMessageList().add(
								"[" + temDev.getPowerDeviceName().trim()
										+ "]处于合位！["+pd.getPowerStationName()+"][" + pd.getPowerDeviceName()
										+ "]不能分闸！");
						return false;
					}
				
				}
			}
		} else {

			// 三、当前接地刀闸作用范围内没有刀闸时，搜索其他连接设备
			inPara.put("oprSrcDevice", pd);
			inPara.put("excDevList", excDevs);
			inPara.put("isSearchDirectDevice", true);// 搜索直接相连的设备
			cs.execute(inPara, outPara);
			inPara.clear();
			if (outPara.get("linkedDeviceList") != null) {
				List devs = (ArrayList) outPara.get("linkedDeviceList");
				for (int i = 0; i < devs.size(); i++) {
					temDev = (PowerDevice) devs.get(i);
					if ("01".indexOf(temDev.getDeviceStatus()) >= 0) // 线路热备用运行提示
					{
						rbm.getMessageList().add(
								"[" + temDev.getPowerDeviceName().trim()
										+ "]带电！[" + pd.getPowerDeviceName()
										+ "]不能分闸！");
						return false;
					}
				}
			}
		}
		return true;
	}
}
