package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-16 上午11:20:00   
* 修改人：FU   
* 修改备注：   旁路刀闸拉开规则
* @version    
*    
*/
public class CheckPLKnifeOff implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd=rbm.getPd();
		//判断刀闸是否为旁路刀闸
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)){
			return true;
		}
		//旁路刀闸连接的旁路开关
		PowerDevice pl=null;
		List<PowerDevice> pllist=RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchPL, null, false, true, false, true);
		if(pllist.size()>0){
			pl=pllist.get(0);
		}
		if(pl!=null){
			//判断旁路开关当前是否在分位
			if(pl.getDeviceStatus().equals("0")){
				CheckMessage cm=new CheckMessage();
				List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
				pdlist.add(pl);
				cm.setPd(pdlist);
				//旁路开关在合位，旁路刀闸不能拉开
				cm.setBottom("114");
				CBSystemConstants.lcm.add(cm);
			}else{
				return true;
			}
		}
		return true;
	}

}
