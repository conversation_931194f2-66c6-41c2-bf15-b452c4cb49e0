package czprule.rule.conditionmodel;

import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * 主变非高压侧开关合上校核算法(主变高压侧在运行状态，才能合中低压侧开关)
 */
public class ZBLowVoltageSideSwitchOnCheck implements RulebaseInf {

    @Override
    public boolean execute(RuleBaseMode rbm) {
        if (rbm == null) {
            return true;
        }
        PowerDevice currentPD = rbm.getPd();
        if (currentPD == null) {
            return true;
        }
        if (!currentPD.getDeviceType().equals(SystemConstants.Switch)) {
            //设备类型是开关，才做校验
            return true;
        }
        if (CBSystemConstants.lcm == null) {
            CBSystemConstants.lcm = new ArrayList<CheckMessage>();
        }
        List<PowerDevice> mainTransformerDeviceList = RuleExeUtil.getDeviceList(currentPD, SystemConstants.PowerTransformer, SystemConstants.Switch, true,
                true, true);
        if (mainTransformerDeviceList.size() > 0) {
            PowerDevice mainTransformerPD = mainTransformerDeviceList.get(0);
            if (CBSystemConstants.RunModelBridgeLine.equals(mainTransformerPD.getDeviceRunModel())) {//适配内桥情况
                innerBridgeHandle(mainTransformerPD);
            } else {//适配非内桥情况
                noInnerBridgeHandle(mainTransformerPD);
            }
        }
        return true;
    }

    /**
     * 非内桥情况处理
     * @param mainTransformerPD 主变设备
     */
    private void noInnerBridgeHandle(PowerDevice mainTransformerPD) {
        List<PowerDevice> switchPDList = RuleExeUtil.getDeviceList(mainTransformerPD, null ,SystemConstants.Switch
                ,SystemConstants.PowerTransformer, null,null,false,true,
                true, true,true);
        if (switchPDList.size() > 0) {
            PowerDevice switchPD = switchPDList.get(0);
            if (!"0".equals(switchPD.getDeviceStatus())) {
                ArrayList<PowerDevice> relativePDs = new ArrayList<PowerDevice>();
                relativePDs.add(switchPD);
                RuleExeUtil.lcmAddHandle(relativePDs,"1216");
            }
        }
    }

    /**
     * 内桥情况处理
     * @param mainTransformerPD 主变设备
     */
    private void innerBridgeHandle(PowerDevice mainTransformerPD) {
        List<PowerDevice> switchPDList = RuleExeUtil.getDeviceList(mainTransformerPD, null ,SystemConstants.Switch
                ,SystemConstants.PowerTransformer, null,null,false,true,
                false, true,true);
        if (switchPDList.size() > 0) {
            ArrayList<PowerDevice> relativePDs = new ArrayList<PowerDevice>();//相关设备（校核表达式“{相关设备}”的取值)）
            for (PowerDevice switchPD : switchPDList) {
                if ("0".equals(switchPD.getDeviceStatus())) {
                    return ;//存在一个开关在合位，无需校核
                } else {
                    relativePDs.add(switchPD);
                }
            }
            RuleExeUtil.lcmAddHandle(relativePDs,"1216");
        }
    }

}
