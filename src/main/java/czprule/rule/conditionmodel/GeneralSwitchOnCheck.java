package czprule.rule.conditionmodel;

import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

import java.util.ArrayList;
import java.util.List;

/**
*  一般开关合上校核算法（开关关联接地刀闸在分位及开关两侧刀闸在合位才能操作）
*/
public class GeneralSwitchOnCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null){
			return true;
		}
		PowerDevice currentPD = rbm.getPd();
		if(currentPD==null){
			return true;
		}
		//识别设备类型判断设备是否是开关
		if(!currentPD.getDeviceType().equals(SystemConstants.Switch)){
			return true;
		}
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (CBSystemConstants.RunTypeSwitchXL.equals(currentPD.getDeviceRunType())) {//线路开关不做处理
			return true;
		}
		ArrayList<PowerDevice> relativeGroundPDs = new ArrayList<PowerDevice>();//相关接地刀闸设备
		ArrayList<PowerDevice> relativePDs = new ArrayList<PowerDevice>();//相关刀闸设备
		List<PowerDevice> groundDisconnectorDirectList = RuleExeUtil.getDeviceDirectList(currentPD, SystemConstants.SwitchFlowGroundLine);//获取开关直接相连的接地刀闸
		for (PowerDevice groundDisconnector : groundDisconnectorDirectList) {
			if ("0".equals(groundDisconnector.getDeviceStatus())) {//有接地刀闸在合位，加入校核内容
				relativeGroundPDs.add(groundDisconnector);
			}
		}
		List<PowerDevice> disconnectorDirectList = RuleExeUtil.getDeviceDirectList(currentPD, SystemConstants.SwitchSeparate);//获取开关直接相连的刀闸
		ArrayList<PowerDevice> monthLineKnifePDs = new ArrayList<PowerDevice>();//母线刀闸设备
		for(PowerDevice disconnector : disconnectorDirectList){
			if(disconnector.getDeviceStatus().equals("1")){
				if (!CBSystemConstants.RunTypeKnifeMX.equals(disconnector.getDeviceRunType())) {//非母线刀闸
					relativePDs.add(disconnector);
				}else{
					monthLineKnifePDs.add(disconnector);
				}
			}
		
		}
		if(currentPD.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
			if(monthLineKnifePDs.size()==2){
				relativePDs.addAll(monthLineKnifePDs);
			}
		}else{
			relativePDs.addAll(monthLineKnifePDs);
		}
			
//		ArrayList<PowerDevice> monthLineKnifePDs = new ArrayList<PowerDevice>();//母线刀闸设备
//		ArrayList<PowerDevice> noMonthLineKnifePDs = new ArrayList<PowerDevice>();//非母线刀闸设备
//		for (PowerDevice disconnector : disconnectorDirectList) {
//			
//				monthLineKnifePDs.add(disconnector);
//			} else {//非母线刀闸设备
//				noMonthLineKnifePDs.add(disconnector);
//			}
//		}
//		knifeHandle(relativePDs, monthLineKnifePDs);
//		knifeHandle(relativePDs, noMonthLineKnifePDs);
		if(relativePDs.size()>0){
			RuleExeUtil.lcmAddHandle(relativePDs, "1213");
		}
		if(relativeGroundPDs.size()>0){
			RuleExeUtil.lcmAddHandle(relativeGroundPDs, "1214");
		}
	
		return true;
	}

	/**
	 * 刀闸（非接地）处理
	 * @param relativePDs 相关刀闸设备（校核内容相关）
	 * @param knifePDs 刀闸
	 */
	private static void knifeHandle(ArrayList<PowerDevice> relativePDs, ArrayList<PowerDevice> knifePDs) {
		for (PowerDevice monthLineKnifePD : knifePDs) {
			if ("1".equals(monthLineKnifePD.getDeviceStatus())) {//刀闸在分位
				relativePDs.add(monthLineKnifePD);
			}
			if ("0".equals(monthLineKnifePD.getDeviceStatus())) {//有一个母线刀闸在合位即可，如果满足，则清空对应的校核
				relativePDs.clear();
			}
		}
	}

}
