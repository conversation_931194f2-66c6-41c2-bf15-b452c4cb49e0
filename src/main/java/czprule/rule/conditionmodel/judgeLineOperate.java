package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipChooseIssueSuper;
import czprule.rule.view.LineTransChooseDialog;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DeviceSVGPanelUtil;

/**
 * 标准规则：线路操作顺序
 * <AUTHOR>
 *
 */
public class judgeLineOperate implements RulebaseInf {
	
	public  PowerDevice sourceLineTrans=null;  //线路合环侧
	public  List<PowerDevice> loadLineTrans=new ArrayList<PowerDevice>(); //线路解环侧

	public boolean execute(RuleBaseMode rbm) {
		
		if(rbm.getPd().getPowerVoltGrade()<220){
			return new judgeLinePowerSide().execute(rbm);
		}
		else {
			return new judgeLineOperateOrder().execute(rbm);
		}
	}
	
	
}
