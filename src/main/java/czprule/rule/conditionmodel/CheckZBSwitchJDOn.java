package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-17 上午09:27:47   
* 修改人：FU   
* 修改备注：   主变开关合上接地电阻算法
* @version    
*    
*/
public class CheckZBSwitchJDOn implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd = rbm.getPd();
		//判断是否是主变电源侧开关
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC))
			return true;
		//查找主变开关对应的接地电阻
		List<PowerDevice> mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer+","+SystemConstants.Switch, true, true, true);
		if(mlList.size() == 0)
			return true;
		List<PowerDevice> efList = RuleExeUtil.getDeviceList(mlList.get(0), SystemConstants.EarthingTransformer, SystemConstants.PowerTransformer, true, true, true);
		for(PowerDevice ef : efList) {
			//查找接地电阻的开关
			PowerDevice efSwitch = RuleExeUtil.getDeviceSwitch(ef);
			//判断接地电阻开关是否合上
			if(!efSwitch.getDeviceStatus().equals("0")) {
				CheckMessage cm=new CheckMessage();
				List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
				pdlist.add(efSwitch);
				cm.setPd(pdlist);
				//合上主变开关前需要先合上接地电阻开关
				cm.setBottom("275");
				CBSystemConstants.lcm.add(cm);
				return true;
			}
		}
		return true;
	}

}
