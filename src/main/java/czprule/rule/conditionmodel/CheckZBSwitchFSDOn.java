package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**
 * 
 * 主变开关合上反充电规则
 * 
 * <AUTHOR>
 * 
 */
public class CheckZBSwitchFSDOn implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd = rbm.getPd();
		// 判断当前开关是否是电源侧开关
		if (RuleExeUtil.isSourceSide(pd)) {
			return true;
		}
		// 判断当前开关是否是负荷侧主变开关
		if (!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)) {
			return true;
		}
		// 查找主变
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd,SystemConstants.PowerTransformer,SystemConstants.PowerTransformer, true, true, true);
		if(tfList.size() == 0)
			return true;
		PowerDevice tf = tfList.get(0);
		
		// 判断电源侧主变开关或母线是否运行
		List<PowerDevice> dycSwitches = RuleExeUtil.getDeviceList(tf,SystemConstants.Switch,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchDYC,CBSystemConstants.RunTypeSwitchFHC, false, true, true, true);
		if(dycSwitches.size() == 0)
			dycSwitches = RuleExeUtil.getDeviceList(tf,null,SystemConstants.MotherLine,SystemConstants.PowerTransformer,"","", false, true, true, true,true);
		if (dycSwitches.size() > 0) {
			if (dycSwitches.get(0).getDeviceStatus().equals("0")) {
				return true;
			}
		}
		else if(dycSwitches.size() == 0)
			return true;
		// 判断负荷侧主变开关是否运行
		List<PowerDevice> fhcSwitches = RuleExeUtil.getDeviceList(tf,SystemConstants.Switch,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchFHC,CBSystemConstants.RunTypeSwitchDYC, false, true, true, true);
		for(PowerDevice dev : fhcSwitches) {
			if (!dev.equals(pd) && dev.getPowerVoltGrade()<pd.getPowerVoltGrade() && dev.getDeviceStatus().equals("0")) {
				List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
				pdlist.add(pd);
				CheckMessage cm = new CheckMessage();
				cm.setBottom("273");
				cm.setPd(pdlist);
				CBSystemConstants.lcm.add(cm);
				return true;
			}
		}
		return true;
	}

}
