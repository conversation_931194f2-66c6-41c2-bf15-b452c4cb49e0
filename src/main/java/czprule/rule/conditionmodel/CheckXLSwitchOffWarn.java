package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.EMSService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**
 * 
 * 创建时间：2014-10-28 下午03:52:26 修改人：gny 修改备注： 拉开线路开关操作顺序提醒
 * 
 * @version
 * 
 */
public class CheckXLSwitchOffWarn implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			return false;
		}
		// 识别开关是否线路开关或线变组接线的主变开关
		if (!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
				&& !pd.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchDYC)) {
			return true;
		}
		
		List<PowerDevice> lineList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
		if(lineList.size() > 0) {
			PowerDevice line = lineList.get(0);
			List<PowerDevice> otherList = RuleExeUtil.getLineOtherSideList(line);
			boolean isExistOff = false;
			for (PowerDevice other : otherList) {
				if(!other.getDeviceStatus().equals("0")) {
					isExistOff = true;
					break;
				}
			}
			if(isExistOff)
				return true;
			//String cl = EMSService.getService().getLineFlow(line.getPowerStationID(), line.getPowerDeviceID());
			List<PowerDevice> searchedList = new ArrayList<PowerDevice>();
			String cl = RuleExeUtil.judgeLineFlow(line,line,searchedList);
			if(line.getDeviceRunModel().equals(CBSystemConstants.RunModelOneLine)) {
				if(cl.equals("2")) {
					List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
					pdlist.add(pd);
					CheckMessage cm = new CheckMessage();
					cm.setBottom("252");
					cm.setPd(pdlist);
					CBSystemConstants.lcm.add(cm);
					return true;
				}
			}
			else if(line.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleLine)) {
				if(cl.equals("1")) {
					List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
					pdlist.add(pd);
					CheckMessage cm = new CheckMessage();
					cm.setBottom("252");
					cm.setPd(pdlist);
					CBSystemConstants.lcm.add(cm);
					return true;
				}
			}
		}
		/*
		// 判断是否能获取到实时线路潮流数据
		String cl = EMSService.getService().getLineFlow(pd.getPowerDeviceID(),
				pd.getPowerStationID());
		if (cl == null || cl.equals("-1")) {
			// 根据相关厂站接线，当前开关连接的线路是否能判定为负荷侧或电源侧(不确定)
			List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(pd,
					SystemConstants.InOutLine);
			if(list.size()<1){
				List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
				pdlist.add(pd);
				CheckMessage cm = new CheckMessage();
				cm.setPd(pdlist);
				cm.setBottom("252");
				CBSystemConstants.lcm.add(cm);
				return true;
			}
			Map<PowerDevice, String> stationlines = QueryDeviceDao
					.getPowersLineByLine(list.get(0));
			if (stationlines.size() < 2) {
				List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
				pdlist.add(pd);
				CheckMessage cm = new CheckMessage();
				cm.setPd(pdlist);
				cm.setBottom("252");
				CBSystemConstants.lcm.add(cm);
				return true;
			}
		}
		// 当前开关连接的线路是否为电源侧，且存在未拉开的负荷侧开关
		List<PowerDevice> stationLines = RuleExeUtil.getDeviceDirectList(pd,
				SystemConstants.InOutLine);
		PowerDevice Line = stationLines.get(0);
		if (RuleExeUtil.isSourceSide(Line)) {
			Map<PowerDevice, String> stationlines = QueryDeviceDao
					.getPowersLineByLine(Line);
			for (PowerDevice line : stationLines) {
				if (!line.getPowerStationID().equals(Line.getPowerStationID())) {
					List<PowerDevice> Switchs = RuleExeUtil
							.getDeviceDirectList(line, SystemConstants.Switch);
					if (Switchs.get(0).getDeviceStatus().equals("0")) {
						return true;
					} else {
						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
						pdlist.add(pd);
						CheckMessage cm = new CheckMessage();
						cm.setBottom("252");
						cm.setPd(pdlist);
						CBSystemConstants.lcm.add(cm);
						return true;
					}
				}
			}
		}
		*/
		//20141214改为客户端验证先拉开的开关是否取开票时选择的解环侧，服务调用暂未做
//		List<PowerDevice> stationLines = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
//		if(stationLines.size() > 0) {
//			PowerDevice line = stationLines.get(0);
//			List<PowerDevice> otherLines = RuleExeUtil.getLineOtherSideList(line);
//			for (PowerDevice otherLine : otherLines) {
//				if(otherLine.getDeviceStatus().equals("0")) {
//					if(CBSystemConstants.LineSource.containsKey(line.getPowerDeviceID()) && 
//							CBSystemConstants.LineSource.get(line.getPowerDeviceID()).equals(otherLine)) {
//						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
//						pdlist.add(pd);
//						CheckMessage cm = new CheckMessage();
//						cm.setBottom("252");
//						cm.setPd(pdlist);
//						CBSystemConstants.lcm.add(cm);
//						return true;
//					}
//				}
//			}
//		}
		
		return true;
	}

}
