package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-12-14上午09:36:28   
* 修改人：郑柯   
* 修改备注：  线路开关合上校核算法
* @version    
*    
*/

public class XLSwitchOnCheck implements RulebaseInf {

	private List<CheckMessage> dzpdList;

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			return false;
		}
		
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
			List<PowerDevice> xlList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
			if(xlList.size()>0){
				List<PowerDevice>  dzpdList = new ArrayList<PowerDevice>();
				List<PowerDevice>  ddpdList = new ArrayList<PowerDevice>();
				List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate);
				ArrayList<PowerDevice> monthLineKnifePDs = new ArrayList<PowerDevice>();//母线刀闸设备
				for(PowerDevice dz:dzList){//有开关直接连接刀闸在分位的
					if(!dz.getDeviceStatus().equals("0")){
						if (!CBSystemConstants.RunTypeKnifeMX.equals(dz.getDeviceRunType())) {//非母线刀闸
							dzpdList.add(dz);
						}else{
							monthLineKnifePDs.add(dz);
						}
					}
				}
				if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
					if(monthLineKnifePDs.size()==2){
						dzpdList.addAll(monthLineKnifePDs);
					}
				}else{
					dzpdList.addAll(monthLineKnifePDs);
				}
				
				List<PowerDevice> ddList = RuleExeUtil.getDeviceDirectList(xlList.get(0), SystemConstants.SwitchFlowGroundLine);
				for(PowerDevice dd:ddList){//有开关直接连接地刀在合位的
					if(dd.getDeviceStatus().equals("0")){
						ddpdList.add(dd);
					}
				}
				List<PowerDevice> allxlList = RuleExeUtil.getLineAllSideList(xlList.get(0));
				for(PowerDevice xl:allxlList){
					List<PowerDevice> xlddList  = RuleExeUtil.getDeviceDirectList(xl, SystemConstants.SwitchFlowGroundLine);
					for(PowerDevice dd:xlddList){
						if(ddpdList.contains(dd)&&dd.getDeviceStatus().equals("0")){//有线路地刀在合位的
							ddpdList.add(dd);
						}
					}
				}
				
				if(dzpdList.size()>0){
					CheckMessage cm = new CheckMessage();
					cm.setBottom("1302");
					cm.setPd(dzpdList);
					CBSystemConstants.lcm.add(cm);
				}
				if(ddpdList.size()>0){
					CheckMessage cm = new CheckMessage();
					cm.setBottom("1303");
					cm.setPd(ddpdList);
					CBSystemConstants.lcm.add(cm);
				}
				return true;
			}
		}
		return true;
	}

}
