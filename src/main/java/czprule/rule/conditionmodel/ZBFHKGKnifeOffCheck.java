package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import javax.print.attribute.standard.PDLOverrideSupported;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2022-10-09 上午11:20:00   
* 修改人：WJQ   
* 修改备注：   主变负荷开关除小车外刀闸拉开校核算法
* @version    
*    
*/
public class ZBFHKGKnifeOffCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd=rbm.getPd();
		CheckMessage cm=new CheckMessage();
		List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
		if(swList.size() > 0){
			//判断设备是否是主变负荷开关除小车外刀闸
			if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZB)){
				return true;
			}
		}
		for(PowerDevice sw : swList){
			if(sw.getDeviceStatus().equals("0")){
				pdlist.add(sw);
			}
		}
		if(pdlist.size() > 0){
			cm.setPd(pdlist);
			cm.setBottom("1000");
			CBSystemConstants.lcm.add(cm);
		}else{
			return true;
		}
		return true;
	}

}
