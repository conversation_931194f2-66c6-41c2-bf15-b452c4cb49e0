package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import javax.print.attribute.standard.PDLOverrideSupported;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2022-10-09 上午11:20:00   
* 修改人：WJQ   
* 修改备注：   内桥主变高压侧刀闸合上校核算法
* @version    
*    
*/
public class NQZBGYKnifeOnCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd=rbm.getPd();
		//判断设备是否是内桥主变刀闸
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZBS)){
			return true;
		}
		CheckMessage cm = new CheckMessage();
		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
		List<PowerDevice> swList = new ArrayList<PowerDevice>();
		List<PowerDevice> kgList = new ArrayList<PowerDevice>();
		List<PowerDevice> mxList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.MotherLine);
		List<PowerDevice> zbList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.PowerTransformer);
		List<PowerDevice> ddList = new ArrayList<PowerDevice>();
		if(mxList.size()>0){
			ddList=RuleExeUtil.getDeviceDirectList(mxList.get(0), SystemConstants.SwitchFlowGroundLine);
		}
		List<PowerDevice> devList=RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchFlowGroundLine);
		for(PowerDevice dev:devList){
			if(!ddList.contains(dev)){
				ddList.add(dev);
			}
		}
		for(PowerDevice mx : mxList){
			swList = RuleExeUtil.getDeviceList(mx, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		}
		for(PowerDevice zb : zbList){
			kgList = RuleExeUtil.getDeviceList(zb, SystemConstants.Switch, SystemConstants.MotherLine, CBSystemConstants.RunTypeSwitchFHC, "", false, true, true, true);
		}
		swList.addAll(kgList);
		for(PowerDevice sw : swList){
			if(sw.getDeviceStatus().equals("0")){
				pdlist.add(sw);
			}
		}
		for(PowerDevice dd : ddList){
			if(dd.getDeviceStatus().equals("0")){
				pdlist.add(dd);
			}
		}
		if(pdlist.size() > 0){
			cm.setPd(pdlist);
			cm.setBottom("1001");
			CBSystemConstants.lcm.add(cm);
		}

		return true;
	}

}
