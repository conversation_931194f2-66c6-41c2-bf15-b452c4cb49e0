package czprule.rule.conditionmodel;

import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;

import javax.swing.*;


/**
 *
 * 创建时间：2014-10-28 下午04:33:48
 * 修改人：FU
 * 修改备注：   开关拉开防误算法
 * @version
 *
 */
public class SelectChangeFH implements RulebaseInf {

    @Override
    public boolean execute(RuleBaseMode rbm) {
        // TODO Auto-generated method stub

        if(rbm.getPd()==null){
            return true;
        }
        if(rbm.getPd().getPowerVoltGrade()!=35&&rbm.getPd().getPowerVoltGrade()!=110){
            return true;
        }
//        if(rbm.getPd().getDeviceType().equals(SystemConstants.Switch)){
//            return true;
//        }
        Object[] options = {"是","否"};
        int sel = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "是否转负荷", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);
        if(sel==0){
            CBSystemConstants.isChangeFH = true;
        }else if(sel==1){
            CBSystemConstants.isChangeFH = false;
        }else{
            return false;
        }
        return true;
    }

}
