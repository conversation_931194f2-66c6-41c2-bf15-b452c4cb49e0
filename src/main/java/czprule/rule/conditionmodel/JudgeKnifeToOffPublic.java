/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 刀闸分闸公用算法
 * 作    者 : 张余平
 * 开发日期 : 2010-10-04
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprulepw.PWSystemConstants;

/**
 * 用 途:刀闸分闸公用算法 描述：刀闸直接连接的开关不处在运行时，可以断开刀闸，在双母情况下另外一个刀闸合上的情况下，可以断开刀闸 特
 * 殊:750kv线路刀闸断开时，判断刀闸连接的母线是否带电，如果带电全部转为停电状态
 * 
 * @参数1(Map)：输入参数 起始设备 oprSrcDevice true表示可分闸,false表示不可分闸
 * @返回值：无
 */
public class JudgeKnifeToOffPublic implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
			if (rbm == null)
				return false;
			PowerDevice pd = rbm.getPd();
			if (pd == null)
				return false;
			if (!pd.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
				rbm.getMessageList().add(
						"刀闸分闸公用算法输入判定对象[" + pd.getPowerDeviceName() + "]非刀闸！");
				return false;
			}
			if (pd.getDeviceStatus().equals("1")&&CBSystemConstants.roleCode.equals("0")) {
				// 已经处于分闸位置
				return true;
			}

			if(CBSystemConstants.roleCode.equals("0")){
				if (!knifeWithSwitch(rbm) || !knifeWithOtherDevice(rbm)
						|| !sideKnifeOffJudgeSwitch(rbm) || !knifeOffWithBigFlow(rbm)
						|| !knifeOffOrderCheck(rbm))
					return false;
			}

			if(CBSystemConstants.lcm == null){
				CBSystemConstants.lcm = new ArrayList<CheckMessage>();
			}
			
			if(CBSystemConstants.roleCode.equals("1")&&CBSystemConstants.usePwRole&&pd.getDeviceType().equals(SystemConstants.SwitchSeparate)){
				List<PowerDevice> li = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, null, PWSystemConstants.PWRunTypeSwitchZX, null, false, true, false, true ,"1");
	        	if(li.size()>0){
	        		List<PowerDevice> tanrs = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, null, null, null, false, true, false, true ,"2");
	        		
	        		if(tanrs.size()>0){
	        			li = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, null, PWSystemConstants.PWRunTypeSwitchZX, null, false, false, false, true);
	        			
	        			if(li.size()>0){
	        				CheckMessage cm = new CheckMessage();
	                		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
	        				pdlist.add(pd);
	        				cm.setPd(pdlist);
	        				cm.setBottom("103");
	        				CBSystemConstants.lcm.add(cm);
	        				return true;
	        			}
	        		}
	        	}else{
	        		 li = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, null, PWSystemConstants.PWRunTypeSwitchZX, null, false, true, false, false ,"2");
	        		 if(li.size()>0){
	        				List<PowerDevice> tanrs = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, null, null, null, false, true, false, true ,"1");
	        				
	        				if(tanrs.size()>0){
	                			li = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, null, PWSystemConstants.PWRunTypeSwitchZX, null, false, false, false, true);
	                			
	                			if(li.size()>0){
	                				CheckMessage cm = new CheckMessage();
	                        		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
	                				pdlist.add(pd);
	                				cm.setPd(pdlist);
	                				cm.setBottom("103");
	                				CBSystemConstants.lcm.add(cm);
	                				return true;
	                		 }
	                	 }
	        		 }
	        	}
			}
			
			return true;
		}
		
		/**
		 * 刀闸拉开顺序闭锁：拉开开关两侧刀闸时，应先拉负荷侧、后拉电源侧。(热倒合除外)
		 * 
		 * @param rbm
		 * @return
		 */
		private boolean knifeOffOrderCheck(RuleBaseMode rbm) {
			
			if(CBSystemConstants.isMaxRangeOffTicket)
				return true;
			if(rbm.getPd().getPowerDeviceName().contains("小车")||rbm.getPd().getPowerDeviceName().contains("手车")){
				return true;
			}
			
			PowerDevice knife = rbm.getPd();
			if(knife.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)) {
				// 获取连接的开关
				List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(knife, SystemConstants.Switch);
				if (list.size() > 0) {
					PowerDevice sw = (PowerDevice) list.get(0);
					if(sw.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
						return true;
					if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) || sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL))
						return true;
					List<PowerDevice> kfList1 = RuleExeUtil.getDeviceDirectByPortList(sw, SystemConstants.SwitchSeparate, "1");
					List<PowerDevice> kfList2 = RuleExeUtil.getDeviceDirectByPortList(sw, SystemConstants.SwitchSeparate, "2");
					List<PowerDevice> mxkfList = null;
					List<PowerDevice> dckfList = null;
					if(kfList1.contains(knife)) {
						mxkfList = kfList1;
						dckfList = kfList2;
					}
					else {
						mxkfList = kfList2;
						dckfList = kfList1;
					}
					//如果母线侧存在其他合上的刀闸则可以拉开
					if(mxkfList.size() >= 2) {
						for(PowerDevice kf : mxkfList) {
							if(!kf.equals(knife) && kf.getDeviceStatus().equals("0")) {
								return true;
							}
						}
					}
					for(PowerDevice kf : dckfList) {
						if(kf.getDeviceStatus().equals("0")) {
							if(kf.getPowerStationName().contains("曹家铺")&&kf.getPowerDeviceName().contains("曹501")){
								return true;
							}
							rbm.getMessageList().add("[" + knife.getPowerDeviceName() + "]" + "处在电源侧," + "处在负荷侧的[" + kf + "]未拉开");
							return false;
						}
					}
				}
			}
			return true;
		}

		/**
		 * 刀闸与设备间的闭锁：电网正常时，220KV及以下刀闸可以拉、合电压互感器、避雷器等设备(热倒合除外)
		 * 
		 * @param rbm
		 * @return
		 */
		private boolean knifeWithOtherDevice(RuleBaseMode rbm) {
			PowerDevice pd = rbm.getPd();
			if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZB) || pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZBS))
				return true;
			List<PowerDevice> listOtherDevice = null;
			List<PowerDevice> listPTandArrester = null;
			List<PowerDevice> listSwitch = null;
			listPTandArrester = RuleExeUtil.getDeviceDirectList(pd, "Arrester,PT,BusbarSection");
			listSwitch = RuleExeUtil.getDeviceDirectList(pd, "Breaker,Disconnector,GroundDisconnector");
			listOtherDevice = RuleExeUtil.getDeviceDirectList(pd, "");
			if (listPTandArrester != null && listPTandArrester.size() > 0)// 如果存在避雷器和电压互感器则可以拉、合刀闸，返回true
				return true;
			else {
				if (listOtherDevice != null && listOtherDevice.size() > 0) {
					for (PowerDevice switchTemp : listOtherDevice) {
						if (switchTemp.getDeviceType().equals(SystemConstants.Switch) && !switchTemp.getDeviceStatus().equals("0"))
							return true;
					}
					for (PowerDevice switchTemp : listOtherDevice) {
						if (listPTandArrester.contains(switchTemp) || listSwitch.contains(switchTemp))
							continue;
						if (switchTemp.getDeviceType().equals(SystemConstants.Switch) && !switchTemp.getDeviceStatus().equals("0"))
							return true;
//						if (switchTemp.getDeviceStatus().equals("0")) {// 如果连接的设备有处于合位的，则不可以操作刀闸，返回false
//							rbm.getMessageList().add("[" + switchTemp + "]在运行不能拉开[" + pd + "]");
//							return false;
//						}
					}
					return true;
				}
			}
			return true;
		}

		/**
		 * 刀闸与开关闭锁规则，如果开关处于合位，则不允许拉开和合上开关两侧的刀闸(热倒合除外)
		 * 
		 * @param rbm
		 * @return
		 */
		private boolean knifeWithSwitch(RuleBaseMode rbm) {
			PowerDevice pd = rbm.getPd();
			List<PowerDevice> listSwitch = null;
			listSwitch = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);// 获取刀闸连接的开关
			if (listSwitch.size() == 0) {// 如果直接连接的设备中没有开关则搜索连接的设备，排除母线和主变，不跨母线和断开路径搜索
				if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZB)) {
					List<PowerDevice> listTF = null;
					listTF = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, SystemConstants.MotherLine + "," + SystemConstants.PowerTransformer, "", "", false, false, true, true, "1");
					if(listTF.size() == 0)
						listSwitch = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.MotherLine + "," + SystemConstants.PowerTransformer, "", "", false, false, true, true, "1");
					listTF = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, SystemConstants.MotherLine + "," + SystemConstants.PowerTransformer, "", "", false, false, true, true, "2");
					if(listTF.size() == 0)
						listSwitch = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.MotherLine + "," + SystemConstants.PowerTransformer, "", "", false, false, true, true, "2");
				}
				else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL))
					;
				else
					listSwitch = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.MotherLine + "," + SystemConstants.PowerTransformer, false, true, true);
				
			}
			if(listSwitch.size() == 0)
				return true;
			PowerDevice switchTemp = listSwitch.get(0);
			
			if (!switchTemp.getDeviceStatus().equals("0"))// 如果开关在分位则返回true
				return true;
			else {
				PowerDevice otherKnife = RuleExeUtil.getKnifeOtherML(pd);
				if(switchTemp.getPowerStationName().contains("曹家铺")&&switchTemp.getPowerDeviceName().contains("曹50")){
					return true;
				}
				if (otherKnife == null) {
					rbm.getMessageList().add("[" + switchTemp + "]在合位不能拉开[" + pd + "]");
					return false;
				} else {
					if (switchTemp.getDeviceRunModel().equals(
							CBSystemConstants.RunModelDoubleMotherLine)
							&& "1".equals(otherKnife.getDeviceStatus())) {// 如果是双母接线方式则返回true(热倒合判断)，如果不是则返回false
						rbm.getMessageList().add(
								"[" + switchTemp + "]在合位，[" + otherKnife
										+ "]在分位，不能拉开[" + pd + "]");
						return false;
					} else if (switchTemp.getDeviceRunModel().equals(
							CBSystemConstants.RunModelDoubleMotherLine)
							&& "0".equals(otherKnife.getDeviceStatus())) {
						return true;
					} else {
						List<PowerDevice> mxList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
						for(int i=0;i<mxList.size();i++){
							if(mxList.get(i).getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
								mxList.remove(i);
								i--;
							}
						}
						if(mxList.size()<2){
							rbm.getMessageList().add(
									"[" + switchTemp + "]在合位不能拉开[" + pd + "]");
							return false;
						}else{
							return true;
						}
					
					}
				}
			}
		}

		/**
		 * 旁路刀闸操作规则：合上旁路刀闸时，若此时没有其它的旁路刀闸在合位，旁路开关必须在分位，否则闭锁；拉开该旁路刀闸时，旁路开关也必须在分位，否则闭锁。
		 * */
		private boolean sideKnifeOffJudgeSwitch(RuleBaseMode rbm) {
			PowerDevice pd = rbm.getPd();
			PowerDevice motherLine = RuleUtil.getDirectMotherLine(pd);
			PowerDevice publicswitch;
			if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)) {
				publicswitch = RuleUtil.getSideSwitch(motherLine);
				if (publicswitch == null) {
					return true;
				}
				if (publicswitch.getDeviceStatus().equals("0")) {
					rbm.getMessageList().add(
							"[" + publicswitch + "]在合位不能拉开[" + pd + "]");
					return false;
				}
			}
			return true;
		}

		/**
		 * 刀闸拉合大电流空载设备：两侧无断路器的刀闸（如内桥接线的主变高压侧刀闸、旁路刀闸等），如操作后使刀闸连接的主变、线路带电状态改变（有压与无压互换）
		 * ，禁止该刀闸操作
		 * */
		private boolean knifeOffWithBigFlow(RuleBaseMode rbm) {

			PowerDevice pd = rbm.getPd();
			if (!pd.getPowerDeviceID().equals(
					CBSystemConstants.getParentDev().getPowerDeviceID())) {
				return true;
			}
			List<PowerDevice> pds = RuleUtil.getDirectDevice(pd);
			List<PowerDevice> devList = RuleUtil.getDirectDevice(pd);

			int count = 0;
			for (PowerDevice dev : pds) {
				// 两侧无断路器
				if (dev.getDeviceType().equals(SystemConstants.Switch)) {
					return true;
				}
				// 在运行的主变 有一个主变开关在运行的就加入计数
				if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)
						&& dev.getDeviceStatus().equals("0")) {
					List<PowerDevice> list = RuleUtil.getLinkedDeviceByType1(dev,
							SystemConstants.Switch);
					for (PowerDevice sw : list) {
						if (sw.getDeviceStatus().equals("0")) {
							if(!devList.contains(dev))
								devList.add(dev);
							count++;
							break;
						}
					}
				}
				if (dev.getDeviceType().equals(SystemConstants.MotherLine)
						|| dev.getDeviceType().equals(SystemConstants.InOutLine)) {
					if (dev.getDeviceStatus().equals("0")) {
						if(!devList.contains(dev))
							devList.add(dev);
						count++;
					}
				}
			}
			// 两个或者两个以上的设备在运行 不允许拉刀闸
			if (count > 1) {
				rbm.getMessageList().add("["+RuleExeUtil.getDeviceName(devList)+"]在运行，不能拉开["+pd.getPowerDeviceName()+"]！");
				return false;
			}
			return true;
		}

		/**
		 * 判断刀闸是否处在负荷侧：根据刀闸连接的开关类型分：主变开关、旁路开关、母联开关、线路开关、旁路兼母联开关和其他开关五种类型
		 * true:是负荷侧；false：电源侧
		 * 
		 * @param rbm
		 * @return
		 */
		public boolean knifeIsLoad(RuleBaseMode rbm) {
			PowerDevice pd = rbm.getPd();
			String result = RuleExeUtil.JudgeknifeIsPowerSide(pd);
			if ("2".equals(result) || "9".equals(result))
				return true;
			else
				return false;
		}

		/**
		 * 判断操作刀闸所属开关的另一侧刀闸的开合状态，拉开刀闸时当且仅当刀闸处于电源侧时才会进入此方法验证处于负荷侧的另一道闸是否处于分位，
		 * 如果不处于分位则不可以拉开电源侧的刀闸
		 * 
		 * @param rbm
		 * 
		 * @param knifePd
		 * @param switchPd
		 * @return
		 */
		public boolean isOtherKnifeOff(RuleBaseMode rbm) {
			PowerDevice knifePd = rbm.getPd();
			List<PowerDevice> list = null;

			// 获取连接的开关
			list = RuleUtil.getDirectDevice(knifePd, SystemConstants.Switch);
			if (list == null || list.size() == 0) {
				return true;
			}
			PowerDevice switchPd = (PowerDevice) list.get(0);
			
			if(switchPd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
				return true;

			List<PowerDevice> otherKnifeList = new ArrayList<PowerDevice>();
			List<PowerDevice> port1List = RuleExeUtil.getDeviceList(switchPd, SystemConstants.SwitchSeparate, "", "", "", true, true, true, true, "1");
			if(!port1List.contains(knifePd))
				otherKnifeList.addAll(port1List);
			List<PowerDevice> port2List = RuleExeUtil.getDeviceList(switchPd, SystemConstants.SwitchSeparate, "", "", "", true, true, true, true, "2");
			if(!port2List.contains(knifePd))
				otherKnifeList.addAll(port2List);
				
			for (PowerDevice otherKnife : otherKnifeList) {
				if (!CBSystemConstants.isMaxRangeOffTicket
						&& otherKnife.getDeviceStatus().equals("0")) {// 判断另一个刀闸是否在合位，如果是则返回false，不可以拉开
					rbm.getMessageList().add(
							"[" + knifePd.getPowerDeviceName() + "]" + "处在电源侧,"
									+ "处在负荷侧的[" + otherKnife + "]未拉开");
					return false;
				}
			}
			return true;
		}
}
