package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-12-14上午09:36:28   
* 修改人：郑柯   
* 修改备注：  3/2接线开关合上校核算法
* @version    
*    
*/

public class ThreeTwoSwitchOnCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			return false;
		}
		
		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)) {
			if(RuleExeUtil.isSwMiddleInThreeSecond(pd)) { //中间开关
				List<PowerDevice> swLit = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				for(PowerDevice sw : swLit) {
					//存在分位的母线侧开关
					if(!RuleExeUtil.isSwMiddleInThreeSecond(sw) && !sw.getDeviceStatus().equals("0")) {
						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
						pdlist.add(sw);
						CheckMessage cm = new CheckMessage();
						cm.setBottom("1301");
						cm.setPd(pdlist);
						CBSystemConstants.lcm.add(cm);
						return true;
					}
				}
			}
		}
		return true;
	}

}
