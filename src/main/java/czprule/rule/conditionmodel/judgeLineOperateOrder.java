package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipChooseIssueSuper;
import czprule.rule.view.LineTransChooseDialog;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DeviceSVGPanelUtil;

public class judgeLineOperateOrder implements RulebaseInf {
	
	public  PowerDevice sourceLineTrans=null;  //线路合环侧
	public  List<PowerDevice> loadLineTrans=new ArrayList<PowerDevice>(); //线路解环侧

	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		if(CBSystemConstants.sdkz_gz == 1){
			CBSystemConstants.sdkz_gz = 0;
			return true;
		}
		sourceLineTrans=null;
		//loadLineTrans.clear();
		Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineByLine(pd);
		if(stationlines.size() == 0) { //该线路是站内线路
			sourceLineTrans=pd;
			setSourceLoad(pd, sourceLineTrans, loadLineTrans);
			return true;
		}
		if(CBSystemConstants.cardbuildtype.equals("2")) {
			
		}
		else if(CBSystemConstants.cardbuildtype.equals("1")) {
			sourceLineTrans=pd;
			setSourceLoad(pd, sourceLineTrans, loadLineTrans);
			return true;
		}
		PowerDevice dev=null; //变电站对象
		List<PowerDevice> trans=new ArrayList<PowerDevice>();
		if(CBSystemConstants.isCurrentSys && !CBSystemConstants.cardbuildtype.equals("2")) {
			//打开线路关联的变电站接线图
			String filePath = "";
			if(SystemConstants.getGuiBuilder()!=null){//不为空  才能获得路径    tanfei 2014-09-09
				filePath = SystemConstants.getGuiBuilder().getActivateSVGPanel().getFilePath();
			}
			
			
			for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
				dev=(PowerDevice)iterator.next();
				trans.add(dev);
				DeviceSVGPanelUtil.openSVGPanel(dev.getPowerStationID(), dev.getPowerDeviceID());
			}
		
			if(!"".equals(pd.getPowerStationName()) && !"".equals(filePath)) //如果是在站内操作线路，打开对侧变电站后还要回到当前变电站，如果是全网图线路则不管
			     SystemConstants.getGuiBuilder().activateTabbedPageByName(filePath);
		}
		else {
			if(!CBSystemConstants.isztcp){
				for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
					dev=(PowerDevice)iterator.next();
					trans.add(dev);
					CreatePowerStationToplogy.loadFacData(dev.getPowerStationID());
				}
			}
		}
		
		//选择线路两端变电站电源侧负荷侧
		PowerDevice checkDev=null;
		
		
		if(checkDev==null ||  CBSystemConstants.isCurrentSys == false||  CBSystemConstants.cardbuildtype.equals("2")){
			String hjh = "";
			if(rbm.getBeginStatus().equals("0"))
				hjh = "解环";
			else if(rbm.getEndState().equals("0"))
				hjh = "合环";
			else
				hjh = "解合环";
			
			if(stationlines.size() == 1 || CBSystemConstants.isCurrentSys == false||  CBSystemConstants.cardbuildtype.equals("2")) {
				for (Iterator iterator = stationlines.keySet().iterator(); iterator
						.hasNext();) {
					checkDev = (PowerDevice) iterator.next();
					if(!CBSystemConstants.oneClickString.equals("") && 
							CBSystemConstants.oneClickString.equals(CZPService.getService().getDevName(CBSystemConstants.getPowerStation(checkDev.getPowerStationID()))))
						break;
					if(!CBSystemConstants.oneClickString.equals("") && 
							CZPService.getService().getDevName(CBSystemConstants.getPowerStation(checkDev.getPowerStationID())).contains(CBSystemConstants.oneClickString))
						break;
					if(CBSystemConstants.mapPara.containsKey("lineStationName") && 
							!CBSystemConstants.mapPara.get("lineStationName").equals("") && 
							CBSystemConstants.mapPara.get("lineStationName").equals(CZPService.getService().getDevName(CBSystemConstants.getPowerStation(checkDev.getPowerStationID()))))
						break;
					if(CBSystemConstants.mapPara.containsKey("lineStationID") && 
							!CBSystemConstants.mapPara.get("lineStationID").equals("") && 
							CBSystemConstants.mapPara.get("lineStationID").equals(checkDev.getPowerStationID()))
						break;
				}
			}
			else{
				LineTransChooseDialog linetransChoose=new LineTransChooseDialog(SystemConstants.getMainFrame(), true, stationlines, "选择【"+CZPService.getService().getDevName(pd)+"】的"+hjh+"厂站");
				checkDev=linetransChoose.getReslut();
			}
		}
		if(checkDev==null)
			return false;
		
		sourceLineTrans=(PowerDevice)CBSystemConstants.getPowerDevice(checkDev.getPowerStationID(), checkDev.getPowerDeviceID());
		trans.remove(checkDev);
		CBSystemConstants.putCurOperateDev(sourceLineTrans);
		for (int i = 0; i < trans.size(); i++) {
			dev=(PowerDevice)CBSystemConstants.getPowerDevice(trans.get(i).getPowerStationID(), trans.get(i).getPowerDeviceID());
			CBSystemConstants.putCurOperateDev(dev);
			loadLineTrans.add(dev);
		}
		
//		if(Integer.valueOf(rbm.getBeginStatus()) < Integer.valueOf(rbm.getEndState())) {
//			sourceLineTrans=(PowerDevice)CBSystemConstants.getPowerDevice(checkDev.getPowerStationID(), checkDev.getPowerDeviceID());
//			trans.remove(checkDev);
//			CBSystemConstants.putCurOperateDev(sourceLineTrans);
//			for (int i = 0; i < trans.size(); i++) {
//				dev=(PowerDevice)CBSystemConstants.getPowerDevice(trans.get(i).getPowerStationID(), trans.get(i).getPowerDeviceID());
//				CBSystemConstants.putCurOperateDev(dev);
//				loadLineTrans.add(dev);
//			}
//		}
//		else {
//			dev = (PowerDevice)CBSystemConstants.getPowerDevice(checkDev.getPowerStationID(), checkDev.getPowerDeviceID());
//			loadLineTrans.add(dev);
//			CBSystemConstants.putCurOperateDev(dev);
//			trans.remove(checkDev);
//			for (int i = 0; i < trans.size(); i++) {
//				dev=(PowerDevice)CBSystemConstants.getPowerDevice(trans.get(i).getPowerStationID(), trans.get(i).getPowerDeviceID());
//				CBSystemConstants.putCurOperateDev(dev);
//				sourceLineTrans = dev;
//			}
//		}
		
		for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
			pd=(PowerDevice)iterator.next();
			setSourceLoad(pd, sourceLineTrans, loadLineTrans);
		}

		return true;
	}
	
	private void setSourceLoad(PowerDevice pd, PowerDevice source, List<PowerDevice> load) {
		CBSystemConstants.putLineSource(pd.getPowerDeviceID(), source);
		CBSystemConstants.putLineLoad(pd.getPowerDeviceID(), load);
	}

}
