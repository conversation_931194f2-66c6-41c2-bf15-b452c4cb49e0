package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
/**
 *  合主变开关与中性点刀闸规则
 * <AUTHOR>
 *
 */
public class CheckZBSwitchZXDOn implements RulebaseInf {


	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		
		PowerDevice pd = rbm.getPd();
		if(!RuleExeUtil.isSourceSide(pd))
			return true;
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
			return true;
		}
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
			return true;
		}
		// 搜索开关连接主变
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd,SystemConstants.PowerTransformer,SystemConstants.PowerTransformer, null, null,false, false, false, true);
		if(tfList.size() == 0) {
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(pd,SystemConstants.MotherLine,SystemConstants.PowerTransformer, null, null,false, false, false, true);
			if(mlList.size() > 0) {
				tfList = RuleExeUtil.getDeviceList(mlList.get(0),SystemConstants.PowerTransformer,SystemConstants.PowerTransformer, null, null,false, false, false, true);
			}
		}
		for(PowerDevice tf: tfList) {
			if(tf.getDeviceStatus().equals("0"))
				continue;
			if(tf.getPowerVoltGrade() <= 35)
				continue;
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(pd, null, SystemConstants.InOutLine+","+SystemConstants.MotherLine, SystemConstants.PowerTransformer, null, null, false, false, false, true, false);
			if(lineList.size() == 0)
				continue;
			else {
				for (Iterator<PowerDevice> it = lineList.iterator();it.hasNext();) {
					PowerDevice ln = it.next();
					if(!ln.getDeviceStatus().equals("0") && !ln.getDeviceStatus().equals("1"))
						it.remove();
				}
			}
			if(lineList.size() > 0) {
				// 搜索开关连接线圈中性点刀闸
				List<PowerDevice> knifes = RuleExeUtil.getDeviceList(tf,SystemConstants.SwitchFlowGroundLine,null, CBSystemConstants.RunTypeGroundZXDDD, null,true, false, true, true);
				// 是否存在中性点刀闸
				if (knifes.size() > 0) {
					for (PowerDevice kinfe : knifes) {
						// 判断中性点刀闸是否合位
						if (!kinfe.getDeviceStatus().equals("0")) {
							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
							pdlist.add(kinfe);
							CheckMessage cm = new CheckMessage();
							cm.setBottom("173");
							cm.setPd(pdlist);
							CBSystemConstants.lcm.add(cm);
							return true;
						}
					}
				} else {
					continue;
				}
			}
			
		}

		return true;
		/*
		
		// 搜索开关连接线圈中性点刀闸
		List<PowerDevice> knifes = RuleExeUtil.getDeviceList(pd,SystemConstants.SwitchFlowGroundLine,null, CBSystemConstants.RunTypeGroundZXDDD, null,true, false, true, true);
		
		List<PowerDevice> knifes = RuleExeUtil.getDeviceList(pd,
				CBSystemConstants.RunTypeGroundZXDDD, null, true, true, true);
		// 是否存在中性点刀闸
		if (knifes.size() > 0) {
			for (PowerDevice kinfe : knifes) {
				if (kinfe.getPowerVoltGrade() == pd.getPowerVoltGrade()) {
					// 判断中性点刀闸是否合位
					if (!kinfe.getDeviceStatus().equals("0")) {
						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
						pdlist.add(kinfe);
						CheckMessage cm = new CheckMessage();
						cm.setBottom("173");
						cm.setPd(knifes);
						CBSystemConstants.lcm.add(cm);
						return true;
					}else{
						break;
					}
				}
			}
		} else {
			return true;
		}
	
		return true;
		*/
	}



}
