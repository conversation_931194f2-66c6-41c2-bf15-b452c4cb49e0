package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**
 *
 * 创建时间：2014-10-16 上午10:42:08
 * 修改人：FU
 * 修改备注：   刀闸合上操作顺序闭锁
 * @version
 *
 */
public class CheckKnifeOnSequence implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}


		//20141218 修改
		PowerDevice knife = rbm.getPd();
		if(knife.getPowerDeviceName().contains("小车"))
			return true;

//		List<PowerDevice> linelist = RuleExeUtil.getDeviceList(knife, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
//		if(linelist.size() == 0)
//			return true;

		List<PowerDevice> swlist = RuleExeUtil.getDeviceDirectList(knife, SystemConstants.Switch);


		if(!knife.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)) {



			List<PowerDevice> otherSideKnifeList = RuleExeUtil.getKnifeOtherSide(knife);


			if(otherSideKnifeList != null &&
					otherSideKnifeList.size()>0 &&
					otherSideKnifeList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)) {
				if(!otherSideKnifeList.get(0).getPowerDeviceName().contains("手车1")&&!otherSideKnifeList.get(0).getPowerDeviceName().contains("小车1")){
					boolean isExistOn = false;
					for(PowerDevice otherSideKnife :otherSideKnifeList)  {
						if(otherSideKnife.getDeviceStatus().equals("0")) {
							isExistOn = true;
							break;
						}
					}
					if(!isExistOn) {
						CheckMessage cm=new CheckMessage();
						List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
						pdlist.addAll(otherSideKnifeList);
						cm.setPd(pdlist);
						//送电操作，请先合上电源侧刀闸
						cm.setBottom("133");
						CBSystemConstants.lcm .add(cm);
						return true;
					}
				}

			}else if(swlist.size()>0&&!swlist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){//非双母接线，非母线侧刀闸，如果连接线路，则判断是否直接连线路，如果非直连，则判断另外一个直连刀闸是否已经拉开
				if(swlist.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
					//找其他开关，3/2接线有两个设备（2线路或1线路1主变，）判断是停哪个设备
					List<PowerDevice>  otherswList = RuleExeUtil.getDeviceList(swlist.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					PowerDevice dev = null;//停电开关
					for(PowerDevice sw:otherswList){
						if(!sw.getDeviceStatus().equals("0")&&!sw.getPowerDeviceName().contains("虚")){//搜非运行开关，该开关连接设备为停电设备
							dev =sw;
							break;
						}
					}
					if(dev!=null){
						List<PowerDevice>  devList = RuleExeUtil.getDeviceList(dev, SystemConstants.InOutLine+","+SystemConstants.PowerTransformer,
								SystemConstants.Switch, true, true, true);
						if(devList.size()>0){
							List<PowerDevice> pathList= RuleExeUtil.getPathByDevice(knife, devList.get(0), SystemConstants.Switch, "", true, true);
							if(pathList!=null&&pathList.size()>0){
								otherSideKnifeList = RuleExeUtil.getKnifeOtherSide(knife);
								if(otherSideKnifeList==null){
									return true;
								}
								for(PowerDevice otherSideKnife :otherSideKnifeList)  {
									if(otherSideKnife.getPowerDeviceName().contains("手车1")||otherSideKnife.getPowerDeviceName().contains("小车1")){
										continue;
									}
									if(!otherSideKnife.getDeviceStatus().equals("0")) {
										CheckMessage cm=new CheckMessage();
										List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
										pdlist.add(otherSideKnife);
										cm.setPd(pdlist);
										//停电操作，请先拉开负荷侧刀闸
										cm.setBottom("133");
										CBSystemConstants.lcm.add(cm);
										return true;
									}
								}
							}
						}
					}

				}else{
					List<PowerDevice> lineList  = RuleExeUtil.getDeviceList(swlist.get(0), SystemConstants.InOutLine, SystemConstants.PowerTransformer+","+SystemConstants.Term,
							true, true, true);
					if(lineList.size()>0&&RuleExeUtil.getDeviceDirectList(knife, SystemConstants.InOutLine).size()==0){
						otherSideKnifeList = RuleExeUtil.getKnifeOtherSide(knife);
						if(otherSideKnifeList==null){
							return true;
						}
						for(PowerDevice otherSideKnife :otherSideKnifeList)  {
							if(otherSideKnife.getPowerDeviceName().contains("手车1")||otherSideKnife.getPowerDeviceName().contains("小车1")){
								continue;
							}
							if(otherSideKnife.getDeviceStatus().equals("0")) {
								CheckMessage cm=new CheckMessage();
								List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
								pdlist.add(otherSideKnife);
								cm.setPd(pdlist);
								//停电操作，请先拉开负荷侧刀闸
								cm.setBottom("133");
								CBSystemConstants.lcm.add(cm);
								return true;
							}
						}
					}
				}




			}
		}


		return true;
	}

}
