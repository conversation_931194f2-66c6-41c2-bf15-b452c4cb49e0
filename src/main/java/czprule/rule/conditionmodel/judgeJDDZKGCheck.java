package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-10 上午09:40:05   
* 修改人：FU   
* 修改备注：   接地电阻防误提醒规则
* @version    
*    
*/
public class judgeJDDZKGCheck implements RulebaseInf{

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null) {
			return false;
		}
		List<CheckMessage> cml=rbm.getMessage().get("sbxx");
		if(cml==null){
			cml=new ArrayList<CheckMessage>();
			rbm.getMessage().put("sbxx", cml);
		}
		//目标状态
		String rbmendstate=rbm.getEndState();
		//设备
		PowerDevice pd=rbm.getPd();
		if(!pd.getDeviceType().equals(SystemConstants.Switch))
			return true;
		//是否接地电阻
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB)){
			//接地电阻最近的母线
			PowerDevice mx=null;
			List<PowerDevice> mxlist=RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, null, null, null, false, true, true, true);
			if(mxlist.size()>0){
				mx=mxlist.get(0);
				
			}
			if(mx!=null){
				//是否还有其他接地电阻
				List<PowerDevice> jddzlist=RuleExeUtil.getDeviceList(mx,SystemConstants.Switch,null,CBSystemConstants.RunTypeSwitchJDB,null,false,false,false,false);
				int n=0;
				//母线上的接地电阻
				if(jddzlist.size()>1){
					//目标状态是否拉开
					if(rbmendstate.equals("0")){
//						String sbtx="开关合上导致所连母线上存在多个运行的接地电阻";
//						CheckMessage cm=new CheckMessage();
//						cm.setMessage(sbtx);
//						cm.setPd(mx);
//						cm.setBottom("1");
//						cml.add(cm);
					}else{
						
					}
				}else{
					//目标状态是否拉开
					if(rbmendstate.equals("0")){
						
					}else{
//						String sbtx="开关断开导致所连"+mx.getPowerDeviceName()+"失去接地电阻";
//						CheckMessage cm=new CheckMessage();
//						cm.setMessage(sbtx);
//						cm.setPd(mx);
//						cm.setBottom("2");
//						cml.add(cm);
					}
				}
			}
		}else{
			//非接地电阻最近的母线
			PowerDevice mx=null;
			List<PowerDevice> mxlist=RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, null, null, null, false, true, true, true);
			if(mxlist.size()>0){
				mx=mxlist.get(0);
			}
			if(mx!=null){
				//是否还有其他接地电阻
				List<PowerDevice> jddzlist=RuleExeUtil.getDeviceList(mx,SystemConstants.Switch,null,CBSystemConstants.RunTypeSwitchJDB,null,false,false,false,false);
				int n=0;
				if(jddzlist.size()>0){
					//是否运行多个接地电阻
					Boolean ismorejddz=true;
					if(jddzlist.size()>1){
						for(int i=0;i<jddzlist.size();i++){
							PowerDevice jddz=jddzlist.get(i);
							if(!jddz.getDeviceStatus().equals("0")){
								ismorejddz=false;
							}
						}
					}
					if(ismorejddz){
//						String sbtx="开关所连母线上存在多个运行的接地电阻";
//						CheckMessage cm=new CheckMessage();
//						cm.setMessage(sbtx);
//						cm.setPd(mx);
//						cm.setBottom("1");
//						cml.add(cm);
					}
				}else{
					//目标状态是否拉开
					if(rbmendstate.equals("0")){
						
					}else{
//						String sbtx="开关所连母线无接地电阻投运，不能送电";
//						CheckMessage cm=new CheckMessage();
//						cm.setMessage(sbtx);
//						cm.setPd(mx);
//						cm.setBottom("2");
//						cml.add(cm);
					}
				}
			}
		}
		return true;
	}

}
