package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.EMSService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**
 * 线路开关合上操作顺序提醒
 * 
 * <AUTHOR>
 * 
 */
public class CheckSwitchOnOrder implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			return false;
		}
		// 识别开关是否线路开关或线变组接线的主变开关
		if (!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
				&& !pd.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchDYC)) {
			return true;
		}
		/*
		// 判断是否能获取到实时线路潮流数据
		String cl = EMSService.getService().getLineFlow(pd.getPowerDeviceID(),
				pd.getPowerStationID());
		if (cl == null || cl.equals("-1")) {
			// 根据相关厂站接线，当前开关连接的线路是否能判定为负荷侧或电源侧(不确定)
			List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(pd,
					SystemConstants.InOutLine);
			if(list.size()<1){
				List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
				pdlist.add(pd);
				CheckMessage cm = new CheckMessage();
				cm.setPd(pdlist);
				cm.setBottom("271");
				CBSystemConstants.lcm.add(cm);
				return true;
			}
			Map<PowerDevice, String> stationlines = QueryDeviceDao
					.getPowersLineByLine(list.get(0));
			if (stationlines.size() < 2) {
				List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
				pdlist.add(pd);
				CheckMessage cm = new CheckMessage();
				cm.setPd(pdlist);
				cm.setBottom("271");
				CBSystemConstants.lcm.add(cm);
				return true;
			}
		}
		// 当前开关连接的线路是否为电源侧，且存在未拉开的负荷侧开关
		List<PowerDevice> stationLines = RuleExeUtil.getDeviceDirectList(pd,
				SystemConstants.InOutLine);
		PowerDevice Line = stationLines.get(0);
		if (RuleExeUtil.isSourceSide(Line)) {
			Map<PowerDevice, String> stationlines = QueryDeviceDao
					.getPowersLineByLine(Line);
			for (PowerDevice line : stationLines) {
				if (!line.getPowerStationID().equals(Line.getPowerStationID())) {
					List<PowerDevice> Switchs = RuleExeUtil
							.getDeviceDirectList(line, SystemConstants.Switch);
					if (Switchs.get(0).getDeviceStatus().equals("0")) {
						return true;
					} else {
						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
						pdlist.add(pd);
						CheckMessage cm = new CheckMessage();
						cm.setBottom("271");
						cm.setPd(pdlist);
						CBSystemConstants.lcm.add(cm);
						return true;
					}
				}
			}
		}
		*/
//		//20141218改为客户端验证后合上的开关是否取开票时选择的合环侧，服务调用暂未做
//		List<PowerDevice> stationLines = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
//		if(stationLines.size() > 0) {
//			PowerDevice line = stationLines.get(0);
//			List<PowerDevice> otherLines = RuleExeUtil.getLineOtherSideList(line);
//			for (PowerDevice otherLine : otherLines) {
//				if(!otherLine.getDeviceStatus().equals("0")) {
//					if(CBSystemConstants.LineSource.containsKey(line.getPowerDeviceID()) &&
//							CBSystemConstants.LineSource.get(line.getPowerDeviceID()).equals(line)) {
//						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
//						pdlist.add(pd);
//						CheckMessage cm = new CheckMessage();
//						cm.setBottom("271");
//						cm.setPd(pdlist);
//						CBSystemConstants.lcm.add(cm);
//						return true;
//					}
//				}
//			}
//		}
		return true;
	}

}
