package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-17 上午09:06:19   
* 修改人：FU   
* 修改备注：   主变开关合上顺序算法
* @version    
*    
*/
public class CheckZBSwitchOnSequence implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd = rbm.getPd();
		//查找高于当前开关电压等级的各侧主变开关
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, "", true, true, true);
		if(tfList.size() == 0)
			return true;
		PowerDevice tf = tfList.get(0);
		PowerDevice zyc=null;
		PowerDevice gyc=null;
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(tf, SystemConstants.Switch, "", "", CBSystemConstants.RunTypeSideMother, false, false, false, true);
		for (Iterator<PowerDevice> it = swList.iterator(); it.hasNext();) {
			PowerDevice sw = it.next();
			if(sw.equals(pd) || sw.getPowerVoltGrade() <= pd.getPowerVoltGrade())
				it.remove();
			if(zyc==null){
				zyc=sw;
			}else{
				gyc=sw;
			}
			if(zyc!=null&&gyc!=null){
				if(zyc.getPowerVoltGrade()>gyc.getPowerVoltGrade()){
					PowerDevice temp=null;
					temp=zyc;
					zyc=gyc;
					gyc=temp;
				}
			}
		}
		
		int gyccount = 0;
		boolean isgycon = false;
		for (Iterator<PowerDevice> it = swList.iterator(); it.hasNext();) {
			PowerDevice sw = it.next();
			if(sw.getPowerVoltGrade() == tf.getPowerVoltGrade()) {
				gyccount++;
				if(sw.getDeviceStatus().equals("0"))
					isgycon = true;
			}
		}
		if(gyccount >= 2 && isgycon) {
			for (Iterator<PowerDevice> it = swList.iterator(); it.hasNext();) {
				PowerDevice sw = it.next();
				if(sw.getPowerVoltGrade() == tf.getPowerVoltGrade() && !sw.getDeviceStatus().equals("0")) {
					it.remove();
				}
			}
		}
		
		//判断查找到的开关是否为空
		if(swList.size() == 0)
			return true;
		//判断查找到的开关是否都在合上状态
		boolean isAllOn = true;
		for(PowerDevice sw : swList) {
			if(!sw.getDeviceStatus().equals("0")) {
				isAllOn = false;
				break;
			}
		}
		if(isAllOn)
			return true;
		//判断电源侧主变开关是否在拉开状态
		boolean isSourceOn = false;
		for(PowerDevice sw : swList) {
			if((sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)||
					sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)||
					sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) && 
					sw.getDeviceStatus().equals("0")) {
				isSourceOn = true;
				break;
			}
		}
		if(isSourceOn) {
			CheckMessage cm=new CheckMessage();
			List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
			pdlist.add(zyc);
			cm.setPd(pdlist);
			//先闭合中压侧开关
			cm.setBottom("272");
			CBSystemConstants.lcm.add(cm);
			return true;
		}
		else {
			CheckMessage cm=new CheckMessage();
			List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
			pdlist.add(gyc);
			cm.setPd(pdlist);
			//先闭合电源侧开关
			cm.setBottom("272");
			CBSystemConstants.lcm.add(cm);
			return true;
		}
		
	}

}
