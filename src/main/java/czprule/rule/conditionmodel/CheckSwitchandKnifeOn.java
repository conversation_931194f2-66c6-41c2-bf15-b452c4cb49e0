package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**
 * 合开关与刀闸操作闭锁
 * 
 * <AUTHOR>
 * 
 */
public class CheckSwitchandKnifeOn implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		if (!pd.getDeviceType().equals(SystemConstants.Switch)) {
			return true;
		}
		// 搜索开关直接相连设备是否有开关刀闸
		List<PowerDevice> sgList = RuleExeUtil.getDeviceDirectList(pd,
				SystemConstants.SwitchSeparate);
		if (sgList.size() > 0) {
			// 搜索开关直接相连设备是否有开关刀闸
			boolean isOk1 = false;
			boolean isOk2 = false;
			// 得到开关第一个端口直接连接的刀闸
			List<PowerDevice> ssList1 = RuleExeUtil.getDeviceDirectByPortList(
					pd, SystemConstants.SwitchSeparate, "1");
			// 判断开关第一个端口直接连接的刀闸中是否存在合上的
			if (ssList1.size() == 0)
				isOk1 = true;
			else if (ssList1.size() > 0) {
				// 至少有一个被选中
				for (int index = 0; index < ssList1.size(); index++) {
					if (ssList1.get(index).getDeviceStatus().equals("0")) {
						isOk1 = true;
						break;
					} else {
						continue;
					}
				}
			} else
				isOk1 = true;
			// 得到开关第二个端口直接连接的刀闸
			List<PowerDevice> ssList2 = RuleExeUtil.getDeviceDirectByPortList(
					pd, SystemConstants.SwitchSeparate, "2");
			// 判断开关第二个端口直接连接的刀闸中是否存在合上的
			if (ssList2.size() == 0)
				isOk2 = true;
			else if (ssList2.size() > 0) {
				// 至少有一个被选中
				for (int index = 0; index < ssList2.size(); index++) {
					if (ssList2.get(index).getDeviceStatus().equals("0")) {
						isOk2 = true;
						break;
					} else {
						continue;
					}
				}
			}
			// 开关 两侧刀闸是否都有合上的记录
			if (isOk1 && isOk2) {
				return true;
			} else {
				List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
				if (!isOk1) {
					pdlist.addAll(ssList1);
				}
				if (!isOk1) {
					pdlist.addAll(ssList2);
				}
				CheckMessage cm = new CheckMessage();
				cm.setBottom("172");
				cm.setPd(pdlist);
				CBSystemConstants.lcm.add(cm);
				return true;
			}
		}
		return true;
	}

}
