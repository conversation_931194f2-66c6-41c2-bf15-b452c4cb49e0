package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import javax.print.attribute.standard.PDLOverrideSupported;

import org.hibernate.pretty.DDLFormatter;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2022-10-09 上午11:20:00   
* 修改人：WJQ   
* 修改备注：   母线对侧刀闸合上校核算法
* @version    
*    
*/
public class MXDCKnifeOnCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd=rbm.getPd();
		//判断设备是否是母线对侧刀闸
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeDY)){
			return true;
		}
		CheckMessage cm = new CheckMessage();
		CheckMessage cn = new CheckMessage();
		List<PowerDevice> pd_list= new ArrayList<PowerDevice>();
		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
		List<PowerDevice> mxList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
		List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);
		List<PowerDevice> dzList = new ArrayList<PowerDevice>();
		PowerDevice mx = mxList.get(0);
		PowerDevice sw = swList.get(0);
		//线路开关
		if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
			List<PowerDevice> inoutline = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, "", true, true, true);
			PowerDevice line = inoutline.get(0);
			List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(line);
			for(PowerDevice li : lineList){
				List<PowerDevice> ddList = RuleExeUtil.getDeviceDirectList(li, SystemConstants.SwitchFlowGroundLine);
				for(PowerDevice dd : ddList){
					if(dd.getDeviceStatus().equals("0")){
						pdlist.add(dd);
					}
				}
			}
		}
		//主变开关
		else if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC) || sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
			PowerDevice zb = (PowerDevice)RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, "", true, true, true);
			List<PowerDevice> ddList = RuleExeUtil.getDeviceDirectList(zb, SystemConstants.SwitchFlowGroundLine);
			for(PowerDevice dd : ddList){
				if(dd.getDeviceStatus().equals("0")){
					pdlist.add(dd);
				}
			}
		}
		//所属开关
		if(sw.getDeviceStatus().equals("0")){
			pdlist.add(sw);
		}
		//单母接线
		if(mx.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
			dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate, CBSystemConstants.RunTypeKnifeMX);
			for(PowerDevice dz : dzList){
				if(dz.getDeviceStatus().equals("1")){
					pd_list.add(dz);
				}
			}
		}
		//双母接线
		else if(mx.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
			dzList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);
			dzList.remove(pd);
			PowerDevice dz1 = dzList.get(0);
			PowerDevice dz2 = dzList.get(1);
			if(dz1.getDeviceStatus().equals("1") && dz2.getDeviceStatus().equals("1")){
				pd_list.add(dz1);
				pd_list.add(dz2);
			}
		}
		if(pdlist.size() > 0){
			cm.setPd(pdlist);
			cm.setBottom("1001");
			CBSystemConstants.lcm.add(cm);
		}
		if(pd_list.size() > 0 ){
			cn.setPd(pd_list);
			cn.setBottom("1003");
			CBSystemConstants.lcm.add(cn);
		}
		else{
			return true;
		}
		return true;
	}

}
