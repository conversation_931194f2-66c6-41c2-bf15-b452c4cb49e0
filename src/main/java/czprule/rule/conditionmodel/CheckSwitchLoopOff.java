package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

public class CheckSwitchLoopOff implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		if(!pd.getDeviceType().equals(SystemConstants.Switch)){
			return true;
		}
		String runtype=pd.getDeviceRunType();
		//判断是否为线路开关
		if(runtype.equals(CBSystemConstants.RunTypeSwitchXL)){
			//查找线路
			PowerDevice circuit=null;
			List<PowerDevice> circuits=RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, null, null, CBSystemConstants.RunTypeSideMother, false, true,true, true,"0");
			if(circuits.size()==0){
				return false;
			}else{
				circuit=circuits.get(0);
			}
			//对侧厂站线路
			PowerDevice otheresidecircuit=null;
			List<PowerDevice> othersidecircuits=RuleExeUtil.getLineOtherSideList(circuit);
			if(othersidecircuits.size()==0){
				return false;
			}else{
				otheresidecircuit=othersidecircuits.get(0);
			}
			//对侧厂站开关
			PowerDevice othersideswith=null;
			List<PowerDevice> othersideswiths=RuleExeUtil.getDeviceList(otheresidecircuit, SystemConstants.Switch, null, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true, "0");
			if(runtype.equals(CBSystemConstants.RunModelCableLine)){
				
			}else{
				if(othersideswiths.size()==0){
					return false;
				}else{
					othersideswith=othersideswiths.get(0);
				}
			}
			//查找线路连接母线
			List<PowerDevice> circuitsmx=RuleExeUtil.getDeviceList(pd,null,SystemConstants.MotherLine,null,null,null,false,false,true,true);
			List<PowerDevice> otheresidecircuitmx=RuleExeUtil.getDeviceList(othersideswith,null,SystemConstants.MotherLine,null,null,null,false,false,true,true);
			if(circuitsmx.size()==0||otheresidecircuitmx.size()==0){
				return true;
			}
			//
			PowerDevice cz=CBSystemConstants.getPowerStation(pd.getPowerStationID());
			PowerDevice dccz=CBSystemConstants.getPowerStation(othersideswith.getPowerStationID());
			//判断一侧是否存在电厂
			if(cz.getDeviceType().equals(SystemConstants.PowerFactory)||dccz.getDeviceType().equals(SystemConstants.PowerFactory)){
				//判断是否都通电
				if(circuitsmx.get(0).getDeviceStatus().equals("0")&&otheresidecircuitmx.get(0).getDeviceStatus().equals("0")){
					System.out.println("256");
				}else{
					return true;
				}
			}else{
				//目前只有双回线
				//查找母线连接的线路
				PowerDevice dclpd=null;
				List<PowerDevice> xllist=RuleExeUtil.getDeviceList(circuitsmx.get(0),null,SystemConstants.InOutLine,null,null,null,false,false,true,true);
				for(int i=0;i<xllist.size();i++){
					PowerDevice lpd=xllist.get(i);
				
					List<PowerDevice> dclpds=RuleExeUtil.getLineOtherSideList(lpd);
					if(othersidecircuits.size()==0){
						continue;
					}else{
						dclpd=dclpds.get(0);
					}
				}
				if(dclpd!=null){
					System.out.println("255");
				}else{
					System.out.println("256");
				}
				
			}
		}
		return false;
	}

}
