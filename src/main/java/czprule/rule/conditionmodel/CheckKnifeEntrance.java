package czprule.rule.conditionmodel;

import java.util.List;

import czprule.model.CheckMessage;
import czprule.wordcard.model.CardModel;

/**   
*    
* 创建时间：2014-10-14 下午04:57:36   
* 修改人：FU   
* 修改备注：   一、解析传入参数，生成任务，步骤
			 二、执行指令校核
			（1）单条指令校核设备名称
			（2）设备状态校核
			（3）与操作任务对比
			（4）实行校核规则校核
			三、返回校核结果
* @version    
*    
*/
public class CheckKnifeEntrance {
	/**
	 * @param CardModel为操作票生成数据类型
	 * @return
	 */
	public static List<CheckMessage> check(CardModel cm){
		
		return null;
	}
}
