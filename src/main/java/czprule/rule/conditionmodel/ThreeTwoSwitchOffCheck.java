package czprule.rule.conditionmodel;

import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

import java.util.ArrayList;
import java.util.List;

/**
*  3/2接线开关断开校核算法
*/
public class ThreeTwoSwitchOffCheck implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null){
			return true;
		}
		PowerDevice pd = rbm.getPd();
		if(pd==null){
			return true;
		}
		//识别设备类型判断设备是否是开关
		if(!pd.getDeviceType().equals(SystemConstants.Switch)){
			return true;
		}
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if(CBSystemConstants.RunModelThreeTwo.equals(pd.getDeviceRunModel())) {
			if(!RuleExeUtil.isSwMiddleInThreeSecond(pd)) { //母线侧开关
				List<PowerDevice> swLit = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				if(swLit.size() > 0) {
					if(RuleExeUtil.isSwMiddleInThreeSecond(swLit.get(0)) && swLit.get(0).getDeviceStatus().equals("0")) {//中间开关在合位
						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
						pdlist.add(swLit.get(0));
						CheckMessage cm = new CheckMessage();
						cm.setBottom("1211");
						cm.setPd(pdlist);
						CBSystemConstants.lcm.add(cm);
						return true;
					}
				}
			}
		}
		return true;
	}

}
