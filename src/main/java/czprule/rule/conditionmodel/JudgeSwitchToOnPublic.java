/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 判断开关合上是否符合五防规则
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.conditionmodel;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;

public class JudgeSwitchToOnPublic implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
        if (!pd.getDeviceType().equals(SystemConstants.Switch)) {
        	rbm.getMessageList().add("[" + pd.getPowerDeviceName() + "]不是开关类型!");
			return false;
        }
        if (pd.getDeviceStatus().equals("0")) {   //开关已经处于合位  
            rbm.getMessageList().add("[" + pd.getPowerDeviceName() + "]开关已经处于运行状态!");
 			return false;
        }
		
        return (groundknifeswitch(rbm, pd)&&
        		Switchsidesknife(rbm, pd)&&
        		equipmentstate(rbm, pd));
	}

	/** 接地刀闸判断 */
	public boolean groundknifeswitch(RuleBaseMode rbm, PowerDevice pd) {
		List<PowerDevice> sgList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchFlowGroundLine);
		if (sgList.size() > 0) {
			for (int index = 0; index < sgList.size(); index++) {
				if (sgList.get(index).getDeviceStatus().equals("0")) {
					rbm.getMessageList().add(sgList.get(index) + "未拉开，开关不能合上");
					return false;
				}
			}
		}
		return true;
	}

	/** 开关两侧刀闸判断 */
	public boolean Switchsidesknife(RuleBaseMode rbm, PowerDevice pd) {
		boolean isOk1 = false;
		boolean isOk2 = false;
		// 得到开关第一个端口直接连接的刀闸
		List<PowerDevice> ssList1 = RuleExeUtil.getDeviceDirectByPortList(pd, SystemConstants.SwitchSeparate, "1");
		// 判断开关第一个端口直接连接的刀闸中是否存在合上的
		if(ssList1.size() == 0)
			isOk1 = true;
		else if (ssList1.size() > 0) {
			// 至少有一个被选中
			for (int index = 0; index < ssList1.size(); index++) {
				if (ssList1.get(index).getDeviceStatus().equals("0")) {
					isOk1 = true;
					break;
				} else {
					continue;
				}
			}
		}else
			isOk1 = true;
		// 得到开关第二个端口直接连接的刀闸
		List<PowerDevice> ssList2 = RuleExeUtil.getDeviceDirectByPortList(pd, SystemConstants.SwitchSeparate, "2");
		// 判断开关第二个端口直接连接的刀闸中是否存在合上的
		if(ssList2.size() == 0)
			isOk2 = true;
		else if (ssList2.size() > 0) {
			// 至少有一个被选中
			for (int index = 0; index < ssList2.size(); index++) {
				if (ssList2.get(index).getDeviceStatus().equals("0")) {
					 isOk2 = true;
					break;
				} else {
					continue;
				}
			}
		}
		if (isOk1 && isOk2) {
			return true;
		} else {
			rbm.getMessageList().add("[" + pd.getPowerDeviceName() + "]关联刀闸未合上，开关不能合上");
			return false;
		}
	}

	/** 设备处于检修状态判断 */
	public boolean equipmentstate(RuleBaseMode rbm, PowerDevice pd) {
		List<PowerDevice> maintenancestateList = RuleExeUtil.getDeviceList(pd, "", SystemConstants.PowerTransformer, false, true, true);
		if (maintenancestateList.size() > 0) {
			for (int index = 0; index < maintenancestateList.size(); index++) {
				// 判断母线是否处于检修状态
				if (maintenancestateList.get(index).getDeviceStatus().equals("3")) {
					rbm.getMessageList().add(maintenancestateList.get(index) + "处于检修状态，开关不能合上");//
					return false;
				}
			}
		}
		return true;
	}
	
//	public boolean judgeSwitchOrder(RuleBaseMode rbm, PowerDevice pd) {
//		//判断开关是否为中间的开关
//		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)) {
//			boolean b=RuleExeUtil.isSwMiddleInThreeSecond(pd);
//			if(b){
//				List<PowerDevice> sws = RuleUtil.getLinkedDeviceByType3(pd, SystemConstants.Switch);
//				for (PowerDevice sw : sws) {
//					if(!sw.getDeviceStatus().equals("0")){
//						rbm.getMessageList().add("["+sw+"在分位，不能合上["+pd+"]");
//						return false;
//					}
//				}
//			}
//		}
//		return true;
//	}
}
