package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-10 下午06:00:24   
* 修改人：FU   
* 修改备注：   冷倒热倒规则算法
* @version    
*    
*/
public class judgeLDRDPublic implements RulebaseInf{

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null){
			return true;
		}
		List<CheckMessage> cml=rbm.getMessage().get("sbxx");
		if(cml==null){
			cml=new ArrayList<CheckMessage>();
			rbm.getMessage().put("sbxx", cml);
		}
		//目标状态
		String rbmendstate=rbm.getEndState();
		//设备
		PowerDevice pd=rbm.getPd();
		//判断是否刀闸
		if(pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
			return true;
		}
		//搜索母线
		PowerDevice mx=null;
		List<PowerDevice> mxlist=RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, null, null, null, false, true, true,true);
		if(mxlist.size()==0){
			return true;
		}
		//母线是否是双母
		mx=mxlist.get(0);
		if(!mx.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
			return true;
		}
		//搜索刀闸连接的开关
		PowerDevice kg=null;
		List<PowerDevice> kglist=RuleExeUtil.getKnifeRelateSwitch(pd);
		if(kglist.size()>0){
			kg=kglist.get(0);
			if(kg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
				return true;
			}else{
				if(kg.getDeviceStatus().equals("0")){
					if(rbmendstate.equals("0")){
						//对侧刀闸
						PowerDevice otherknife=null;
						List<PowerDevice> otherknifelist=RuleExeUtil.getSwitchRelateKnife(kg);
						for(int i=0;i<otherknifelist.size();i++){
							PowerDevice dz=otherknifelist.get(i);
							if(dz.equals(pd)){
								otherknifelist.remove(dz);
								i--;
							}
							if(dz.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
								otherknifelist.remove(dz);
								i--;
							}
						}
						if(otherknifelist.size()>0){
							otherknife=otherknifelist.get(0);
						}
						if(otherknife!=null){
							if(otherknife.getDeviceStatus().equals("0")){
								//母联开关或刀闸
								PowerDevice ml=null;
								List<PowerDevice> mllist=RuleExeUtil.getMLSwitchList(mx);
								if(mllist.size()>0){
									ml=mllist.get(0);
									if(ml.getDeviceStatus().equals("0")){
										return true;
									}else{
//										String sbtx="热倒方式下，母联开关及其附属刀闸不在合位，母线侧刀闸不能合上";
//										CheckMessage cm=new CheckMessage();
//										cm.setMessage(sbtx);
//										cm.setPd(mx);
//										cm.setBottom("2");
//										cml.add(cm);
									}
								}
							}else{
								return true;
							}
						}
					}else{
						return true;
					}
				}else{
					if(rbmendstate.equals("0")){
//						String sbtx="冷倒方式下，请先将合位母线侧刀拉开，再合上分位母线侧刀闸";
//						CheckMessage cm=new CheckMessage();
//						cm.setMessage(sbtx);
//						cm.setPd(mx);
//						cm.setBottom("2");
//						cml.add(cm);
					}else{
						return true;
					}
				}
			}
		}
		
		return true;
	}

}
