package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
/**
 * 
 * 拉开开关失电提醒
 * <AUTHOR>
 *
 */
public class CheckSwitchOffWarn implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			System.out.println(this.getClass().getName() + ">>Null");
			return false;
		}
		// 判断是否为线路开关
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
			//判断查找到的线路开关连接的线路是否为终端线路（模型中不存在其他线路侧）
			List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.InOutLine);
			if(list.size()>0){
				Map<PowerDevice, String> stationlines = QueryDeviceDao
						.getPowersLineByLine(list.get(0));
						if(stationlines.size()<2){
							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
							pdlist.add(pd);
							CheckMessage cm=new CheckMessage();
							cm.setPd(pdlist);
							cm.setBottom("251");
							CBSystemConstants.lcm.add(cm);
						}
			}
			
		}
		return true;
	}
	
}
