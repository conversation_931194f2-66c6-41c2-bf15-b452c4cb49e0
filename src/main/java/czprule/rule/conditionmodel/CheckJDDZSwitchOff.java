package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**
 * 接地电阻开关拉开规则
 * 
 * <AUTHOR>
 * 
 */
public class CheckJDDZSwitchOff implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		if (rbm == null) {
			return false;
		}
		// 设备目标状态
		String rbmendstate = rbm.getEndState();
		PowerDevice pd = rbm.getPd();
		if (pd == null) {
			return false;
		}
		/**
		 * 识别设备类型判断设备是否是接地电阻 开关
		 */
		if (!pd.getDeviceType().equals(SystemConstants.Switch)) {
			return true;
		}
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchJDB)) {
			// 搜索与开关最近的母线
			PowerDevice mx = null;
			List<PowerDevice> mxlist = RuleExeUtil.getDeviceList(pd,
					SystemConstants.MotherLine, null, null, null, false, true,
					true, true);
			if (mxlist.size() > 0) {
				mx = mxlist.get(0);
			}
			if (mx != null) {
				// 搜索是否还有其他接地电阻
				List<PowerDevice> jddzlist = RuleExeUtil.getDeviceList(mx,
						SystemConstants.Switch, null,
						CBSystemConstants.RunTypeSwitchJDB, null, false, false,
						false, false);
				int n = 0;
				// 母线上是否存在接地电阻
				if (jddzlist.size() > 1) {
					int i = 0;
					// 是否投运  
					for (PowerDevice device : jddzlist) {
						if (device.getDeviceStatus().equals("0")) {
							i++;
						}
					}
					if (i<2&&!rbmendstate.equals("0")) {
						List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
						pdlist.add(mx);
						CheckMessage cm = new CheckMessage();
						cm.setBottom("154");
						cm.setPd(pdlist);
						CBSystemConstants.lcm.add(cm);
						return true;
					}
				} else {
					List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
					pdlist.add(mx);
					CheckMessage cm = new CheckMessage();
					cm.setPd(pdlist);
					cm.setBottom("154");
					CBSystemConstants.lcm.add(cm);
					return true;
				}
			}
		}
		return true;
	}
}
