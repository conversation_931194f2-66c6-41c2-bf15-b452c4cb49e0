package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.List;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**   
*    
* 创建时间：2014-10-11 上午11:17:27   
* 修改人：FU   
* 修改备注：主变开关操作防误算法   
* @version    
*    
*/
public class judgeZBKGPublic implements RulebaseInf{

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null){
			return true;
		}
		List<CheckMessage> cml=rbm.getMessage().get("sbxx");
		if(cml==null){
			cml=new ArrayList<CheckMessage>();
			rbm.getMessage().put("sbxx", cml);
		}
		//目标状态
		String rbmendstate=rbm.getEndState();
		//设备
		PowerDevice pd=rbm.getPd();
		//主变开关
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)||pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
		}
		return true;
	}

}
