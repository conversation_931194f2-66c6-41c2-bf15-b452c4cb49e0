package czprule.rule.conditionmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.baserule.SupplyElecAlgorithm;
import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

/**   
*    
* 创建时间：2014-10-16 上午11:50:40   
* 修改人：FU   
* 修改备注：   禁止带接地合刀闸规则
* @version    
*    
*/
public class CheckBanJDKnifeOn implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		PowerDevice pd = rbm.getPd();
		SupplyElecAlgorithm sea = new SupplyElecAlgorithm();
		PowerDevice pDevice = null;
		// 一、 送电范围内不能存在合闸的接地刀闸
		List<PowerDevice> devs = sea.execute(pd);
		List<PowerDevice> pdlist=new ArrayList<PowerDevice>();
		CheckMessage cm=new CheckMessage();
		for (int i = 0; i < devs.size(); i++) {
			pDevice = devs.get(i);
			if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXL) && !pd.getPowerStationID().endsWith(pDevice.getPowerStationID()))
				continue;//北京 西单站 可以在其他站地刀合上情况下，合上本侧线路刀闸
			if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePT)
					&&RuleExeUtil.getDeviceDirectList(pDevice, SystemConstants.InOutLine).size()>0){
				continue;//操作PT刀闸不需要线路地刀在合位
			}
			if (pDevice.getDeviceStatus().equals("0")) {
				
				// 当前范围内有接地刀闸和接地线路，先判断接地刀闸和接地线路是否处于合位，
				// 如果任意一个处于合位则不允许合闸，如果没有则再判断直连设备状态
				pdlist.add(pDevice);
				pDevice.setActionWord(pd.getPowerStationName()+CZPService.getService().getDevName(pd));

			}
		}
		// 二、 如果刀闸所连的母线上有处于合位的地刀时，合此刀闸，将闭锁
		List<PowerDevice> mxLit = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
		for (int i = 0; i < mxLit.size(); i++) {
			PowerDevice mxDevice = mxLit.get(i);
			List<PowerDevice> ddLit = RuleExeUtil.getDeviceDirectList(mxDevice, SystemConstants.SwitchFlowGroundLine);
			for (int x = 0; x < ddLit.size(); x++) {
				PowerDevice ddDevice = ddLit.get(x);
				if (ddDevice.getDeviceStatus().equals("0")) {
					
					pdlist.add(ddDevice);
				}
			}
				
		}
	 
		if(pdlist.size()>0){
			cm.setPd(pdlist);
			//刀闸所在线路已接地，禁止刀闸合闸
			cm.setBottom("135");
			CBSystemConstants.lcm.add(cm);
		}

		return true;
	}

}
