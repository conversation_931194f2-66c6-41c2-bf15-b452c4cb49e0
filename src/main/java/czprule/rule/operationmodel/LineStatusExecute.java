package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;








import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;

public class LineStatusExecute implements RulebaseInf {
	public static PowerDevice fhckg;//220kV负荷侧（其实是电源侧，220设置电源负荷侧的时候取的相反的）厂站开关，用来出“同期”合上开关术语
	
	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		String beginStatus =rbm.getBeginStatus();
		String endStatus = rbm.getEndState();
		PowerDevice pd = rbm.getPd();
		
		//获取各侧站内线路
		PowerDevice lineSource = null;
//		List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(pd);
		
		String paraID=pd.getPowerDeviceID();
		
		if(pd.getPowerVoltGrade()==110){
			List<PowerDevice> mxList =RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer
					, true, true, true);
			if(mxList.size()==0){
				List<PowerDevice> otherLines =RuleExeUtil.getLineOtherSideList(pd);
				for(PowerDevice line:otherLines){
					List<PowerDevice> mxs =RuleExeUtil.getDeviceList(line, SystemConstants.MotherLine, SystemConstants.PowerTransformer
							, true, true, true);
					if(mxs.size()>0){
						paraID=line.getPowerDeviceID();
						break;
					}
					
				}
			}
		}
		lineSource = CBSystemConstants.LineSource.get(paraID);
		
		List<PowerDevice> lineLoad = CBSystemConstants.LineLoad.get(paraID);
		
		if(lineLoad==null){
			return true;
		}
		lineLoad = orderByLoadline(lineLoad);
				

		List<PowerDevice> dckgs =null;
		
		boolean result =false;
		
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		
		if(beginStatus.equals("0")){
			result = xl0to1(lineSource, lineLoad);
			if(!result)
				return result;
		}else if(beginStatus.equals("1") && endStatus.equals("2")) { //热备用转冷备用
			xl1to2(lineSource, lineLoad);
		}else if(beginStatus.equals("2") && endStatus.equals("3")) { //冷备用转检修
			xl2to3(lineSource, lineLoad);
		}
		else if(beginStatus.equals("2") && endStatus.equals("1")) { //冷备用转热备用
			result = xl2to1(lineSource, lineLoad);
			if(!result)
				return result;
		}else if(beginStatus.equals("3") && endStatus.equals("2")) { //检修转冷备用
			result = xl3to2(lineSource, lineLoad);
			if(!result)
				return result;
		}
		else if(endStatus.equals("0")) { //热备用转运行
			result = xl1to0(lineSource, lineLoad);
		}
		
	
		

		
		return true;
	}

	/**
	 * 线路运行转热备用
	 */
	public boolean xl0to1(PowerDevice lineSource,List<PowerDevice> lineLoad){
		RuleBaseMode rbm = new RuleBaseMode();
		//线路倒负荷及断开线路对侧开关
		for(PowerDevice line : lineLoad) {
			if(line.getPowerVoltGrade()<220){
				boolean res = loadLine(line);
				if(!res){
					return res;
				}
			}
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			this.sortListBySwMiddle(swList);
			for(PowerDevice sw:swList){
				if(!sw.getDeviceStatus().equals("1")){
					RuleExeUtil.deviceStatusExecuteJB(sw, sw.getDeviceStatus(), "1");
				}
			}
//			if(sw.size()>0&&sw.get(0).getDeviceStatus().equals("0")){
//				if(!sw.get(0).getDeviceStatus().equals("1")){
//					RuleExeUtil.deviceStatusExecuteJB(sw.get(0), sw.get(0).getDeviceStatus(), "1");
//				}
//
//			}
//			new SwitchExecute().execute(new RuleBaseMode(lineSource, "0", "1", "T"));
		}
		
		//断开线路开关
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		this.sortListBySwMiddle(swList);
		for(PowerDevice sw:swList){
			if(!sw.getDeviceStatus().equals("1")){
				RuleExeUtil.deviceStatusExecuteJB(sw, sw.getDeviceStatus(), "1");
			}
		}
//		new SwitchExecute().execute(new RuleBaseMode(lineSource, "0", "1", "S"));
		
		
		return true;
	}
	/**
	 * 线路热备用转运行
	 */
	public boolean xl1to0(PowerDevice lineSource,List<PowerDevice> lineLoad){
				
		List<PowerDevice> swList1 = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch,
				SystemConstants.PowerTransformer, true, true, true);
		this.sortListBySwMiddle(swList1);
		Collections.reverse(swList1);
		for(PowerDevice sw:swList1){
			if(!sw.getDeviceStatus().equals("0")){
				RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "0");
			}
		}
		
		
		for(PowerDevice line : lineLoad) {
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch,
					SystemConstants.PowerTransformer, true, true, true);
			this.sortListBySwMiddle(swList);
			Collections.reverse(swList);
			for(PowerDevice sw:swList){
				if(!sw.getDeviceStatus().equals("0")&&(CBSystemConstants.isMultiTicket||
						CBSystemConstants.getLineTagStatus(sw).equals("-1")||CBSystemConstants.getLineTagStatus(sw).equals("0"))){
					RuleExeUtil.deviceStatusExecute(sw, sw.getDeviceStatus(), "0");
				}
			}
			if(swList.size()>0&&swList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				LineExecuteLoad loadExe = new LineExecuteLoad();
				loadExe.executeLoadBack(line);
			}
		}
		
//		//合上线路开关
//		PowerDevice sourceSW = RuleExeUtil.getDeviceSwitch(lineSource);
//		if(sourceSW!=null){
//			if(!sourceSW.getDeviceStatus().equals("0")){
//				RuleExeUtil.deviceStatusChange(sourceSW, sourceSW.getDeviceStatus(), "0");
//				
//			}
//		}
//	
//		for(PowerDevice line : lineLoad) {
//			
//			PowerDevice sw = RuleExeUtil.getDeviceSwitch(line);
//			
//			
//			if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)||sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
//				if(line.getPowerVoltGrade()<220){
//					LineExecuteLoad loadExe = new LineExecuteLoad();
//				
//					if(line.getDeviceStatus().equals("0"))
//						continue;
//					if (!loadExe.executeLoadBack(line)) {
//						return false;
//					}
//				}
//			}
//			else if(sw!=null&&sw.getDeviceStatus().equals("1")){
//				if(lineSource.getPowerVoltGrade()!=110){
//					if(!sw.getDeviceStatus().equals("0")){
//						
//						fhckg=null;//初始化
//						if(sw.getPowerVoltGrade()==220){
//							fhckg = sw;
//						}
//
//						RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "0");
//						
//						
//						if(line.getPowerVoltGrade()<220){
//							LineExecuteLoad loadExe = new LineExecuteLoad();
//						
//							if(line.getDeviceStatus().equals("0"))
//								continue;
//							if (!loadExe.executeLoadBack(line)) {
//								return false;
//							}
//
//							
//
//						}
//						
//					
//					}
//				}
//				else{
//					if(lineLoad.size()==1){
//						RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "0");
//					}
//				}
//				
//				
//			}
//			
//			
//		}
		

				
		
		return true;
	}
	
	
	
	
	/**
	 * 线路热备用转冷备用
	 */
	public boolean xl1to2(PowerDevice lineSource,List<PowerDevice> lineLoad){

		
		List<PowerDevice> lineList = new ArrayList<PowerDevice>();
		lineList.add(lineSource);
		lineList.addAll(lineLoad);
	
		for(PowerDevice line : lineList) {
			PowerDevice xldz = null;
			List<PowerDevice> dzList = RuleExeUtil.getDeviceList(line, SystemConstants.SwitchSeparate,
					SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXL+","+CBSystemConstants.RunTypeKnifePT,"",false,true, true, true);//搜索线路关联刀闸
			if(dzList.size()>0){
				for(PowerDevice dz : dzList){
					if(dz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXL)){
						xldz = dz;
					}
					
					if(dz.getDeviceStatus().equals("0")){//合上线路侧刀闸
						RuleExeUtil.deviceStatusChange(dz, "0", "1");
					}
				}
			}
			
			List<PowerDevice> onSW =  new ArrayList<PowerDevice>();//选择合上的开关

			List<PowerDevice> swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch,
					SystemConstants.PowerTransformer, true, true, true);//搜索线路开关
			this.sortListBySwMiddle(swList);
			for(PowerDevice sw:swList){
		
				if(("0").equals(CBSystemConstants.LineTagStatus.get(sw))){//选择器选择了合线路开关的
					onSW.add(sw);
				}else{
					List<PowerDevice> swdzList = RuleExeUtil.getDeviceList(sw, SystemConstants.SwitchSeparate,
							SystemConstants.PowerTransformer, true, true, true);//搜索开关刀闸
					for(int i=0;i<swdzList.size();i++){
						if(!swdzList.get(i).getDeviceStatus().equals("0")){
							swdzList.remove(i);
							i--;
						}
					}
					this.sortDzListByMXDZ(swdzList);
					for(PowerDevice dz:swdzList){
//							if(dz.getDeviceStatus().equals("0")){
							RuleExeUtil.deviceStatusChange(dz, "0", "1");//拉开开关刀闸
//							}
					}
				}
			}
			
			//合上选择的开关
			Collections.reverse(onSW);
			for(PowerDevice sw:onSW){
				if(xldz!=null&&xldz.getDeviceStatus().equals("1")){
					RuleExeUtil.deviceStatusChange(sw, "1", "0");//合上线路开关
				}
			}
		}
		for(PowerDevice line:lineList){
			if(line.getDeviceStatus().equals("1")){
				RuleExeUtil.deviceStatusSet(line, "1", "2");
			}
		}
		
		return true;
	}
	
	
	/**
	 * 线路冷备用转热备用
	 */
	public boolean xl2to1(PowerDevice lineSource,List<PowerDevice> lineLoad){
		
		List<PowerDevice> lineList = new ArrayList<PowerDevice>();
		
		lineList.addAll(lineLoad);
		lineList.add(lineSource);
		for(PowerDevice line : lineList) {
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(line, SystemConstants.Switch,
					SystemConstants.PowerTransformer, true, true, true);//搜索线路开关
			this.sortListBySwMiddle(swList);
			for(PowerDevice sw:swList){
				if(sw.getDeviceStatus().equals("0")){
					RuleExeUtil.deviceStatusChange(sw, "0", "1");//断开线路开关
				}
			}
			
			Collections.reverse(swList);
			for(PowerDevice sw:swList){
				if(sw.getDeviceStatus().equals("3")){
					RuleExeUtil.deviceStatusChange(sw, "3", "2");
				}
				RuleExeUtil.deviceStatusChange(sw, "2", "1");//开关转热备用
//				List<PowerDevice> swdzList = RuleExeUtil.getDeviceList(sw, SystemConstants.SwitchSeparate,
//						SystemConstants.PowerTransformer, true, true, true);//搜索开关刀闸
//				this.sortDzListByMXDZ(swdzList);
//				Collections.reverse(swdzList);
//				for(PowerDevice dz:swdzList){
//					if(dz.getDeviceStatus().equals("1")){
//						RuleExeUtil.deviceStatusChange(dz, "1", "0");//合上开关刀闸
//					}
//				}
				
			}
			
			
			

			List<PowerDevice> dzList = RuleExeUtil.getDeviceList(line, SystemConstants.SwitchSeparate,
					SystemConstants.PowerTransformer, true, true, true);//搜索线路关联刀闸
			
			for(PowerDevice dz : dzList){
				if(dz.getDeviceStatus().equals("1")){//合上线路侧刀闸
					RuleExeUtil.deviceStatusChange(dz, "1", "0");
				}
			}
		}
		
		//
		for(PowerDevice line:lineList){
			if(line.getDeviceStatus().equals("2")){
				RuleExeUtil.deviceStatusSet(line, "2", "1");
			}
		}

		return true;
	}
	
	/**
	 * 线路冷备用转检修
	 */
	public boolean xl2to3(PowerDevice lineSource,List<PowerDevice> lineLoad){
	
		
		List<PowerDevice> didao = RuleExeUtil.getDeviceDirectList(lineSource,SystemConstants.SwitchFlowGroundLine);
		if(didao.size()>0){
			for(PowerDevice dd:didao){
				RuleExeUtil.deviceStatusChange(dd, "1", "0");
				
			}
		}else{
			RuleExeUtil.deviceStatusSet(lineSource, "2", "3");
			
		}
		
		//加入电源侧放最后面
		lineLoad = orderByLoadlineDesc(lineLoad);
		//对线路冷备用转检修
		for(int i=0;i<lineLoad.size();i++){
			List<PowerDevice> didaoLoad = RuleExeUtil.getDeviceDirectList(lineLoad.get(i), SystemConstants.SwitchFlowGroundLine);
			if(didaoLoad.size()>0){
				for(PowerDevice dd:didaoLoad){
					RuleExeUtil.deviceStatusChange(dd, "1", "0");
					
				}
			}else{
				RuleExeUtil.deviceStatusSet(lineLoad.get(i), "2", "3");
				
			}
		
			//线路开关根据选择器状态执行
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineLoad.get(i), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			for(PowerDevice sw:swList){
				if("3".equals(CBSystemConstants.LineTagStatus.get(sw))){
					RuleExeUtil.deviceStatusExecuteJB(sw, "2", "3");
				}
			}

		}
		
		
		

		//线路开关根据选择器状态执行
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		for(PowerDevice sw:swList){
			if("3".equals(CBSystemConstants.LineTagStatus.get(sw))){
				RuleExeUtil.deviceStatusExecuteJB(sw, "2", "3");
			}
		}
		return true;
	}
	/**
	 * 线路检修转冷备用
	 */
	public boolean xl3to2(PowerDevice lineSource,List<PowerDevice> lineLoad){
		
		//如果开关在检修，先转冷备用
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineSource, SystemConstants.Switch, SystemConstants.PowerTransformer,
				true, true, true);
		for(PowerDevice sw:swList){
			if(sw.getDeviceStatus().equals("3")){
				RuleExeUtil.deviceStatusChange(sw, "3", "2");
			}
		}
		
		
		//电源侧线路检修转冷备用
		List<PowerDevice> didaoDY = RuleExeUtil.getDeviceList(lineSource,
				SystemConstants.SwitchFlowGroundLine, SystemConstants.PowerTransformer,true, true, true);
		for(PowerDevice dd:didaoDY){
			RuleExeUtil.deviceStatusChange(dd, "0", "1");
			//关联设备状态设置
			
		}
		
		
		lineLoad =orderByLoadlineDesc(lineLoad);
		//对线路检修转冷备用
		for(int i=0;i<lineLoad.size();i++){
			//如果开关在检修，先转冷备用
			List<PowerDevice> swLoadList = RuleExeUtil.getDeviceList(lineLoad.get(i), SystemConstants.Switch, SystemConstants.PowerTransformer,
					true, true, true);
			for(PowerDevice sw:swLoadList){
				if(sw.getDeviceStatus().equals("3")){
					RuleExeUtil.deviceStatusChange(sw, "3", "2");
				}
			}
			List<PowerDevice> didao = RuleExeUtil.getDeviceList(lineLoad.get(i),
					SystemConstants.SwitchFlowGroundLine, SystemConstants.PowerTransformer,true, true, true);
			if(didao.size()>0){
				for(PowerDevice dd:didao){
					RuleExeUtil.deviceStatusChange(dd, "0", "1");
					
				}
			}else{
				RuleExeUtil.deviceStatusSet(lineLoad.get(i), "3", "2");
				
			}


		}
		
		return true;
	}
	/**
	 * 对侧线路按选择排序，无选择则按习惯排序
	 */
	public List<PowerDevice> orderByLoadline(List<PowerDevice> line){
		//线变组放前面
		for(int i=0;i<line.size();i++){
			if(line.get(i)!=null){
				PowerDevice lineSwitch = RuleExeUtil.getDeviceSwitch(line.get(i));
				//识别开关是否线变组接线的主变开关
				if(lineSwitch!=null &&  lineSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
					line.add(0,line.remove(i));
				}
			}

		}
		return line;
	}
	
	/**
	 * （主变放后面）
	 */
	public List<PowerDevice> orderByLoadlineDesc(List<PowerDevice> line){
		//线变组放前面
		for(int i=0;i<line.size();i++){
			PowerDevice lineSwitch = RuleExeUtil.getDeviceSwitch(line.get(i));
			//识别开关是否线变组接线的主变开关
			if(lineSwitch!= null && lineSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
				line.add(line.size()-1,line.remove(i));
			}
			
		}
		
		return line;
	}
	
	
	
	/**
	 * 线路倒负荷
	 * @param line
	 * @param rbm
	 * @return
	 */
	public boolean loadLine(PowerDevice line) {
		boolean result = true;
		if(!line.getDeviceStatus().equals("0"))
			return true;
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(line, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, true, true);
		if(tfList.size() > 0) { //如果线路直接连接主变，执行主变的主变状态转换
			for (PowerDevice tf : tfList) {
				result = loadTransformer(line, tf);
				if(!result)
					return false;
			}
		}
		else { //如果线路连接了母线，对母线倒负荷
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(line, SystemConstants.MotherLine, SystemConstants.PowerTransformer,"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
			for (PowerDevice ml : mlList) {
				if(!RuleExeUtil.isDeviceOperate(ml)&& (!ml.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)) && (!ml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) ) {
					result = loadMotherLine(line, ml);
					if(!result)
						return false;
				}
			}
		}
		return true;
	}
	
	/**
	 * 主变倒负荷
	 * @param srcLine
	 * @param tf
	 * @return
	 */
	public boolean loadTransformer(PowerDevice srcLine, PowerDevice tf) {
		boolean result = true;
		PowerDevice lineSwitch = RuleExeUtil.getDeviceSwitch(srcLine);
		//查找主变其他可能的电源点
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(tf, lineSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT, "", false, false, false, true, true);
		for(Iterator it = swList.iterator();it.hasNext();) {
			PowerDevice mlsw = (PowerDevice)it.next();
			List<PowerDevice> srcList = RuleExeUtil.getDeviceList(mlsw, lineSwitch, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "", false, false, false, true, true);
			if(srcList.size() == 0)
				it.remove();
		}
		
		PowerDevice dev = null; 
		if(swList.size() == 1)
			dev = swList.get(0);
		else if(swList.size() > 1) {
			EquipCheckChoose ecc = new EquipCheckChoose(SystemConstants.getMainFrame(), true, swList, "请选择["+CBSystemConstants.getPowerStation(srcLine.getPowerStationID()).getPowerStationName()+"]["+srcLine.getPowerDeviceName()+"]停电前合上的开关");
			if(ecc.getChooseEquip()!=null && ecc.getChooseEquip().size() > 0) {
				dev = ecc.getChooseEquip().get(0);
			}
		}
		if(dev != null)
			RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "0");
		else{
			String endstate =CBSystemConstants.getCurRBM().getEndState();
			if(endstate.equals("3")){
				endstate="2";
			}
			result = RuleExeUtil.deviceStatusChange(tf, tf.getDeviceStatus(), endstate);
		}
		
		return result;
	}
	
	/**
	 * 母线倒负荷
	 * @param srcLine
	 * @param ml
	 * @return
	 */
public boolean loadMotherLine(PowerDevice srcLine, PowerDevice ml) {
		
		List<PowerDevice> lineList = null;
		
		//查找可以代供的线路
		PowerDevice xlSwitch = RuleExeUtil.getDeviceSwitch(srcLine);
		lineList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "", false, false, false, true);
		for(Iterator it = lineList.iterator();it.hasNext();) {
			PowerDevice ln = (PowerDevice)it.next();
			if(String.valueOf(RuleExeUtil.judgeLineFlow(ln)).equals("1"))
				return true; //母线上存在其他进线,不需要倒负荷
			else if(String.valueOf(RuleExeUtil.judgeLineFlow(ln)).equals("2"))
				it.remove();
		}
		
		if(lineList.size() > 0 && ml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
			return true;
		
		List<PowerDevice> swList = new ArrayList<PowerDevice>();
		
		//查找热备用的线路开关和母联开关
		List<PowerDevice> swxlList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
		for(PowerDevice sw : swxlList) {
			if(sw.getDeviceStatus().equals("1"))
				swList.add(sw);
			else if(sw.getDeviceStatus().equals("0")) {
				List<PowerDevice> lnlist = RuleExeUtil.getSwitchLoadLine(sw);
				if(lnlist.size() == 1) {
					List<PowerDevice> otherLinelist = RuleExeUtil.getLineOtherSideList(lnlist.get(0));
					for(PowerDevice otherLine : otherLinelist) {
						PowerDevice otherSw = RuleExeUtil.getDeviceSwitch(otherLine);
						if(otherSw != null && otherSw.getDeviceStatus().equals("1"))
							swList.add(otherSw);
					}
				}
			}
		}
		
		
		List<PowerDevice> swmlList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, false, true);
		for(int i=0;i<swmlList.size();i++){//母联开关排除母线ml，搜到的另一端有线路开关或者母联开关，才有可能是可转电的相关母联开关
			if(RuleExeUtil.getDeviceList(swmlList.get(i), ml, SystemConstants.Switch, SystemConstants.PowerTransformer,
					CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchXL, "", false, true, false, true).size()==0){
				swmlList.remove(i);
				i--;
			}
		}
		
		if(swmlList.size()==1&&!swmlList.get(0).getDeviceStatus().equals("0")){
			RuleExeUtil.deviceStatusExecute(swmlList.get(0), swmlList.get(0).getDeviceStatus(),"0");
		}else if(swmlList.size()>0){
			PowerDevice otherMX = RuleUtil.getAnotherMotherLine(swmlList.get(0), ml);
			List<PowerDevice> otherswxlList = RuleExeUtil.getDeviceList(otherMX, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
			for(PowerDevice sw : otherswxlList) {
				if(!sw.getDeviceStatus().equals("0")){//线路开关非运行，合线路开关
					RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(),"0");
				}else if(!swmlList.get(0).getDeviceStatus().equals(("0"))){//线路开关运行，母联开关非运行合母联开关
					RuleExeUtil.deviceStatusChange(swmlList.get(0), swmlList.get(0).getDeviceStatus(),"0");
				}
		
			}
		}else if(swmlList.size()==0){//没有母联开关，则搜是否有其他线路可以转供
			List<PowerDevice> pdList=RuleExeUtil.getDeviceList(ml, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
			pdList.remove(srcLine);
			List<PowerDevice> switchList=new ArrayList<PowerDevice>();
			for(PowerDevice xlpd:pdList){
				switchList.addAll(RuleExeUtil.getDeviceList(xlpd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true));
			} 
			List<String> defaultStatusList = new ArrayList<String>();
			List<String> expStatusList = new ArrayList<String>();
			for(PowerDevice sw : switchList) {
				defaultStatusList.add(sw.getDeviceStatus());
			}
			expStatusList.add("2");
			expStatusList.add("3");
			Map tagStatusMap = new HashMap();
			if(CBSystemConstants.isCurrentSys&&switchList.size()>0) {
				EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, switchList, defaultStatusList,expStatusList, "如需转电请选择线路开关要转换的状态：");
				tagStatusMap=dialog.getTagStatusMap();
				if(tagStatusMap.size()>0){
					List<Map.Entry<PowerDevice,String>> list = new ArrayList<Map.Entry<PowerDevice,String>>(tagStatusMap.entrySet());
			        for(Map.Entry<PowerDevice,String> entry:list){
			    		if(!entry.getKey().getDeviceStatus().equals(entry.getValue())){//改变了才做操作
							RuleExeUtil.deviceStatusExecuteJB(entry.getKey(), entry.getKey().getDeviceStatus(),entry.getValue());
						}
					}
				}
			}else if(switchList.size()>0&&("1").contains(switchList.get(0).getDeviceStatus())){
				RuleExeUtil.deviceStatusExecuteJB(switchList.get(0), switchList.get(0).getDeviceStatus(), "0");
			}
			
		}
//		//合环转电选择框
//		List<PowerDevice> hhzdList = new ArrayList<PowerDevice>();
//		if(ml.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)&&swmlList.size()>0){
//			PowerDevice otherMX = RuleUtil.getAnotherMotherLine(swmlList.get(0), ml);
//			List<PowerDevice> otherswxlList = RuleExeUtil.getDeviceList(otherMX, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
//			for(PowerDevice sw : otherswxlList) {
//				if(sw.getDeviceStatus().equals("0")||sw.getDeviceStatus().equals("1"))
//					hhzdList.add(sw);
//			}
//		}
//		List<String> defaultStatusList = new ArrayList<String>();
//		for(PowerDevice hhzd : hhzdList) {
//			defaultStatusList.add("0");
//		}
//		
//		if(hhzdList.size()>0){
//			Map tagStatusMap = new HashMap();
//			if(CBSystemConstants.isCurrentSys) {
//				EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, hhzdList, defaultStatusList, "如需转电请选择线路开关要转换的状态：");
//				tagStatusMap=dialog.getTagStatusMap();
//			}
////			if(tagStatusMap.size()==0){
////				return false;
////			}
//			if(tagStatusMap.size()>0){
//				   List<Map.Entry<PowerDevice,String>> list = new ArrayList<Map.Entry<PowerDevice,String>>(tagStatusMap.entrySet());
//			        //然后通过比较器来实现排序
//			        Collections.sort(list,new Comparator<Map.Entry<PowerDevice,String>>() {
//			            //升序排序
//			            public int compare(Entry<PowerDevice, String> o1,
//			                    Entry<PowerDevice, String> o2) {
//			                return o1.getValue().compareTo(o2.getValue());
//			            }
//			            
//			        });
//					
//			        for(Map.Entry<PowerDevice,String> entry:list){
//			    		if(!entry.getKey().getDeviceStatus().equals(entry.getValue())){//改变了才做操作
//							RuleExeUtil.deviceStatusChange(entry.getKey(), entry.getKey().getDeviceStatus(),entry.getValue());
//						}
//					}
//			}
//		}
//	
//	 
//		
//		
//		
//		
//		for(PowerDevice sw : swmlList) {
//			if(sw.getDeviceStatus().equals("1"))
//				swList.add(sw);
//		}
//		
//		PowerDevice dev = null;
//		
//		if(swList.size() == 1)
//			dev = swList.get(0);
//		else if(swList.size() > 1) {
//			EquipCheckChoose ecc = new EquipCheckChoose(SystemConstants.getMainFrame(), true, swList, "请选择["+CBSystemConstants.getPowerStation(srcLine.getPowerStationID()).getPowerStationName()+"]["+srcLine.getPowerDeviceName()+"]停电前合上的开关");
//			if(ecc.getChooseEquip()!=null && ecc.getChooseEquip().size() > 0) {
//				dev = ecc.getChooseEquip().get(0);
//			}
//		}
//		if(dev != null)
//			RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "0");
//		else{
//			return false;
//		}
		
		return true;
	}
	/**
	 * 按中间开关排序，中间开关放最前面
	 */
	public static void sortListBySwMiddle(List<PowerDevice> swlist){
		if(swlist==null){
			return;
		}else{
		    Collections.sort(swlist, new Comparator<PowerDevice>() {
		        @Override
		        public int compare(PowerDevice p1, PowerDevice p2) {
		          if(RuleExeUtil.isSwMiddleInThreeSecond(p1)) {
		            return -1;
		          }
		          else {
		            return 1;
		          }
		        }
		    });
		}
	}
	/**
	 * 按刀闸排序，母线刀闸放后面
	 */
	public static void sortDzListByMXDZ(List<PowerDevice> dzlist){
		if(dzlist.size() ==2){
			PowerDevice pd1 = dzlist.get(0);
			PowerDevice pd2 = dzlist.get(1);
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(pd1, pd2, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "",
				false, true, true, true, true);
			if(lineList.size()==0){
				Collections.reverse(dzlist);
			}
		}
	}
}
