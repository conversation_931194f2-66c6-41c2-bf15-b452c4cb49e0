/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 基础元件操作执行类
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class BasisCompOperate implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();

		if (CBSystemConstants.lcm == null) {
			CBSystemConstants.lcm = new ArrayList<CheckMessage>();
		}
		
		if(pd==null)
			return false;
        if(pd.getDeviceStatus().equals(CBSystemConstants.getDeviceStateValue(rbm.getEndState()))){
        	return true;
        }
        
        String sql="select * from "+CBSystemConstants.opcardUser+"t_a_ruleuserzb a,"+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO b where b.islock = '0' and a.statecode=b.statecode and a.srcstatus='"+rbm.getBeginStatus()+"' and b.statevalue='"+rbm.getEndState()+"' and a.equipid='"+pd.getPowerDeviceID()+"'";
		List result=DBManager.queryForList(sql);
		if(result.size()>0){
			for(int i=0;i<result.size();i++){
				Map map=(Map) result.get(i);
				String ruleid=StringUtils.ObjToString(map.get("ruleid"));
				String cbsql="select * from "+CBSystemConstants.opcardUser+"t_a_ruleusercb_condition where f_ruleid='"+ruleid+"'";
				List cbresult=DBManager.queryForList(cbsql);
				Map cbmap=(Map) cbresult.get(0);
				String equipid=StringUtils.ObjToString(cbmap.get("equipid"));
				String isnegated=StringUtils.ObjToString(cbmap.get("isnegated"));
				String srcstatus=StringUtils.ObjToString(cbmap.get("srcstatus"));
				String endstatus;
				if(isnegated.equals("0")){
					endstatus=srcstatus;
				}else{
					if(srcstatus.equals("0")){
						endstatus="1";
					}else{
						endstatus="0";
					}
				}
				String stationid=pd.getPowerStationID();
				PowerDevice pdN = CBSystemConstants.getPowerDevice(stationid,equipid);
				if(pdN!=null){
					RuleUtil.deviceStatusSet(pdN, pdN.getDeviceStatus(), endstatus);
				}
			}
		}
		

        
        //保存DTD历史状态
        String endStatus=CBSystemConstants.getDeviceStateValue(rbm.getEndState());
        if(pd.getDeviceType().equals(SystemConstants.SwitchSeparate)
        		||pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)) {
        	 DispatchTransDevice dtd=new DispatchTransDevice();
             dtd.setTransDevice(pd);
             dtd.setParentDevice(CBSystemConstants.getParentDev());
             dtd.setBeginstatus(rbm.getBeginStatus());
             dtd.setEndstate(rbm.getEndState());
             dtd.setFlag("1");
             CBSystemConstants.putDtdMap(dtd);
        }
        else {
        	DispatchTransDevice dtd=new DispatchTransDevice();
	        dtd.setTransDevice(pd);
	        dtd.setParentDevice(CBSystemConstants.getParentDev());
	        dtd.setBeginstatus(pd.getDeviceStatus());
	        dtd.setEndstate(endStatus);
	        dtd.setFlag("0");
	        CBSystemConstants.putDtdMap(dtd);
        }
        
        pd.setDeviceStatus(endStatus);//设置内存中设备状态
        if(!CBSystemConstants.roleCode.equals("1")) //配网不做设备状态联动
        	DeviceOperate.buildDeviceStatus(pd, endStatus);  //此处改为不调用，由规则类配置调用
        
		return true;
	}
}
