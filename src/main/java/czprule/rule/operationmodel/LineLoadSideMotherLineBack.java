package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.DictionarysModel;
import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipRadioChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 线路代线路执行类，传入线路进行线路代路操作

 */
public class LineLoadSideMotherLineBack implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {

		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;

	List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		
		if(xlswList.size()>0){

			List<PowerDevice> pldzList = RuleExeUtil.getDeviceList(xlswList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,
					CBSystemConstants.RunTypeKnifePL, "", false, true, true, true);
			if(pldzList.size()>0){
				if(pldzList.get(0).getDeviceStatus().equals("1")){
					ShowMessage.view("["+pldzList.get(0).getPowerDeviceName()+"]在分位！");
		        	return false;
				}
				List<PowerDevice> sideMotherLineList =RuleExeUtil.getDeviceDirectList(pldzList.get(0), SystemConstants.MotherLine);
				if(sideMotherLineList.size()>0){
					List<PowerDevice>  xlList =RuleExeUtil.getDeviceList(sideMotherLineList.get(0), SystemConstants.InOutLine, "", false, true, true);
					xlList.remove(pd);
					if(xlList.size()==0){
						ShowMessage.view("未找到旁路母线连接的其他在合位的刀闸！");
			        	return false;
					}
					List<PowerDevice> dlxlswList =  RuleExeUtil.getDeviceList(xlList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					if(dlxlswList.size()>0){
						if(!dlxlswList.get(0).getDeviceStatus().equals("0")){
							ShowMessage.view("["+dlxlswList.get(0).getPowerDeviceName()+"]不在运行状态！");
				        	return false;
						}
					}
					
					if(xlswList.get(0).getDeviceStatus().equals("0")){
						ShowMessage.view("["+xlswList.get(0).getPowerDeviceName()+"]在运行状态！");
			        	return false;
					}
					
					List<PowerDevice> dlpldzList = RuleExeUtil.getDeviceList(dlxlswList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,
							CBSystemConstants.RunTypeKnifePL, "", false, true, true, true);
//					RuleExeUtil.deviceStatusChange(dlxlswList.get(0), dlxlswList.get(0).getDeviceStatus(), "0");
					RuleExeUtil.deviceStatusChange(xlswList.get(0), xlswList.get(0).getDeviceStatus(), "0");
					
					RuleExeUtil.deviceStatusChange(pldzList.get(0), "0", "1");
					if(dlpldzList.size()>0){
						RuleExeUtil.deviceStatusChange(dlpldzList.get(0), "0", "1");
					}
					
					
				}
				
				
			}
		}
		
		return true;
	}

}
