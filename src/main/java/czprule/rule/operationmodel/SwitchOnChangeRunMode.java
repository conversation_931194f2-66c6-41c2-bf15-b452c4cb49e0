package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;

public class SwitchOnChangeRunMode implements RulebaseInf {
	public boolean execute(RuleBaseMode rbm) {
		
		boolean result = true;
		PowerDevice pd = rbm.getPd();
		
		
		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
			return true;
		
		 if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)) {
			
			if(rbm.getBeginStatus().equals("3"))
				RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "2");
			else if(rbm.getBeginStatus().equals("2"))
				RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "1");
			
			List<PowerDevice> zbList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
			
			PowerDevice zb = zbList.get(0);
			
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, "", CBSystemConstants.RunTypeSwitchFHC, false, false, false, false);
			if(mlList.size() > 0) {
				PowerDevice ml = mlList.get(0);
				
				List<PowerDevice> tfList = RuleExeUtil.getDeviceList(ml, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, false, true);

				if(tfList.size() == 1) {
					
					PowerDevice tf = tfList.get(0);
					
				
					
					List<PowerDevice> mlswLoadList = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, true, true);
					
					if(mlswLoadList.size()==0){
						mlswLoadList = RuleExeUtil.getDeviceList(ml, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchFHC, "", false, false, true, true);
					}
					for(Iterator it = mlswLoadList.iterator(); it.hasNext();) {
						PowerDevice dev = (PowerDevice)it.next();
						if(dev.equals(pd)) 
							it.remove();
						else if(!dev.getDeviceStatus().equals("0"))
							it.remove();
					}
					PowerDevice loadMLSwitch = null;
					if(mlswLoadList.size() == 1)
						loadMLSwitch = mlswLoadList.get(0);
					else if(mlswLoadList.size() == 2) {
						List<PowerDevice> tfList1 = RuleExeUtil.getDeviceList(ml, mlswLoadList.get(0), SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, null, "", false, false, false, true);
						if(tfList1.size() == 0)
							loadMLSwitch = mlswLoadList.get(0);
						else {
							List<PowerDevice> tfList2 = RuleExeUtil.getDeviceList(ml, mlswLoadList.get(1), SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, null, "", false, false, false, true);
							if(tfList2.size() == 0)
								loadMLSwitch = mlswLoadList.get(1);
						}
					}
					List<PowerDevice> sourceMLSwitchList = new ArrayList<PowerDevice>();
					List<PowerDevice> devList = new ArrayList<PowerDevice>();
					devList = RuleExeUtil.getTransformersLinkDevice(zb, tf);
					if(devList != null) {
						for (PowerDevice dev : devList) {
							if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
								sourceMLSwitchList.add(dev);
							}
						}
					}
					
					RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), "0");
					RuleExeUtil.deviceStatusExecute(loadMLSwitch, loadMLSwitch.getDeviceStatus(), "1");
					if(sourceMLSwitchList.size()>0){
						RuleExeUtil.deviceStatusExecute(sourceMLSwitchList.get(0), sourceMLSwitchList.get(0).getDeviceStatus(), "1");
					}
				}
			}

			return true;
		}
		
		else {
			RuleExeUtil.deviceStatusExecute(pd, pd.getDeviceStatus(), rbm.getEndState());
		}
		
		return true;
	
	}

}
