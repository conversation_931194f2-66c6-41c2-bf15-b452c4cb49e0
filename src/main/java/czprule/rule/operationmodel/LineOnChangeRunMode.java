package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;

/**
 * 线路送电调整运行方式
 * <AUTHOR>
 *
 */
public class LineOnChangeRunMode implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		
		PowerDevice pd=rbm.getPd();
		
		
		if (pd == null)
			return false;
		
		// 获取负荷侧线路
		List<PowerDevice> load = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
		if(load==null&&pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
			load = RuleExeUtil.getDeviceList(pd, null, "ACLineSegment", null, null, null, false, true, true, true);
		}
			
		for (PowerDevice dev : load) {
			if(!dev.getDeviceStatus().equals("0"))
				continue;
			if (!executeLoadBack(dev)) {
				return false;
			}

		}
		
		return true;
	}
	
	public boolean executeLoadBack(PowerDevice pd) {
		List<PowerDevice> switchs=new ArrayList<PowerDevice>();
		PowerDevice sw = RuleExeUtil.getDeviceSwitch(pd);
		List<PowerDevice> mls =  RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
		if(mls.size() > 0) {
			List<PowerDevice> lines =  RuleExeUtil.getDeviceList(mls.get(0), sw, SystemConstants.InOutLine, SystemConstants.PowerTransformer, null, null, false, false, true, true);
			if(lines.size() == 0) {
				List<PowerDevice> kgs = RuleExeUtil.getDeviceList(mls.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, null, false, false, true, true);
				for (PowerDevice kg : kgs) {
					if(kg.getDeviceStatus().equals("0")){
						switchs.add(kg);
						RuleExeUtil.deviceStatusExecute(kg, kg.getDeviceStatus(), "1");
						break;
					}
				}
				if(switchs.size() == 0){
					kgs = RuleExeUtil.getDeviceList(mls.get(0), sw, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, null, false, false, false, true);
					for (PowerDevice kg : kgs) {
						if(kg.getDeviceStatus().equals("0")){
							switchs.add(kg);
						}
					}
					EquipCheckChoose ecc =new EquipCheckChoose(null, true, switchs, "选择要断开的线路开关");
					if(ecc.getChooseEquip()!=null) {
						List<PowerDevice> choose=ecc.getChooseEquip();
						for (PowerDevice ch : choose) {
							RuleUtil.deviceStatusChange(ch, ch.getDeviceStatus(), "1");
						}
					}
				}
			}
		}
		/*
		List<PowerDevice> mls =  RuleUtil.getMotherLineListAllVolOff(pd);
		List<PowerDevice> switchs=new ArrayList<PowerDevice>();
		for (PowerDevice ml : mls) {
			//if(ml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
			//	continue;
			List<PowerDevice> kgs = RuleUtil.getBayDeviceByType(ml, SystemConstants.Switch);
			for (PowerDevice kg : kgs) {
				if(kg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
					//RuleUtil.deviceStatusChange(kg, kg.getDeviceStatus(), "1");
					//break;
					List<PowerDevice> lines = RuleUtil.getLinkedDeviceByType1(kg, SystemConstants.MotherLine);
					lines.remove(ml);
					lines =RuleUtil.getLinkedDeviceByType3(lines.get(0), SystemConstants.InOutLine);
					for (PowerDevice l : lines) {
						if(l.getDeviceStatus().equals("0") && kg.getDeviceStatus().equals("0")){
							switchs.add(kg);
							break;
						}
					}
					
				}
			}
			
			if(switchs.size()==0){
				return true;
			}
//			EquipCheckChoose ecc =new EquipCheckChoose(null, true, switchs, "选择要断开的母联开关");
//			if(ecc.getChooseEquip()!=null) {
//				List<PowerDevice> choose=ecc.getChooseEquip();
//				for (PowerDevice ch : choose) {
//					RuleUtil.deviceStatusChange(ch, ch.getDeviceStatus(), "1");
//				}
//			}
			for (PowerDevice ch : switchs) {
				RuleUtil.deviceStatusChange(ch, ch.getDeviceStatus(), "1");
			}
		}
		*/
	//}
	
	
	return true;
	}

}
