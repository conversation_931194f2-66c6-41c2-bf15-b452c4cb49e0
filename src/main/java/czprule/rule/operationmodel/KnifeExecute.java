/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：预研部操作票产品
 * 功能说明 : 设备连接刀闸执行器
 * 作    者 : 张余平
 * 开发日期 : 2011-07-11
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipRadioChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;
import czprule.wordcard.dao.DeviceStateMentManager;

public class KnifeExecute implements RulebaseInf {

	/**
	 * 执行设备连接的指定运行类型的刀闸，如果源设备是开关并且一段存在两个母线刀闸，弹出刀闸选择器
	 */
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		List<PowerDevice> knifes = new ArrayList<PowerDevice>(); // 执行刀闸集合
		List<PowerDevice> port1MLknifes = new ArrayList<PowerDevice>(); // 开关端口一母线刀闸集合
		List<PowerDevice> port2MLknifes = new ArrayList<PowerDevice>(); // 开关端口二母线刀闸集合

		String devRunType = rbm.getDeviceruntype();
		//判断操作时 断电还是送点
		int czfx;
		int b =Integer.parseInt(rbm.getBeginStatus());
		int e = Integer.parseInt(rbm.getEndState());
		czfx= b-e;

		
		// 一、搜索设备连接的母线刀闸
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		PowerDevice tempDev = null;
		List tempKnifes=null;
		if(!"".equals(rbm.getTranType())){
			 PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
			 List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
			 if(sourceLineTrans==null||loadLineTrans==null){
				 if(CBSystemConstants.isCurrentSys) {
					 ShowMessage.view("请先设置线路两端变电站属性！");
					 return false;
				 }
				 else
					 return true;
			 }
			 if("S".equals(rbm.getTranType())){
				 inPara.put("oprSrcDevice", sourceLineTrans);
				 inPara.put("tagDevType", SystemConstants.SwitchSeparate);
		         inPara.put("isSearchDirectDevice", true);
	             cs.execute(inPara, outPara);
	    	 	 inPara.clear();
	    	 	 tempKnifes = (ArrayList) outPara.get("linkedDeviceList");
	    	 	 for (int i = 0; i < tempKnifes.size(); i++) {
	    	 		tempDev=(PowerDevice)tempKnifes.get(i);
	    	 		if (devRunType.equals("")) {
						knifes.add(tempDev);
					} else {
						if (tempDev.getDeviceRunType().equals(devRunType))
							knifes.add(tempDev);
					}
				 }
			 }else{
				 for (int i = 0; i < loadLineTrans.size(); i++) {
					inPara.put("oprSrcDevice", loadLineTrans.get(i));
					inPara.put("tagDevType", SystemConstants.SwitchSeparate);
			        inPara.put("isSearchDirectDevice", true);
		            cs.execute(inPara, outPara);
		    		inPara.clear();
		    		tempKnifes = (ArrayList) outPara.get("linkedDeviceList");
		    	 	for (int j = 0; j < tempKnifes.size(); j++) {
		    	 		tempDev=(PowerDevice)tempKnifes.get(j);
		    	 		if (devRunType.equals("")) {
							knifes.add(tempDev);
						} else {
							if (tempDev.getDeviceRunType().equals(devRunType))
								knifes.add(tempDev);
						}
					}
				}
			 }
		} else {
			if (pd.getDeviceType().equals(SystemConstants.Switch)) {
				inPara.put("oprSrcDevice", pd);
				inPara.put("tagDevType", SystemConstants.SwitchSeparate);
				inPara.put("isSearchDirectDevice", true);
				inPara.put("validPort", "1");
				cs.execute(inPara, outPara);
			    tempKnifes = (ArrayList) outPara.get("linkedDeviceList");
			    
			    if(!pd.getPowerStationID().equals("SS-418")){//玉溪地调220kV寒武变
			    	 //有些站低压侧主变开关两侧是小车刀闸，小车刀闸再连接主变刀闸，这种情况把小车连接的主变刀闸也查出来
				    for (int i = 0; i < tempKnifes.size(); i++) {
				    	tempDev = (PowerDevice) tempKnifes.get(i);
				    	if(tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeDY)) {
				    		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(tempDev, SystemConstants.SwitchSeparate);
				    		if(dzList.size() == 1 && 
				    				dzList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZB) &&
				    				!tempKnifes.contains(dzList.get(0))) {
				    			tempKnifes.add(dzList.get(0));
				    			break;
				    		}
				    	}
				    }
			    }
			   
			    
				for (int i = 0; i < tempKnifes.size(); i++) {
					tempDev = (PowerDevice) tempKnifes.get(i);
					if (devRunType.equals("")) {
						if(tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX))
							port1MLknifes.add(tempDev);
						else
							knifes.add(tempDev);	
					} else {
						if (tempDev.getDeviceRunType().equals(devRunType)){
							if(devRunType.equals(CBSystemConstants.RunTypeKnifeMX))
								port1MLknifes.add(tempDev);
							else
								knifes.add(tempDev);
						}
					}
				}
				inPara.put("validPort", "2");
				cs.execute(inPara, outPara);
				inPara.clear();
			    tempKnifes = (ArrayList) outPara.get("linkedDeviceList");
			    
			    //有些站低压侧主变开关两侧是小车刀闸，小车刀闸再连接主变刀闸，这种情况把小车连接的主变刀闸也查出来
			    for (int i = 0; i < tempKnifes.size(); i++) {
			    	tempDev = (PowerDevice) tempKnifes.get(i);
			    	if(tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeDY)) {
			    		List<PowerDevice> dzList = RuleExeUtil.getDeviceDirectList(tempDev, SystemConstants.SwitchSeparate);
			    		if(dzList.size() == 1 && 
			    				dzList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZB) &&
			    				!tempKnifes.contains(dzList.get(0))) {
			    			
			    			PowerDevice station = CBSystemConstants.getPowerStation(dzList.get(0).getPowerStationID());
			    			
			    			if(dzList.get(0).getPowerVoltGrade()==station.getPowerVoltGrade()){
				    			tempKnifes.add(dzList.get(0));
			    			}
			    			
			    			break;
			    		}
			    	}
			    }
			    
				for (int i = 0; i < tempKnifes.size(); i++) {
					tempDev = (PowerDevice) tempKnifes.get(i);
					if (devRunType.equals("")) {
						if(tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX))
							port2MLknifes.add(tempDev);
						else
							knifes.add(tempDev);	
					} else {
						if (tempDev.getDeviceRunType().equals(devRunType)){
							if(devRunType.equals(CBSystemConstants.RunTypeKnifeMX))
								port2MLknifes.add(tempDev);
							else
								knifes.add(tempDev);
						}
					}
				}
			} else {
				inPara.put("oprSrcDevice", pd);
				inPara.put("tagDevType", SystemConstants.SwitchSeparate);
				inPara.put("isSearchDirectDevice", true);
				cs.execute(inPara, outPara);
			    tempKnifes = (ArrayList) outPara.get("linkedDeviceList");
				for (int i = 0; i < tempKnifes.size(); i++) {
					tempDev = (PowerDevice) tempKnifes.get(i);
					if (devRunType.equals("")) {
						knifes.add(tempDev);
					} else {
						if (tempDev.getDeviceRunType().equals(devRunType))
							knifes.add(tempDev);
					}
				}
			}

		}

		//双母情况下，存在合上的刀闸，不用再做选择
		for (int i = 0; i < port1MLknifes.size(); i++) {
			tempDev=port1MLknifes.get(i);
			if(CBSystemConstants.getDeviceStateValue(rbm.getEndState()).equals(tempDev.getDeviceStatus())&&"0".equals(tempDev.getDeviceStatus())){
				port1MLknifes.clear();
				break;
			}
		}
		for (int i = 0; i < port2MLknifes.size(); i++) {
			tempDev=port2MLknifes.get(i);
			if(CBSystemConstants.getDeviceStateValue(rbm.getEndState()).equals(tempDev.getDeviceStatus())&&"0".equals(tempDev.getDeviceStatus())){
				port2MLknifes.clear();
				break;
			}
		}
		
		
		// 双母情况选择需要合上的母线刀闸
		if (port1MLknifes.size() > 1 && "0".equals(CBSystemConstants.getDeviceStateValue(rbm.getEndState()))) {
			if(CBSystemConstants.jh_tai == 1 || !CBSystemConstants.isCurrentSys) { //校核模式下不弹选择框，自动选一个
				knifes.add(0, port1MLknifes.get(0));
			}
			else {
				
				PowerDevice knife =null;
				if(CBSystemConstants.stateOfTheDrawer){
					for (int x = 0; x < port1MLknifes.size(); x++) {
						tempDev=port1MLknifes.get(x);
						if(CBSystemConstants.LineTagStatus.containsKey(tempDev)) {
					 		knife = tempDev;
						 	return true;
					 	}
					}
				}else{
					
				EquipRadioChoose dcd = new EquipRadioChoose(SystemConstants.getMainFrame(), true, port1MLknifes, "请选择需要合上的母线刀闸.");
				for (int i = 0; i < port1MLknifes.size(); i++) {// 循环需要选择的刀闸集合
																// --黄翔修改
					tempDev = port1MLknifes.get(i);// tempDev 为选上的刀闸
					String defaultstate = tempDev.getPowerDeviceDefaultSta();// 获取刀闸的常态
					if (defaultstate.equals("0")) {// 如果为0，则设置选上
						dcd.setChooseEquip(tempDev);
						break;
					}
					else if(CZPService.getService().getKnifeNormalStatus(tempDev).equals("0")) {
						dcd.setChooseEquip(tempDev);
						break;
					}
				}
				  knife = dcd.getChooseEquip();
				
				}
				if (knife == null)
					return false;
				knifes.add(0, knife);
			}
		} else if (Integer.valueOf(rbm.getBeginStatus()) < Integer.valueOf(rbm.getEndState())) {
			knifes.addAll(port1MLknifes);
		} else {
			knifes.addAll(0, port1MLknifes);
		}
		if (port2MLknifes.size() > 1 && "0".equals(CBSystemConstants.getDeviceStateValue(rbm.getEndState()))) {
			if(CBSystemConstants.jh_tai == 1 || !CBSystemConstants.isCurrentSys) { //校核模式下不弹选择框，自动选一个
				knifes.add(0, port2MLknifes.get(0));
			}
			else {
				PowerDevice knife =null;
				if(CBSystemConstants.stateOfTheDrawer){
					for (int x = 0; x < port2MLknifes.size(); x++) {
						tempDev=port2MLknifes.get(x);
						if(CBSystemConstants.LineTagStatus.containsKey(tempDev)) {
					 		knife = tempDev;
						 	//return true;
					 	}
					}
				}else{
					EquipRadioChoose dcd = new EquipRadioChoose(SystemConstants.getMainFrame(), true, port2MLknifes, "请选择需要合上的母线刀闸.");
					if (dcd == null)
						return false;
					for (int i = 0; i < port2MLknifes.size(); i++) {// 循环需要选择的刀闸集合
																	// --黄翔修改
						tempDev = port2MLknifes.get(i);// tempDev 为选上的刀闸
						String defaultstate = tempDev.getPowerDeviceDefaultSta();// 获取刀闸的常态
						if (defaultstate.equals("0")||defaultstate.equals("")) {// 如果为0，则设置选上
							dcd.setChooseEquip(tempDev);
							break;
						}
						else if(CZPService.getService().getKnifeNormalStatus(tempDev).equals("0")) {
							dcd.setChooseEquip(tempDev);
							break;
						}
					}
					  knife = dcd.getChooseEquip();
				}
			
				
				
	
				if (knife == null)
					return false;
				knifes.add(0, knife);
			}
		} else if (Integer.valueOf(rbm.getBeginStatus()) < Integer.valueOf(rbm.getEndState())) {
			knifes.addAll(port2MLknifes);
		} else {
			knifes.addAll(0, port2MLknifes);
		}
		//刀闸操作排序
		if (czfx > 0) {
			for (int i = knifes.size() - 1; i > 0; --i) {
				for (int j = 0; j < i; ++j) {
					PowerDevice powerd = (PowerDevice) knifes.get(j);
					if (!powerd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)
							&& knifes.get(j + 1).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)) {
						knifes.set(j, knifes.get(j + 1));
						knifes.set(j + 1, powerd);
					}
				}
			}
		} else {
			knifes = RuleExeUtil.sortByXLC(knifes);
			
			
//			for (int i = knifes.size() - 1; i > 0; --i) {
//				for (int j = 0; j < i; ++j) {
//					PowerDevice powerd = (PowerDevice) knifes.get(j);
//					if (powerd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)
//							&& !knifes.get(j + 1).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)) {
//						knifes.set(j, knifes.get(j + 1));
//						knifes.set(j + 1, powerd);
//					}
//				}
//			}
		}
		
		
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){//母联开关或者分段开关刀闸顺序按编号顺序执行
			RuleExeUtil.swapLowDeviceList(knifes);
			if(czfx > 0){//复电倒序
				Collections.reverse(knifes);
			}
			
		}
		
		// 执行刀闸操作
		for (int i = 0; i < knifes.size(); i++) {
			tempDev = (PowerDevice) knifes.get(i);
	/*		String  deviceRunModel = "";
			if(CBSystemConstants.stateOfTheDrawer){
				//搜素与开关直接连接的母线(排除旁路母线)
				List<PowerDevice> otherMLList = RuleExeUtil.getDeviceList(tempDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer+","+CBSystemConstants.RunTypeSideMother, true, false, true);
				if(otherMLList.size()> 0){
					for(PowerDevice mlTemp : otherMLList){
						if(!mlTemp.getDeviceRunModel().trim().equals("") && !mlTemp.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
							deviceRunModel  = mlTemp.getDeviceRunModel();
							if(mlTemp.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine))
								break;
						}
						}
					}
				}
			if(!deviceRunModel.equals(CBSystemConstants.RunModelOneMotherLine)&&!CBSystemConstants.stateOfTheDrawer){
				if(CBSystemConstants.getDeviceStateValue(rbm.getEndState()).equals(tempDev.getDeviceStatus()))
					continue;
			}
			*/
			if(CBSystemConstants.getDeviceStateValue(rbm.getEndState()).equals(tempDev.getDeviceStatus()))
				continue;
			RuleExecute ruleExecute = new RuleExecute();
			RuleBaseMode rbmode = new RuleBaseMode();
			DeviceStateMentManager dsmm = new DeviceStateMentManager();
			rbmode.setPd(tempDev);
			rbmode.setBeginStatus(rbm.getBeginStatus());
			rbmode.setEndState(rbm.getEndState());
			rbmode.setStateCode(dsmm.getStateCodeByStatus(tempDev.getDeviceType(), rbm.getEndState(), "9")); //默认取基本操作,操作类型代码是9
			if (!ruleExecute.execute(rbmode)) {
				return false;
			}
		}
		
		//兼容设备没关联刀闸情况,如果刀闸数量为0，拉开刀闸时设备状态为热备用的变更成冷备用，合上刀闸时设备状态为冷备用的变成热备用
		if(knifes.size()==0&&!pd.getDeviceType().equals(SystemConstants.PowerTransformer)){
			if(rbm.getDeviceruntype().equals("")){//有设置安装类型的，不做处理
				if(rbm.getEndState().equals("1")&&pd.getDeviceStatus().equals("1")){
					RuleExeUtil.deviceStatusSet(pd, "1", "2");
				}else if(rbm.getEndState().equals("0")&&pd.getDeviceStatus().equals("2")){
					RuleExeUtil.deviceStatusSet(pd, "2", "1");
				}
			}

		}
		

		return true;
	}

}
