package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;
/**
 * 将母联兼旁路开关由母联作用转为旁路作用
 * <AUTHOR>
 *
 */
public class SwitchChangeMLtoPL implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		if (!pd.getDeviceType().equals(SystemConstants.Switch)) {
			ShowMessage.view("[" + pd.getPowerDeviceName() + "]不是开关！");
			return false;
		}
		// 带主变,线路开关
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)
				|| pd.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchFHC)
				|| pd.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchXL)) {
			// 查找操作母线 及刀闸
			CommonSearch cs = new CommonSearch();
			Map<String, Object> inPara = new HashMap<String, Object>();
			Map<String, Object> outPara = new HashMap<String, Object>();
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.MotherLine);
			cs.execute(inPara, outPara);
			inPara.clear();
			PowerDevice sideML = null;
			PowerDevice sideKnife = null;
			List tempMLs = (ArrayList) outPara.get("linkedDeviceList");
			for (int i = 0; i < tempMLs.size(); i++) {
				PowerDevice tempML = (PowerDevice) tempMLs.get(i);
				if (tempML.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSideMother)) {
					sideML = tempML;
					ArrayList<PowerDevice> tempDevs = ((HashMap<PowerDevice, ArrayList<PowerDevice>>) outPara
							.get("pathList")).get(sideML);
					for (int j = 0; j < tempDevs.size(); j++) {
						if (tempDevs.get(j).getDeviceRunType().equals(
								CBSystemConstants.RunTypeKnifePL)) {
							sideKnife = tempDevs.get(j);
							break;
						}
					}
					break;
				}
			}
			if (sideML == null) {
				ShowMessage.view("[" + pd.getPowerDeviceName() + "]没有可导的旁路母线！");
				return false;
			}
			if (sideKnife == null) {
				ShowMessage.view("[" + sideML.getPowerDeviceName()
						+ "]没有可以连接的旁路刀闸！");
				return false;
			}
			if (sideKnife.getDeviceStatus().equals("0")) {
				ShowMessage.view("[" + sideKnife.getPowerDeviceName()
						+ "]连接的旁路刀闸不在断开状态！");
				return false;
			}
			// 查找操作旁路开关

			inPara.put("oprSrcDevice", sideML);
			inPara.put("tagDevType", SystemConstants.Switch);
			cs.execute(inPara, outPara);
			inPara.clear();
			PowerDevice sideSwitch = null;
			List tempSwitchs = (ArrayList) outPara.get("linkedDeviceList");
			for (int i = 0; i < tempSwitchs.size(); i++) {
				PowerDevice tempSwitch = (PowerDevice) tempSwitchs.get(i);
				if (tempSwitch.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchPL)) {
					sideSwitch = tempSwitch;
					break;
				} else if (tempSwitch.getDeviceSetType().equals(
						CBSystemConstants.RunTypeSwitchMLPL)
						&& tempSwitch.getDeviceRunType().equals(
								CBSystemConstants.RunTypeSwitchML)) {
					return changeToPL(tempSwitch);
				}
			}
			if (sideSwitch == null) {
				ShowMessage.view("[" + sideML.getPowerDeviceName()
						+ "]没有连接到旁路开关！");
				return false;
			}
			if (sideSwitch.getDeviceStatus().equals("0")) {
				ShowMessage.view("[" + sideML.getPowerDeviceName()
						+ "]连接的旁路开关在运行状态！");
				return false;
			}
			if (sideSwitch.getDeviceStatus().equals("3")) {
				ShowMessage.view("[" + sideML.getPowerDeviceName()
						+ "]连接的旁路开关在检修状态！");
				return false;
			}
		}
		return true;
	}

	/**
	 * change the Switch type from ML to PL
	 * 
	 * @param pd
	 */
	private boolean changeToPL(PowerDevice pd) {
		
		RuleExeUtil.deviceStatusChange(pd, "0", "1");
		
		List<PowerDevice> pdlist1 = RuleExeUtil.getDeviceDirectByPortList(pd, SystemConstants.SwitchSeparate, "1");
		List<PowerDevice> pdlist2 = RuleExeUtil.getDeviceDirectByPortList(pd, SystemConstants.SwitchSeparate, "2");
		List<PowerDevice> mxkflist = null;
		PowerDevice mxkf = null;
		PowerDevice plkf = null;
		if (pdlist1.size() > 0 && pdlist2.size() > 0) {
			for (int i = 0; i < pdlist1.size(); i++) {
				PowerDevice rt = pdlist1.get(i);
				if (rt.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)) {
					plkf = rt;
					pdlist1.remove(rt);
					if(pdlist1.size() == 1)
						mxkf = pdlist1.get(0);
					break;
				}
			}
			for (int i = 0; i < pdlist2.size(); i++) {
				PowerDevice rt = pdlist2.get(i);
				if (rt.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)) {
					plkf = rt;
					pdlist2.remove(rt);
					if(pdlist1.size() == 1)
						mxkf = pdlist1.get(0);
					break;
				}
			}
			
			if(plkf != null)
				RuleExeUtil.deviceStatusChange(plkf, plkf.getDeviceStatus(),"0"); //合上旁路刀闸
			if(mxkf != null)
				RuleExeUtil.deviceStatusChange(mxkf, mxkf.getDeviceStatus(), "1"); //拉开旁路侧的母线刀闸
		} else {
			ShowMessage.view("没有可转变为旁路开关的母联兼旁路开关！");
			return false;
		}
		
		return true;
	}
}
