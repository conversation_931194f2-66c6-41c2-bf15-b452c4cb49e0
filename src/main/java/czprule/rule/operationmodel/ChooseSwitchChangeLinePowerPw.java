package czprule.rule.operationmodel;

import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoosePw;
import czprule.system.CBSystemConstants;
import java.util.ArrayList;
import java.util.List;

public class ChooseSwitchChangeLinePowerPw
  implements RulebaseInf
{
  EquipCheckChoosePw ecc = null;

  public boolean execute(RuleBaseMode rbm) {
    if (CBSystemConstants.isCurrentSys) {
      final List loadMap = new ArrayList();

      if ((!CBSystemConstants.isSame.booleanValue()) && (CBSystemConstants.getSamepdlist().size() == 0)) {
        CBSystemConstants.isSame = Boolean.valueOf(true);
        new Thread(new Runnable()
        {
          public void run() {
            ChooseSwitchChangeLinePowerPw.this.ecc = 
              new EquipCheckChoosePw(SystemConstants.getMainFrame(), 
              false, loadMap, "请选择目标设备", CBSystemConstants.getCurRBM());
          }
        }).start();
        return false;
      }

      for (int i = 0; i < CBSystemConstants.getSamepdlist().size(); i++) {
    	  PowerDevice pd = (PowerDevice)CBSystemConstants.getSamepdlist().get(i);
          RuleExeUtil.deviceStatusReset(pd, pd.getDeviceStatus(), rbm.getEndState());
      }

      return true;
    }

    return true;
  }
}