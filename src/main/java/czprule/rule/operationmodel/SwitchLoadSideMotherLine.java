package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.DictionarysModel;
import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationmodel.SwitchChangeMotherLine;
import czprule.rule.view.EquipRadioChoose;
import czprule.rule.view.EquipStatusChoose;
import czprule.securitycheck.view.CheckWord;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 倒旁路执行类，传入线路开关/主变开关进行旁代操作
 * 作    者: 郑柯
 * 开发日期: 2012-9-18 下午02:22:51 
 */
public class SwitchLoadSideMotherLine implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {

		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		if(!pd.getDeviceType().equals(SystemConstants.Switch)){
        	ShowMessage.view("["+pd.getPowerDeviceName()+"]不是开关！");
        	return false;
        }
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL) &&
				!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC) &&
				!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
			if(CBSystemConstants.isCurrentSys){
				ShowMessage.view("["+pd.getPowerDeviceName()+"]不能倒旁路！");
			}
        
        	return false;
        }
		if(!"0".equals(pd.getDeviceStatus())){
			if(CBSystemConstants.isCurrentSys){
				ShowMessage.view("开关["+pd.getPowerDeviceName()+"]必须处于运行状态！");
			}
        	return false;
		}
		
		CommonSearch cs=new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
        inPara.put("tagDevType", SystemConstants.MotherLine);
        cs.execute(inPara, outPara);
		inPara.clear();
		PowerDevice sideML=null;
		PowerDevice sideKnife=null;
		List tempMLs = (ArrayList) outPara.get("linkedDeviceList");
		for (int i = 0; i < tempMLs.size(); i++) {
			PowerDevice tempML = (PowerDevice) tempMLs.get(i);
            if(tempML.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
            	sideML = tempML;
            	ArrayList<PowerDevice> tempDevs= ((HashMap<PowerDevice,ArrayList<PowerDevice>>)outPara.get("pathList")).get(sideML);
            	for (int j = 0; j < tempDevs.size(); j++) {
            		if(tempDevs.get(j).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)) {
            			sideKnife = tempDevs.get(j);
            			break;
            		}
            	}
            	break;
            }
		}
		if(sideML == null){
        	ShowMessage.view("["+pd.getPowerDeviceName()+"]没有可导的旁路母线！");
        	return false;
        }
//		if(!sideML.getDeviceStatus().equals("0")){
//        	ShowMessage.view("["+pd.getPowerDeviceName()+"]旁路母线不在运行状态！");
//        	return false;
//        }
		if(sideKnife == null){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]没有可以连接的旁路刀闸！");
        	return false;
        }
		if(sideKnife.getDeviceStatus().equals("0")){
        	ShowMessage.view("["+sideKnife.getPowerDeviceName()+"]连接的旁路刀闸不在断开状态！");
        	return false;
        }
		inPara.put("oprSrcDevice", sideML);
        inPara.put("tagDevType", SystemConstants.Switch);
        cs.execute(inPara, outPara);
		inPara.clear();
		PowerDevice sideSwitch=null;
		List tempSwitchs = (ArrayList) outPara.get("linkedDeviceList");
		for (int i = 0; i < tempSwitchs.size(); i++) {
			PowerDevice tempSwitch = (PowerDevice) tempSwitchs.get(i);
            if(tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL) ||
            		tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
            	sideSwitch = tempSwitch;
            	break;
            }
		}
		if(sideSwitch == null){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]没有连接到旁路开关！");
        	return false;
        }
		if(sideSwitch.getDeviceStatus().equals("0")){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]连接的["+CZPService.getService().getDevName(sideSwitch)+"开关]在运行状态！");
        	return false;
        }
		if(sideSwitch.getDeviceStatus().equals("3")){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]连接的["+CZPService.getService().getDevName(sideSwitch)+"开关]在检修状态！");
        	return false;
        }
		if(sideSwitch.getDeviceStatus().equals("2")){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]连接的["+CZPService.getService().getDevName(sideSwitch)+"开关]在冷备用状态！");
        	return false;
        }
		
		
		//搜索旁路开关的旁路刀闸
		inPara.put("oprSrcDevice", sideSwitch);
        inPara.put("tagDevType", SystemConstants.SwitchSeparate);
        cs.execute(inPara, outPara);
		inPara.clear();
		List tempPLKGDZs = (ArrayList) outPara.get("linkedDeviceList");
		PowerDevice tempplkgdz = null;
		for(int i=0;i<tempPLKGDZs.size();i++){
			PowerDevice paraDev = (PowerDevice)tempPLKGDZs.get(i);
			if(paraDev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)){
				tempplkgdz = paraDev;
				break;
			}
		}
		if(tempplkgdz==null){
			ShowMessage.view("未找到旁路开关的旁路刀闸！");
			return false;
		}
		
		//检测代路前除旁路开关外其余旁路母线侧刀闸均应在分闸位置
		inPara.put("oprSrcDevice", sideML);
        inPara.put("tagDevType", SystemConstants.SwitchSeparate);
        cs.execute(inPara, outPara);
		inPara.clear();
		List tempPLDZs = (ArrayList) outPara.get("linkedDeviceList");
		for(int i=0;i<tempPLDZs.size();i++){
			PowerDevice temppldz = (PowerDevice)tempPLDZs.get(i);
			if(temppldz.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)){
				if( (!temppldz.getPowerDeviceID().equals(sideKnife.getPowerDeviceID()))&&
						(!temppldz.getPowerDeviceID().equals(tempplkgdz.getPowerDeviceID()))&& temppldz.getDeviceStatus().equals("0")){
					ShowMessage.view("["+CZPService.getService().getDevName(temppldz)+"]刀闸不在断开状态！");
					return false;
				}
			}
		}
		
		
		
		
		
		boolean result = true;
		//旁路开关转热备用
		result = RuleExeUtil.deviceStatusChange(sideSwitch, sideSwitch.getDeviceStatus(), "1");
		if(!result)
			return false;
		if(sideSwitch.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) {
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(sideSwitch, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
			for(Iterator it = mlList.iterator();it.hasNext();) {
				PowerDevice ml = (PowerDevice)it.next();
				if(ml.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
					it.remove();
			}
			if(mlList.size() == 2) {
//				EquipRadioChooseGZ dcd = new EquipRadioChooseGZ(
//						SystemConstants.getMainFrame(), true, mlList,"请选择["+CZPService.getService().getDevName(sideSwitch)+"]挂接的母线");
				PowerDevice defaultML = null;
				List<PowerDevice> mlOnList = RuleExeUtil.getDeviceList(sideSwitch, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
				for (int i = 0; i < mlList.size(); i++) {
					if(mlOnList.contains(mlList.get(i))){
						defaultML = mlList.get(i);
//						dcd.setChooseEquip(defaultML);
						break;
					}
				}
//				PowerDevice chooseML = dcd.getChooseEquip();
//				if(!chooseML.equals(defaultML)) {
//					SwitchChangeMotherLine scml = new SwitchChangeMotherLine();
//					RuleBaseMode rbmPLKG = new RuleBaseMode();
//					rbmPLKG.setPd(sideSwitch);
//					scml.execute(rbmPLKG);
//				}
				
				//旁路开关默认与被代开关挂接同一条母线
				
				//当前开关链接母线
				PowerDevice dqMX = null;
				List<PowerDevice> pdOnList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
				if(pdOnList.size()<1){
					ShowMessage.view("拓扑错误！开关["+pd.getPowerDeviceName()+"]没有连接母线！");
					return false;
				}else{
					for(int i=0;i<pdOnList.size();i++){
						if(!pdOnList.get(i).getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
							dqMX = pdOnList.get(i);
							break;
						}
					}
				}
				
				if(dqMX==null){
					ShowMessage.view("开关["+pd.getPowerDeviceName()+"]未找到挂接母线！");
					return false;
				}
				if(!dqMX.equals(defaultML)) {
					SwitchChangeMotherLine scml = new SwitchChangeMotherLine();
					RuleBaseMode rbmPLKG = new RuleBaseMode();
					rbmPLKG.setPd(sideSwitch);
					scml.execute(rbmPLKG);
				}
				
			}
		}
		
		if(pd.getPowerVoltGrade()>110){
			PowerDevice source =null;
			List<PowerDevice> load =new ArrayList<PowerDevice>();
			List<PowerDevice> xlList =RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine
					, SystemConstants.PowerTransformer, true, true, true);
			if(xlList.size()>0){
				source=xlList.get(0);
				load=RuleExeUtil.getLineOtherSideList(source);
			}
			if(source!=null&&load.size()>0){
				CBSystemConstants.putLineSource(source.getPowerDeviceID(), source);
				CBSystemConstants.putLineLoad(source.getPowerDeviceID(), load);
			}
	
		}
		
		
		//合上旁路刀闸
		result = RuleExeUtil.deviceStatusChange(sideKnife, "1", "0");
		if(!result)
			return false;
		//合上旁路开关
		result = RuleExeUtil.deviceStatusChange(sideSwitch, "1", "0");
		if(!result)
			return false;
		
		
		List<PowerDevice> switchs=new ArrayList<PowerDevice>();  //执行开关集合
		List<String> defaultStatusList = new ArrayList<String>();
		switchs.add(pd);
		defaultStatusList.add("2");
		EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, switchs, defaultStatusList, "请选择开关目标状态");
		Map tagStatusMap=dialog.getTagStatusMap();
		if(tagStatusMap.size() == 0)
			return false;
		
	
		for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
			Map.Entry<PowerDevice, String> entry = it.next();
			RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
		}
		
		
		
		return true;
	}

}
