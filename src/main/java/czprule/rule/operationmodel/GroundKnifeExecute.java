/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：预研部操作票产品
 * 功能说明 : 设备连接的接地刀闸或者接地刀闸本身执行器
 * 作    者 : 张余平
 * 开发日期 : 2011-07-11
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

public class GroundKnifeExecute implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		List<PowerDevice> groundknifes=new ArrayList<PowerDevice>();  //执行刀闸集合
		List<PowerDevice> tempknifes=new ArrayList<PowerDevice>();  //接地刀闸集合
		
		//一、搜索设备连接的接地刀闸
		CommonSearch cs=new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		PowerDevice tempDev=null;
		if(pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
			groundknifes.add(pd);
		}else{
			if(!"".equals(rbm.getTranType())){
				 PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
				 List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
				 if(sourceLineTrans==null||loadLineTrans==null){
					 ShowMessage.view("请先设置线路两端变电站属性！");
					 return false;
				 }
				 if("S".equals(rbm.getTranType())){
					 tempknifes = RuleExeUtil.getDeviceList(sourceLineTrans,SystemConstants.SwitchFlowGroundLine,null,null,null,true,true,true,true);  
		    		 for (int j = 0; j < tempknifes.size(); j++) {
		    			 tempDev = tempknifes.get(j);
		    			 if(!groundknifes.contains(tempDev)){
		    				 groundknifes.add(tempDev);
		    			 }
					 }
		    		 //搜索线路刀闸，通过线路刀闸搜索接地刀闸
		    		 tempknifes = RuleExeUtil.getDeviceList(sourceLineTrans,SystemConstants.SwitchSeparate,null,CBSystemConstants.RunTypeKnifeXL,null,true,true,true,true);
		    	 	 for (int j = 0; j < tempknifes.size(); j++) {
		    	 		List<PowerDevice> groupKnifes = RuleExeUtil.getDeviceList(tempknifes.get(j),SystemConstants.SwitchFlowGroundLine,null,null,null,true,true,true,true);  
		    	 		for (int k = 0; k < groupKnifes.size(); k++) {
		    	 			 tempDev = groupKnifes.get(k);
			    			 if(!groundknifes.contains(tempDev)){
			    				 groundknifes.add(tempDev);
			    			 }
						}
		    	 	 }
				 }else{
					 for (int i = 0; i < loadLineTrans.size(); i++) {
						tempknifes = RuleExeUtil.getDeviceList(loadLineTrans.get(i),SystemConstants.SwitchFlowGroundLine,null,null,null,true,true,true,true);  
			    		for (int j = 0; j < tempknifes.size(); j++) {
			    			tempDev = tempknifes.get(j);
			    			 if(!groundknifes.contains(tempDev)){
			    				 groundknifes.add(tempDev);
			    			 }
						}
			    		//搜索线路刀闸，通过线路刀闸搜索接地刀闸
//			    		 tempknifes = RuleExeUtil.getDeviceList(loadLineTrans.get(i),SystemConstants.SwitchSeparate,null,CBSystemConstants.RunTypeKnifeXL,null,true,true,true,true);
//			    	 	 for (int j = 0; j < tempknifes.size(); j++) {
//			    	 		List<PowerDevice> groupKnifes = RuleExeUtil.getDeviceList(tempknifes.get(j),SystemConstants.SwitchFlowGroundLine,null,null,null,true,true,true,true);  
//			    	 		for (int k = 0; k < groupKnifes.size(); k++) {
//			    	 			 tempDev = groupKnifes.get(k);
//				    			 if(!groundknifes.contains(tempDev)){
//				    				 groundknifes.add(tempDev);
//				    			 }
//							}
//			    	 	 }
					}
				 }
			}else{
				inPara.put("oprSrcDevice", pd);
	            inPara.put("tagDevType", SystemConstants.SwitchFlowGroundLine);
	            inPara.put("isSearchDirectDevice", true);
	            cs.execute(inPara, outPara);
	    		inPara.clear();
	    		groundknifes = (ArrayList) outPara.get("linkedDeviceList");
			}	
		}
		
		
		//执行接地刀闸操作
		for (int i = 0; i < groundknifes.size(); i++) {
			tempDev = (PowerDevice) groundknifes.get(i);
			RuleExecute ruleExecute=new RuleExecute();
			RuleBaseMode rbmode=new RuleBaseMode();
			rbmode.setPd(tempDev);
			rbmode.setBeginStatus(rbm.getBeginStatus());
			rbmode.setEndState(rbm.getEndState());
			if(!ruleExecute.execute(rbmode)){
				return false;
			}
        }
		
		
		return true;
	}

}
