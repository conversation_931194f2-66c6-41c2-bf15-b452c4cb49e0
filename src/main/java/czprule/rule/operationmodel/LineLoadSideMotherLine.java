package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.DictionarysModel;
import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipRadioChoose;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 线路代线路执行类，传入线路进行线路代路操作

 */
public class LineLoadSideMotherLine implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {

		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		
		List<PowerDevice> xlswList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		
		if(xlswList.size()>0){
			if(!xlswList.get(0).getDeviceStatus().equals("0")){
				ShowMessage.view("["+xlswList.get(0).getPowerDeviceName()+"]不在运行状态！");
	        	return false;
			}
			List<PowerDevice> pldzList = RuleExeUtil.getDeviceList(xlswList.get(0), SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,
					CBSystemConstants.RunTypeKnifePL, "", false, true, true, true);
			if(pldzList.size()>0){
				if(pldzList.get(0).getDeviceStatus().equals("0")){
					ShowMessage.view("["+pldzList.get(0).getPowerDeviceName()+"]已经在合位！");
		        	return false;
				}
				List<PowerDevice> sideMotherLineList =RuleExeUtil.getDeviceDirectList(pldzList.get(0), SystemConstants.MotherLine);
				if(sideMotherLineList.size()>0){
					List<PowerDevice> xlList = RuleExeUtil.getDeviceList(sideMotherLineList.get(0), SystemConstants.InOutLine, SystemConstants.Switch, true, true, true);
					xlList.remove(pd);
					
					for(int i=0;i<xlList.size();i++){//排除非运行开关
						List<PowerDevice> swList = RuleExeUtil.getDeviceList(xlList.get(i), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
						if(swList.size()>0){
							if(!swList.get(0).getDeviceStatus().equals("0")){
								xlList.remove(i);
								i--;
							}
						}
					}
					
					EquipRadioChoose dcd = new EquipRadioChoose(SystemConstants.getMainFrame(), true, xlList, "请选择需要代路的线路");
					PowerDevice chooseDev = dcd.getChooseEquip();
					if(chooseDev==null){
						ShowMessage.view("请选择代路线路！");
			        	return false;
					}
					List<PowerDevice> dlxlswList = RuleExeUtil.getDeviceList(chooseDev, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					if(dlxlswList.size()>0){
						List<PowerDevice> dlpldzList = RuleExeUtil.getDeviceList(chooseDev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,
								CBSystemConstants.RunTypeKnifePL, "", false, true, true, true);
						if(dlpldzList.size()>0){
							if(dlpldzList.get(0).getDeviceStatus().equals("0")){
								ShowMessage.view("["+dlpldzList.get(0).getPowerDeviceName()+"]已经在合位！");
					        	return false;
							}
							
							String showMessage="请选择设备的目标状态";
							List<String> defaultStatusList = new ArrayList<String>();
							defaultStatusList.add("1");
							List<PowerDevice> switchs = new ArrayList<PowerDevice>();
							switchs.add(xlswList.get(0));
							EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, switchs, defaultStatusList, showMessage);
							Map tagStatusMap=dialog.getTagStatusMap();
							if(tagStatusMap.size() == 0)
								return false;
							
//							for (Iterator<Map.Entry<PowerDevice, String>> it = tagStatusMap.entrySet().iterator(); it.hasNext();) {
//								Map.Entry<PowerDevice, String> entry = it.next();
//								CBSystemConstants.LineTagStatus.put(entry.getKey(), entry.getValue());
//							}
							
							
							
							
							RuleExeUtil.deviceStatusChange(dlpldzList.get(0), "1", "0");
							RuleExeUtil.deviceStatusChange(pldzList.get(0), "1", "0");
							
//							RuleExeUtil.deviceStatusChange(dlxlswList.get(0), dlxlswList.get(0).getDeviceStatus(), "1");
							RuleExeUtil.deviceStatusExecute(xlswList.get(0), xlswList.get(0).getDeviceStatus(), StringUtils.ObjToString(tagStatusMap.get(xlswList.get(0))));
						}
					}
				
				}
			}
			
		}
		
		
		return true;
	}

}
