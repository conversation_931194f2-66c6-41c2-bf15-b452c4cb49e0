package czprule.rule.operationmodel;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoosePw;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

public class ChooseSwitchPowerReplyPw  implements RulebaseInf{
  EquipCheckChoosePw ecc = null;

  public boolean execute(RuleBaseMode rbm) {
    CBSystemConstants.chooseDeviceStatusPw.clear();
    if (CBSystemConstants.isCurrentSys) {
      final List loadMap = new ArrayList();

      if ((!CBSystemConstants.isSame.booleanValue()) && (CBSystemConstants.getSamepdlist().size() == 0)) {
        CBSystemConstants.isSame = Boolean.valueOf(true);
        new Thread(new Runnable()
        {
          public void run() {
            ChooseSwitchPowerReplyPw.this.ecc = 
              new EquipCheckChoosePw(SystemConstants.getMainFrame(), 
              false, loadMap, "请选择目标设备", CBSystemConstants.getCurRBM());
          }
        }).start();
        return false;
      }
      List<PowerDevice> switchs = new ArrayList();

      if (rbm.getPd().getDeviceType().equals(SystemConstants.SwitchSeparate))
        RuleExeUtil.deviceStatusReset(rbm.getPd(), rbm.getPd().getDeviceStatus(), "0");
      else
        RuleExeUtil.deviceStatusReset(rbm.getPd(), rbm.getPd().getDeviceStatus(), rbm.getEndState());
      PowerDevice pd;
      for (int i = 0; i < CBSystemConstants.getSamepdlist().size(); i++) {
        pd = (PowerDevice)CBSystemConstants.getSamepdlist().get(i);

        List<PowerDevice> list = RuleExeUtil.getPathByDevice(rbm.getPd(), pd, "", "", true, true);

        if (pd.getDeviceType().equals(SystemConstants.Switch)) {
          if (rbm.getPd().getDeviceType().equals(SystemConstants.Switch)) {
            switchs.add(rbm.getPd());
          }
          switchs.add(pd);
        } else if (rbm.getPd().getDeviceType().equals(SystemConstants.Switch)) {
          switchs.add(rbm.getPd());
        }

        if (list != null) {
          for (PowerDevice dev : list) {
            if ((!dev.equals(pd)) && (!dev.equals(rbm.getPd()))) {
              if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate))
                RuleExeUtil.deviceStatusReset(dev, dev.getDeviceStatus(), "0");
              else {
                RuleExeUtil.deviceStatusReset(dev, dev.getDeviceStatus(), "0");
              }
            }
          }
        }
      }

      if (CBSystemConstants.isMultiTicket) {
	        RuleExeUtil.deviceStatusSet(rbm.getPd(), rbm.getPd().getDeviceStatus(), (String)ChooseSwitchPowerCutPw.deviceTdBeginState.get(rbm.getPd().getPowerDeviceID()), true);
	
	        for (PowerDevice sw : switchs) {
	          RuleBaseMode devstatus = new RuleBaseMode();
	          devstatus.setBeginStatus(sw.getDeviceStatus());
	          devstatus.setEndState((String)ChooseSwitchPowerCutPw.deviceTdBeginState.get(sw.getPowerDeviceID()));
	          devstatus.setPd(sw);
	          CBSystemConstants.chooseDeviceStatusPw.add(devstatus);
	
	          RuleExeUtil.deviceStatusSet(sw, sw.getDeviceStatus(), (String)ChooseSwitchPowerCutPw.deviceTdBeginState.get(sw.getPowerDeviceID()), true);
	        }
      } else {
        exeute(rbm, switchs);
      }

      return true;
    }

    return true;
  }

  public boolean exeute(RuleBaseMode rbm, List<PowerDevice> switchs)
  {
    List defaultStatusList = new ArrayList();
    String showMessage = "请选择设备的目标状态";
    for (PowerDevice sw : switchs) {
      CBSystemConstants.tagStatusBeginMap.put(sw, "2");
      if (Integer.valueOf(rbm.getBeginStatus()).intValue() < Integer.valueOf(rbm.getEndState()).intValue())
        defaultStatusList.add("2");
      else {
        defaultStatusList.add("0");
      }
    }

    EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, switchs, defaultStatusList, showMessage);
    Map tagStatusMap = dialog.getTagStatusMap();

    if (tagStatusMap.size() == 0) {
      return false;
    }
    for (Iterator it = tagStatusMap.entrySet().iterator(); it.hasNext(); ) {
      RuleBaseMode devstatus = new RuleBaseMode();
      Map.Entry entry = (Map.Entry)it.next();
      devstatus.setBeginStatus(((PowerDevice)entry.getKey()).getDeviceStatus());
      devstatus.setEndState((String)entry.getValue());
      devstatus.setPd((PowerDevice)entry.getKey());
      ChooseSwitchPowerCutPw.handelSwitchAndKnife(entry);
      CBSystemConstants.chooseDeviceStatusPw.add(devstatus);
    }

    Collections.sort(CBSystemConstants.chooseDeviceStatusPw, new Comparator()
    {
     public int compare(Object arg0, Object arg1) {
    	  RuleBaseMode arg2 = (RuleBaseMode)arg0;
        if (arg2.getPd().getDeviceType().equals("pwmainlineswitch")) {
          return 0;
        }
        return 1;
      }
    });
    return true;
  }
}