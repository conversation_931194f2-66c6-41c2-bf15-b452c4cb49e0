/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 基础元件操作执行类
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;


public class OtherTransformChangeMotherLine implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		
		if(pd==null)
			return false;
		
		List<PowerDevice> swList = new ArrayList<PowerDevice>();
		
		List<PowerDevice> zbswList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer,
				true, true, true);
		RuleExeUtil.swapLowDeviceList(zbswList);
		//从高到低等级电压获取双母接线主变开关
		for(int i=0;i<zbswList.size();i++){
			if(!zbswList.get(i).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
				zbswList.remove(i);
				i--;
			}
		}
		
		for(PowerDevice zbsw:zbswList){
			List<PowerDevice> swDevList = RuleExeUtil.getDeviceList(zbsw, SystemConstants.Switch, SystemConstants.PowerTransformer,
					CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC, 
					CBSystemConstants.RunTypeSwitchML, true, true, false, true);
			if(swDevList.size()==0){//母线没有其他同电压等级主变开关，需要其他开关倒母
				swDevList = RuleExeUtil.getDeviceList(zbsw, SystemConstants.Switch, SystemConstants.PowerTransformer,
						CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC, 
						CBSystemConstants.RunTypeSwitchML, false, true, false, true);
				for(PowerDevice dev:swDevList){
					if(dev.getDeviceStatus().equals("0")){
						swList.add(dev);
					}
				}
			}
		}
		
		
		if(swList.size()>0){
			List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();
			EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, swList, "请选择需要倒母的开关：");
			chooseEquips=ecc.getChooseEquip();
			SwitchChangeMotherLine scml = new SwitchChangeMotherLine();
			for(PowerDevice dev :chooseEquips){
				RuleBaseMode rbmKG = new RuleBaseMode();
				rbmKG.setPd(dev);
				scml.execute(rbmKG);
			}
		}
     
		
		
		return true;
	}
}
