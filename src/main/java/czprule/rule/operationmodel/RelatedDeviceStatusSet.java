package czprule.rule.operationmodel;

import com.tellhow.czp.userrule.DeviceOperate;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.GetDeviceRunType;

/**
 * 关联设备状态和运行方式设置
 * <AUTHOR>
 *
 */
public class RelatedDeviceStatusSet implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		
		PowerDevice pd=rbm.getPd();
		//旁路刀闸或母线刀闸变位可能会改变母联兼旁路开关状态
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)||pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
			new GetDeviceRunType(pd);
		}
		//关联设备状态设置
		DeviceOperate.buildDeviceStatus(pd, pd.getDeviceStatus());
		return true;
	}

}
