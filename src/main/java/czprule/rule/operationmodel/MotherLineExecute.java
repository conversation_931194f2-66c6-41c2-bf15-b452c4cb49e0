package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.List;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.EMSService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2014年3月27日 下午8:23:38 
 */
public class MotherLineExecute implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		PowerDevice pd=rbm.getPd();
		if(rbm.getBeginStatus().equals("0")) {
			
			if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)) { //3/2
				//查找母线连接的主变，执行状态转换
				List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, true, true);
				for(PowerDevice tf : tfList) {
					boolean isExistSource = false;
					List<PowerDevice> lnList = RuleExeUtil.getDeviceList(tf, pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "", false, false, false, true);
					for(PowerDevice ln : lnList) {
						if(ln.getPowerVoltGrade() == tf.getPowerVoltGrade()) {
							isExistSource = true;
							break;
						}
					}
					if(!isExistSource&&CBSystemConstants.isCurrentSys) {
						int ok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), "["+tf.getPowerDeviceName()+"]是否要停电？",
								"提示", JOptionPane.YES_NO_OPTION);
						if (ok == JOptionPane.YES_OPTION) {
							boolean result = RuleExeUtil.deviceStatusChange(tf, tf.getDeviceStatus(), "2");
							if(!result)
								return false;
						}
					}
				}
			}
			else if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
				//如果是电源侧母线
				if(RuleExeUtil.isSourceSide(pd)) {
					List<PowerDevice> swList = null;
					//运行转热备用、热备用转冷备用
					if(Integer.valueOf(rbm.getBeginStatus()) < Integer.valueOf(rbm.getEndState())) {
						//2016 新增 出线到负荷. by Gny
						/*List<PowerDevice> mxList = RuleExeUtil.getDeviceList(pd, pd, SystemConstants.MotherLine, null, null, null, false, false, false, false, true);
						mxList.add(pd);
						boolean flag=false;
						List<PowerDevice> xlList = new ArrayList<PowerDevice>();
						if(mxList.size()>0){
							for(PowerDevice mx : mxList){
								xlList.addAll(RuleExeUtil.getDeviceList(mx, SystemConstants.InOutLine, null, false, true, false));
								for(PowerDevice xl : xlList){
									if(RuleExeUtil.getLineFlow(xl).equals("1")){
										flag=true;
										break;
									}
								}
							}
						}
						if(flag){
							for(PowerDevice xl : xlList){
								if(xl.getDeviceStatus().equals("0")){
									boolean result = RuleExeUtil.deviceStatusChange(xl, xl.getDeviceStatus(), rbm.getEndState());
									if(!result)
										return false;
								}
							}
						}*/
						//查找母线连接的主变和负荷线路，执行状态转换
						
						List<PowerDevice> lnList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
						for(PowerDevice ln : lnList) {
							if(EMSService.getService().getLineFlow(ln.getPowerStationID(), ln.getPowerDeviceID()).equals("2")) {
								List<PowerDevice> fhList = RuleExeUtil.getLineOtherSideList(ln);
								for(PowerDevice fh : fhList) {
									LineExecuteLoad lineLoad = new LineExecuteLoad();
									lineLoad.loadLine(fh);
									PowerDevice sw = RuleExeUtil.getDeviceSwitch(fh);
									RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "2");
								}
							}
						}
						
						List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
						for(PowerDevice tf : tfList) {
							if(tf.getDeviceStatus().equals("0") || tf.getDeviceStatus().equals("1")) {
								boolean result = RuleExeUtil.deviceStatusChange(tf, tf.getDeviceStatus(), "1");
								
								List<PowerDevice> kgList = RuleExeUtil.getDeviceList(tf, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);

								for(PowerDevice pd2:kgList){
									RuleExeUtil.deviceStatusExecuteJB(pd2, pd2.getDeviceStatus(), "2");
								}
								
								if(!result)
									return false;
							}
						}
						//线路开关排序
						swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
						if(swList.size() > 0) {
							for(int i = swList.size()-1; i >= 0; i--) {
								PowerDevice sw = swList.get(i);
								if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC))
									swList.remove(i);
								else if(rbm.getBeginStatus().equals("0") && sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))
									swList.remove(i);
								else if(RuleExeUtil.judgeSwitchFlow(pd).equals("1")) {
									
									swList.remove(i);
									swList.add(sw);
								}
							}
							
						}
						for(PowerDevice sw : swList) {
							RuleExeUtil.deviceStatusExecuteJB(sw, sw.getDeviceStatus(), "1");
						}
						
						if(rbm.getEndState().equals("2")||rbm.getEndState().equals("3")){
							for(PowerDevice sw : swList) {
								RuleExeUtil.deviceStatusExecuteJB(sw, sw.getDeviceStatus(), "2");
							}
						}
						
						
						//如果存在线路刀闸，则断开线路刀闸
						swList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXL, "", false, true, true, false);
						if(swList.size() > 0) {
							for(int i = swList.size()-1; i >= 0; i--) {
								if(RuleExeUtil.judgeKnifeFlow(pd).equals("1")) {
									PowerDevice sw = swList.get(i);
									swList.remove(i);
									swList.add(sw);
								}
							}
							for(PowerDevice sw : swList) {
								RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), rbm.getEndState());
							}
						}
						
						//主变转冷备用
						for(PowerDevice tf : tfList) {
							if(tf.getDeviceStatus().equals("0") || tf.getDeviceStatus().equals("1")) {
								boolean result = RuleExeUtil.deviceStatusChange(tf, tf.getDeviceStatus(), "2");
								if(!result)
									return false;
							}
						}
						
						for(PowerDevice sw : swList) {
							if(CBSystemConstants.LineTagStatus.containsKey(sw)){
								String tagStatus = CBSystemConstants.LineTagStatus.get(sw);
								RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), tagStatus);
							}else{
								RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), rbm.getEndState());
							}
						}
					}
					else {
						//如果存在线路刀闸，则合上线路刀闸
						swList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXL, "", false, true, true, false);
						if(swList.size() > 0) {
							for(int i = swList.size()-1; i >= 0; i--) {
								if(RuleExeUtil.judgeKnifeFlow(pd).equals("1")) {
									PowerDevice sw = swList.get(i);
									swList.remove(i);
									swList.add(sw);
								}
							}
							for(PowerDevice sw : swList) {
								RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), rbm.getEndState());
							}
						}
						
						//线路开关排序
						swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
						if(swList.size() > 0) {
							for(int i = swList.size()-1; i >= 0; i--) {
								PowerDevice sw = swList.get(i);
								if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC))
									swList.remove(i);
								else if(rbm.getEndState().equals("0") && sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))
									swList.remove(i);
								else if(RuleExeUtil.judgeSwitchFlow(pd).equals("1")) {
									
									swList.remove(i);
									swList.add(sw);
								}
							}
							
						}
						for(PowerDevice sw : swList) {
							if(CBSystemConstants.LineTagStatus.containsKey(sw)){
								String tagStatus = CBSystemConstants.LineTagStatus.get(sw);
								RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), tagStatus);
							}else{
								RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), rbm.getEndState());
							}
						}
						
						//查找母线连接的主变和负荷线路，执行状态转换
						List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
						for(PowerDevice tf : tfList) {
							boolean result = RuleExeUtil.deviceStatusChange(tf, tf.getDeviceStatus(), rbm.getEndState());
							if(!result)
								return false;
						}
						
						
						//如果是电源侧母线，查找母线连接的负荷线路，执行状态转换
						if(rbm.getEndState().equals("0")) {
							List<PowerDevice> lineList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, true, true);
							for(PowerDevice line : lineList) {
								if(RuleExeUtil.judgeLineFlow(line).equals("2")) {
									List<PowerDevice> loadList = CBSystemConstants.LineLoad.get(line.getPowerDeviceID());
									if(loadList != null) {
										for(PowerDevice load : loadList) {
											swList = RuleExeUtil.getDeviceList(load, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
											for (PowerDevice sw : swList) {
												boolean result = RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "0");
												if(!result)
													return false;
											}
											
											LineExecuteLoad loadExe = new LineExecuteLoad();
											loadExe.executeLoadBack(load);
										}
									}
								}
							}
						}
					}
				}
				else {
					String kgEndState = rbm.getEndState().endsWith("3")?"2":rbm.getEndState();
					String[] runTypeArr = {CBSystemConstants.RunTypeSwitchXL,CBSystemConstants.RunTypeSwitchFHC,CBSystemConstants.RunTypeSwitchML,CBSystemConstants.RunTypeSwitchJDB,CBSystemConstants.RunTypeSwitchDR+","+CBSystemConstants.RunTypeSwitchDK+","+CBSystemConstants.RunTypeSwitchZYB+","+CBSystemConstants.RunTypeSwitchQT};
					for(int i = 1; i <=Integer.valueOf(kgEndState); i++) {
						for(String runType : runTypeArr){
							List<PowerDevice> swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer,runType, null, false, true, true, true);
							for(PowerDevice sw : swList) {
								if(Integer.valueOf(sw.getDeviceStatus()) < i)
									RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), String.valueOf(i));
							}
						}
					}
					if(!kgEndState.equals("1")){
						List<PowerDevice> kfList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifePT+","+CBSystemConstants.RunTypeKnifeBLQ+","+CBSystemConstants.RunTypeKnifeQT, null, false, true, true, true);
						for(PowerDevice kf : kfList) {
							if(kf.getDeviceStatus().equals("0"))
								RuleExeUtil.deviceStatusChange(kf, kf.getDeviceStatus(), "1");
						}
					}
				}
			}
		}
		
		if(rbm.getEndState().equals("0")) {
			if(RuleExeUtil.isSourceSide(pd)) {
//				LineExecuteLoad lineLoad = new LineExecuteLoad();
				List<PowerDevice> lineList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
				if(lineList.size()==1){
					List<PowerDevice> swList = RuleExeUtil.getDeviceList(lineList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					if(swList.size()>0){
						RuleExeUtil.deviceStatusExecuteJB(swList.get(0), swList.get(0).getDeviceStatus(), "0");
					}
					
				}
//				for(PowerDevice line : lineList) {
//					
//					lineLoad.convertLineToOn(line);
//				}
				List<PowerDevice> tfList = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
				for(PowerDevice tf : tfList) {
					
					RuleExeUtil.deviceStatusChange(tf, tf.getDeviceStatus(), rbm.getEndState());
				}
			}
			else {
				List<PowerDevice> kfList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifePT+","+CBSystemConstants.RunTypeKnifeBLQ+","+CBSystemConstants.RunTypeKnifeQT, null, false, true, true, true);
				for(PowerDevice kf : kfList) {
					if(kf.getDeviceStatus().equals("1"))
						RuleExeUtil.deviceStatusChange(kf, kf.getDeviceStatus(), "0");
				}
				String[] runTypeArr = {CBSystemConstants.RunTypeSwitchJDB,CBSystemConstants.RunTypeSwitchML,CBSystemConstants.RunTypeSwitchFHC,CBSystemConstants.RunTypeSwitchXL};
				for(int i = 2; i >= 0; i--) {
					for(String runType : runTypeArr){
						if(runType.equals(CBSystemConstants.RunTypeSwitchML) && i == 0)
							continue; //母联开关不转运行
						List<PowerDevice> swList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer,runType, null, false, true, true, true);
						for(PowerDevice sw : swList) {
							if(Integer.valueOf(sw.getDeviceStatus()) > i)
								RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), String.valueOf(i));
						}
					}
				}
			}
		}
		
		return true;
	}

}
