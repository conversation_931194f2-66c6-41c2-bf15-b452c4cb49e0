/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：预研部操作票产品
 * 功能说明 : 设备连接的开关或者开关本身执行器
 * 作    者 : 张余平
 * 开发日期 : 2011-08-4
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;
import czprule.wordcard.dao.DeviceStateMentManager;

public class SwitchExecute implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		List<PowerDevice> switchs=new ArrayList<PowerDevice>();  //执行开关集合
		PowerDevice tempDev=null;
		if(pd.getDeviceType().equals(SystemConstants.Switch)){//如果是设备为开关，操作设备本身
			switchs.add(pd);
		}else{
			String devRunType = rbm.getDeviceruntype();
			
			//一、搜索设备连接的开关
			CommonSearch cs=new CommonSearch();
			Map<String, Object> inPara = new HashMap<String, Object>();
			Map<String, Object> outPara = new HashMap<String, Object>();
			
		
			List<PowerDevice> tempswitchs=new ArrayList<PowerDevice>();  //开关集合
		
			if(!"".equals(rbm.getTranType())){
				 PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
				 List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
				 if(sourceLineTrans==null||loadLineTrans==null){
					 if(CBSystemConstants.isCurrentSys) {
						 ShowMessage.view("请先设置线路两端变电站属性！");
						 return false;
					 }
					 else
						 return true;
				 }
				 if("S".equals(rbm.getTranType())){
					 inPara.put("oprSrcDevice", sourceLineTrans);
		             inPara.put("tagDevType", SystemConstants.Switch);
		             inPara.put("excDevType", SystemConstants.PowerTransformer);
		             inPara.put("excDevRunType", CBSystemConstants.RunTypeKnifeQT);
		             cs.execute(inPara, outPara);
		    	 	 inPara.clear();
		    	 	 tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
		    	  	 for (int i = 0; i < tempswitchs.size(); i++) {
		    	 		tempDev=(PowerDevice)tempswitchs.get(i);
		    	 		if (devRunType.equals("")) {
							switchs.add(tempDev);
						} else {
							if (tempDev.getDeviceRunType().equals(devRunType))
								switchs.add(tempDev);
						}
					 }
				 }else if("T".equals(rbm.getTranType())){
					 for (int i = 0; i < loadLineTrans.size(); i++) {
						inPara.put("oprSrcDevice", loadLineTrans.get(i));
						inPara.put("tagDevType", SystemConstants.Switch);
			            inPara.put("excDevType", SystemConstants.PowerTransformer);
			            inPara.put("excDevRunType", CBSystemConstants.RunTypeKnifeQT);
			            cs.execute(inPara, outPara);
			    		inPara.clear();
			    		tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
			    		for (int j = 0; j < tempswitchs.size(); j++) {
			    			tempDev=(PowerDevice)tempswitchs.get(j);
			    	 		if (devRunType.equals("")) {
								switchs.add(tempDev);
							} else {
								if (tempDev.getDeviceRunType().equals(devRunType))
									switchs.add(tempDev);
							}
						}
					}
				 }
				 else{
						inPara.put("oprSrcDevice", pd);
						inPara.put("tagDevType", SystemConstants.Switch);
			            inPara.put("excDevType", SystemConstants.PowerTransformer);
			            if(rbm.getPd().getDeviceType().equals(SystemConstants.InOutLine))
			            	inPara.put("excDevRunType", CBSystemConstants.RunTypeKnifeQT);
			            cs.execute(inPara, outPara);
			            tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
			   	  	    for (int i = 0; i < tempswitchs.size(); i++) {
				   	 		tempDev=(PowerDevice)tempswitchs.get(i);
				   	 		if (devRunType.equals("")) {
								switchs.add(tempDev);
							} else {
							    if (tempDev.getDeviceRunType().equals(devRunType))
								    switchs.add(tempDev);
							}
						}
					}
			}else{
				inPara.put("oprSrcDevice", pd);
				inPara.put("tagDevType", SystemConstants.Switch);
	            inPara.put("excDevType", SystemConstants.PowerTransformer);
	            if(rbm.getPd().getDeviceType().equals(SystemConstants.InOutLine))
	            	inPara.put("excDevRunType", CBSystemConstants.RunTypeKnifeQT);
	            cs.execute(inPara, outPara);
	            tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
	   	  	    for (int i = 0; i < tempswitchs.size(); i++) {
		   	 		tempDev=(PowerDevice)tempswitchs.get(i);
		   	 		if (devRunType.equals("")) {
						switchs.add(tempDev);
					} else {
					    if (tempDev.getDeviceRunType().equals(devRunType))
						    switchs.add(tempDev);
					}
				}
			}
			
			//根据用户设置的目标状态开关可能不执行状态转换
			for(Iterator it = switchs.iterator(); it.hasNext();) {
				tempDev = (PowerDevice)it.next();
				if(CBSystemConstants.LineTagStatus.containsKey(tempDev)) {
			 		String tagStatus = CBSystemConstants.LineTagStatus.get(tempDev);
			 		if(tagStatus.equals("")||rbm.getEndState().equals("")){
//			 			it.remove();
			 			continue;
			 		}
			 		if(rbm.getBeginStatus().equals("0") && tempDev.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
			 			;/////3/2接线
			 		else if(Integer.valueOf(rbm.getBeginStatus()) < Integer.valueOf(rbm.getEndState())) {
				 		if(Integer.valueOf(tagStatus) < Integer.valueOf(rbm.getEndState()))
				 			it.remove();
			 		}
			 		else {
			 			if(Integer.valueOf(tagStatus) > Integer.valueOf(rbm.getEndState()))
			 				it.remove();
			 		}
			 	}
				//此处注释，开关是否转检修由实际配置的规则决定
//				else if(rbm.getEndState().equals("3")) {
//					if(CBSystemConstants.getCurRBM().getPd().getDeviceType().equals(SystemConstants.InOutLine)) {
//						it.remove();
//					}
//					
//				}
				//旁路母线排除非旁路开关
				if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother) && !tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL))
					it.remove();
			}
			
			RuleExeUtil.swapDeviceList(switchs);
			//如果是3/2接线方式,中间的开关停电在前，送电在后
			if(switchs.size()==2&&switchs.get(1).getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)){
				PowerDevice switch1 = switchs.get(0);
				PowerDevice switch2 = switchs.get(1);
				boolean b1=RuleExeUtil.isSwMiddleInThreeSecond(switch1);
				boolean b2=Integer.valueOf(rbm.getBeginStatus()) < Integer.valueOf(rbm.getEndState());
			    if(b1&&b2 || (!b1&&!b2)){
			    	switchs.clear();
			    	switchs.add(switch1);
			    	switchs.add(switch2);	
			    }else{
			    	switchs.clear();
			    	switchs.add(switch2);
			    	switchs.add(switch1);
			    }
			}
		}
		
		
		
	
		for (int i = 0; i < switchs.size(); i++) {
			
			tempDev = (PowerDevice) switchs.get(i);
			if(!rbm.getBeginStatus().equals("")&&!rbm.getBeginStatus().equals(tempDev.getDeviceStatus()))
				continue;
			String beginStatus="";
			if(rbm.getBeginStatus().equals(""))
				beginStatus=tempDev.getDeviceStatus();
			else
				beginStatus=rbm.getBeginStatus();
			RuleExecute ruleExecute=new RuleExecute();
			RuleBaseMode rbmode=new RuleBaseMode();
			DeviceStateMentManager dsmm = new DeviceStateMentManager();
			rbmode.setPd(tempDev);
			rbmode.setBeginStatus(beginStatus);
			rbmode.setEndState(rbm.getEndState());
			rbmode.setStateCode(dsmm.getStateCodeByStatus(tempDev.getDeviceType(), rbm.getEndState(), "9")); //默认取基本操作,操作类型代码是9
			if(!ruleExecute.execute(rbmode)){
				return false;
			}
        }
		
		return true;
	}

}
