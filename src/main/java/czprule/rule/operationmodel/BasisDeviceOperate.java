/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 基础设备执行类
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.operationmodel;


import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;

public class BasisDeviceOperate implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
        
		if(!"".equals(rbm.getTranType())){
			 PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
			 List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
			 if(sourceLineTrans==null||loadLineTrans==null){
				 if(CBSystemConstants.isCurrentSys) {
					 ShowMessage.view("请先设置线路两端变电站属性！");
					 return false;
				 }
				 else
					 return true;
			 }
			 if("S".equals(rbm.getTranType())){
			        DispatchTransDevice dtd=new DispatchTransDevice();
			        dtd.setTransDevice(sourceLineTrans);
			        dtd.setParentDevice(CBSystemConstants.getParentDev());
			        dtd.setBeginstatus(rbm.getBeginStatus());
			        dtd.setEndstate(rbm.getEndState());
			        dtd.setFlag("0");
			        CBSystemConstants.putDtdMap(dtd);
			        
			        String endStatus=CBSystemConstants.getDeviceStateValue(rbm.getEndState());
			        if(!"-1".equals(endStatus))
			        	sourceLineTrans.setDeviceStatus(endStatus);//设置内存中设备状态
	            
			 }else{
				 PowerDevice tempDev=null;
				 for (int i = 0; i < loadLineTrans.size(); i++) {
					    tempDev=loadLineTrans.get(i);
					    DispatchTransDevice dtd=new DispatchTransDevice();
				        dtd.setTransDevice(tempDev);
				        dtd.setParentDevice(CBSystemConstants.getParentDev());
				        dtd.setBeginstatus(rbm.getBeginStatus());
				        dtd.setEndstate(rbm.getEndState());
				        dtd.setFlag("0");
				        CBSystemConstants.putDtdMap(dtd);
				        
				        String endStatus=CBSystemConstants.getDeviceStateValue(rbm.getEndState());
				        if(!"-1".equals(endStatus))
				        	tempDev.setDeviceStatus(endStatus);//设置内存中设备状态
					
				}
			 }
		}else if(rbm.getBeginStatus().equals(pd.getDeviceStatus())){
			//保存DTD历史状态
	        DispatchTransDevice dtd=new DispatchTransDevice();
	        dtd.setTransDevice(pd);
	        dtd.setParentDevice(CBSystemConstants.getParentDev());
	        dtd.setBeginstatus(rbm.getBeginStatus());
	        dtd.setEndstate(rbm.getEndState());
	        dtd.setFlag("0");
	        CBSystemConstants.putDtdMap(dtd);
	        
	        String endStatus=CBSystemConstants.getDeviceStateValue(rbm.getEndState());
	        if(!"-1".equals(endStatus))
	             pd.setDeviceStatus(endStatus);//设置内存中设备状态
			
		}
        
		return true;
	}

}
