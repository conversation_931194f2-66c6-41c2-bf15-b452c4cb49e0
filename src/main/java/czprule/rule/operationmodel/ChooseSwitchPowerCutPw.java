package czprule.rule.operationmodel;

import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoosePw;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

public class ChooseSwitchPowerCutPw
  implements RulebaseInf
{
  EquipCheckChoosePw ecc = null;
  public static Map<String, String> deviceTdBeginState = new HashMap();
  public static Map<String, String> deviceTdEndState = new HashMap();

  public boolean execute(RuleBaseMode rbm) {
    CBSystemConstants.chooseDeviceStatusPw.clear();
    if (CBSystemConstants.isCurrentSys) {
     final List loadMap = new ArrayList();

      if ((!CBSystemConstants.isSame.booleanValue()) && (CBSystemConstants.getSamepdlist().size() == 0)) {
        CBSystemConstants.isSame = Boolean.valueOf(true);
        new Thread(new Runnable()
        {
          public void run() {
            ChooseSwitchPowerCutPw.this.ecc = 
              new EquipCheckChoosePw(SystemConstants.getMainFrame(), 
              false, loadMap, "请选择目标设备", CBSystemConstants.getCurRBM());
          }
        }).start();
        return false;
      }
      List<PowerDevice> switchs = new ArrayList();

      if (rbm.getPd().getDeviceType().equals(SystemConstants.SwitchSeparate))
        RuleExeUtil.deviceStatusReset(rbm.getPd(), rbm.getPd().getDeviceStatus(), "1");
      PowerDevice pd;
      for (int i = 0; i < CBSystemConstants.getSamepdlist().size(); i++) {
        pd = (PowerDevice)CBSystemConstants.getSamepdlist().get(i);

        List<PowerDevice> list = RuleExeUtil.getPathByDevice(rbm.getPd(), pd, "", "", true, true);

        if (pd.getDeviceType().equals(SystemConstants.Switch)) {
          if (rbm.getPd().getDeviceType().equals(SystemConstants.Switch)) {
            switchs.add(rbm.getPd());
          }
          switchs.add(pd);
        } else if (rbm.getPd().getDeviceType().equals(SystemConstants.Switch)) {
          switchs.add(rbm.getPd());
        }

        if (list != null) {
          for (PowerDevice dev : list) {
            if ((!dev.equals(pd)) && (!dev.equals(rbm.getPd()))) {
              if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate))
                RuleExeUtil.deviceStatusReset(dev, dev.getDeviceStatus(), "1");
              else {
                RuleExeUtil.deviceStatusReset(dev, dev.getDeviceStatus(), "2");
              }
            }
          }
        }

      }

      if (CBSystemConstants.isMultiTicket) {
        deviceTdBeginState.put(rbm.getPd().getPowerDeviceID(), rbm.getBeginStatus());
        deviceTdEndState.put(rbm.getPd().getPowerDeviceID(), rbm.getEndState());

        for (PowerDevice pdd : switchs) {
          deviceTdBeginState.put(pdd.getPowerDeviceID(), pdd.getDeviceStatus());
          deviceTdEndState.put(pdd.getPowerDeviceID(), rbm.getEndState());
        }

        return exeute(rbm, switchs);
      }
      return exeute(rbm, switchs);
    }

    return true;
  }

  public boolean exeute(RuleBaseMode rbm, List<PowerDevice> switchs)
  {
    List defaultStatusList = new ArrayList();
    String showMessage = "请选择设备的目标状态";
    if (Integer.valueOf(rbm.getBeginStatus()).intValue() < Integer.valueOf(rbm.getEndState()).intValue()) {
      defaultStatusList.add("2");
      defaultStatusList.add("2");
    } else {
      defaultStatusList.add("0");
      defaultStatusList.add("0");
    }

    EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, switchs, defaultStatusList, showMessage);
    Map tagStatusMap = dialog.getTagStatusMap();

    if (tagStatusMap.size() == 0) {
      return false;
    }
    for (Iterator it = tagStatusMap.entrySet().iterator(); it.hasNext(); ) {
      RuleBaseMode devstatus = new RuleBaseMode();
      Map.Entry entry = (Map.Entry)it.next();
      PowerDevice powerDevice = (PowerDevice) entry.getKey();
      devstatus.setBeginStatus(powerDevice.getDeviceStatus());
      devstatus.setEndState((String)entry.getValue());
      devstatus.setPd(powerDevice);
      handelSwitchAndKnife(entry);

      CBSystemConstants.chooseDeviceStatusPw.add(devstatus);
    }

    Collections.sort(CBSystemConstants.chooseDeviceStatusPw, new Comparator()
    {
      public int compare(Object arg0, Object arg1) {
    	  RuleBaseMode arg2 = (RuleBaseMode)arg0;
        if (arg2.getPd().getDeviceType().equals("pwmainlineswitch")) {
          return 0;
        }
        return 1;
      }
    });
    return true;
  }

  /**
   * 处理开关及开关直连的刀闸
   * @param entry 包含开关设备和客户端页面选择的目标状态
   */
  static void handelSwitchAndKnife(Entry entry) {
    PowerDevice powerDevice = (PowerDevice) entry.getKey();
    //如果开关变更状态包含有【热备用-冷备用】，则将其直联的刀闸的状态置为分位
    if (Integer.parseInt(powerDevice.getDeviceStatus()) <= 1 && Integer.parseInt((String) entry.getValue()) >= 2) {
      RuleExeUtil.deviceStatusSet(powerDevice, powerDevice.getDeviceStatus(), (String)entry.getValue());//先拉开开关
      List<PowerDevice> knifePds = RuleExeUtil.getDeviceDirectList(powerDevice, SystemConstants.SwitchSeparate);
      for (PowerDevice knifePd : knifePds) {
        RuleExeUtil.deviceStatusSet(knifePd, knifePd.getDeviceStatus(), "1");//拉开刀闸
      }
    }
    //如果开关变更状态包含有【冷备用-热备用】，则将其直联的刀闸的状态置为合位
    if (Integer.parseInt(powerDevice.getDeviceStatus()) >= 2 && Integer.parseInt((String) entry.getValue()) <= 1) {
      List<PowerDevice> knifePds = RuleExeUtil.getDeviceDirectList(powerDevice, SystemConstants.SwitchSeparate);
      for (PowerDevice knifePd : knifePds) {
        RuleExeUtil.deviceStatusSet(knifePd, knifePd.getDeviceStatus(), "0");//拉开刀闸
      }
      RuleExeUtil.deviceStatusSet(powerDevice, powerDevice.getDeviceStatus(), (String)entry.getValue());//后合上开关
    }
  }
}