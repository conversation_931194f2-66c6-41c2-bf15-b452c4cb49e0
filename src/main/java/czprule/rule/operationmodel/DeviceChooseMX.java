package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 关联设备目标状态选择器
 * 作    者: 郑柯
 * 开发日期: 2013年8月17日 上午10:13:38 
 */
public class DeviceChooseMX implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if(!CBSystemConstants.isCurrentSys)
			return true;
		if(CBSystemConstants.jh_tai == 1)
			return true;
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return true;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return true;
		if(!rbm.getPd().equals(pd))
			return true;
		
		if(pd.getDeviceType().equals(SystemConstants.PowerTransformer) && !RuleUtil.isTransformerNQ(pd))
			return true;

		if(pd.getDeviceType().equals(SystemConstants.MotherLine)){
			
		
			String devRunType = curRBM.getDeviceruntype();
			List<PowerDevice> devs=new ArrayList<PowerDevice>();  //执行设备
			List<String> defaultStatusList = new ArrayList<String>();
			defaultStatusList.add("");
			String showMessage="请选择设备挂接的母线";
			
			CommonSearch cs=new CommonSearch();
		    Map<String,Object> inPara = new HashMap<String,Object>();
	        Map<String,Object> outPara = new HashMap<String,Object>();
	        List<PowerDevice> mxList=null;
	 	    inPara.put("oprSrcDevice", pd);
	        inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
	        inPara.put("isStopOnBusbarSection", "false");
	        inPara.put("isStopOnTagDevType", "false");
	        inPara.put("isStopOnDiffVolt", "true");
	        cs.execute(inPara, outPara);
	        inPara.clear();
	        mxList = (ArrayList) outPara.get("linkedDeviceList");
	        mxList.add(pd);
	        RuleExeUtil.swapDeviceListNum(mxList);
	        
	        //根据母线找链接的线路和主变开关
	        Map<String,PowerDevice> xlzbkgMap = new HashMap<String,PowerDevice>();//连接线路主变开关集合
	        for(int i=0;i<mxList.size();i++){
//	        	inPara =new HashMap<String,Object>();
	        	inPara.put("oprSrcDevice", mxList.get(i));
			    inPara.put("tagDevType", SystemConstants.InOutLine); 
		        cs.execute(inPara, outPara);
		        inPara.clear();
		        List<PowerDevice> lineList = (ArrayList) outPara.get("linkedDeviceList");
		        for(int j=0;j<lineList.size();j++){
		        	PowerDevice devLine = lineList.get(j);
		        	List<PowerDevice> sw = RuleExeUtil.getDeviceList(devLine, SystemConstants.Switch, SystemConstants.PowerTransformer,
		        			true, true, true);
		        	if(sw.size()>0&&
		        			(sw.get(0).getDeviceStatus().equals("2")||sw.get(0).getDeviceStatus().equals("3"))){
		        		continue;
		        	}
//		        	if((!devLine.getDeviceStatus().equals("2"))&&(!devLine.getDeviceStatus().equals("3"))){
	        		if(devLine.getPowerVoltGrade()==mxList.get(i).getPowerVoltGrade()){
	        			if(devLine.getPowerDeviceName().contains("线")){
	        				xlzbkgMap.put(devLine.getPowerDeviceName().substring(0, devLine.getPowerDeviceName().lastIndexOf("线")+1),devLine);
	        			}else{
	        				xlzbkgMap.put(devLine.getPowerDeviceName(),devLine);
	        			}
	        			
	        		}
//		        	}
//		        	xlzbkgMap.put(CZPService.getService().getDevName(devLine),devLine);
		        }
		        
		        inPara =new HashMap<String,Object>();
	        	inPara.put("oprSrcDevice", mxList.get(i));
			    inPara.put("tagDevType", SystemConstants.Switch); 
		        cs.execute(inPara, outPara);
		        inPara.clear();
		        List<PowerDevice> kgList = (ArrayList) outPara.get("linkedDeviceList");
		        for(int j=0;j<kgList.size();j++){
		        	PowerDevice devkg = kgList.get(j);
		        	//主变开关     及旁路开关
		        	if(devkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)||devkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)
		        			||devkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)||devkg.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
		        		if(devkg.getPowerVoltGrade()==mxList.get(i).getPowerVoltGrade()){
		        			if((!devkg.getDeviceStatus().equals("2"))&&(!devkg.getDeviceStatus().equals("3"))){
		        				xlzbkgMap.put(CZPService.getService().getDevName(devkg),devkg);
		        			}	
		        		}	
		        	}
		        }
		        
		        

	        }
	        
	        
	        for(Map.Entry<String, PowerDevice> map: xlzbkgMap.entrySet()){      
	        	PowerDevice xlzbkg = map.getValue();
	        	devs.add(xlzbkg);
	        }
	  
	        
			
			
			
	        EquipChooseMX dialog = new EquipChooseMX(SystemConstants.getMainFrame(), true, devs, showMessage);
			Map<PowerDevice,String> tagStatusMap=dialog.getTagStatusMap();
			
			
			
			if(tagStatusMap.size() == 0){
				JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "没有调整设备！", "提示", JOptionPane.WARNING_MESSAGE);
				return false;
			}
			
			for(Map.Entry<PowerDevice, String> map: tagStatusMap.entrySet()){      
	        	PowerDevice device = map.getKey();
	        	if(device.getDeviceType().equals(SystemConstants.Switch)){
	        		SwitchChangeMotherLine scml = new SwitchChangeMotherLine();
					RuleBaseMode rbmZBKG = new RuleBaseMode();
					rbmZBKG.setPd(device);
					if(!scml.execute(rbmZBKG)){
						return false;
					}
					if(!isMXruning(device)){
						ShowMessage.view(CZPService.getService().getDevName(device)+"所倒母线不在运行状态！");
						return false;
					}
	        	}else{
	        	    inPara =new HashMap<String,Object>();
	   	        	inPara.put("oprSrcDevice", device);
	   			    inPara.put("tagDevType", SystemConstants.Switch); 
	   		        cs.execute(inPara, outPara);
	   		        List<PowerDevice> switchDevices = (ArrayList) outPara.get("linkedDeviceList");
	   		        for(int i=0;i<switchDevices.size();i++){
	   		        	if(switchDevices.get(i).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
	   		        		SwitchChangeMotherLine scml = new SwitchChangeMotherLine();
							RuleBaseMode rbmXLKG = new RuleBaseMode();
							rbmXLKG.setPd(switchDevices.get(i));
							if(!scml.execute(rbmXLKG)){
								return false;
							}
							if(!isMXruning(device)){
								ShowMessage.view(CZPService.getService().getDevName(device)+"所倒母线不在运行状态！");
								return false;
							}
	   		        	}
	   		        }
	        	}
	        	
	        
		    }
			
			
		}

		return true;
	}
	
	//执行后判断开关倒母后连接的母线是否为运行状态
	private boolean isMXruning(PowerDevice switchKG){
		List<PowerDevice> mxList = RuleExeUtil.getDeviceList(switchKG,
				SystemConstants.MotherLine, SystemConstants.PowerTransformer, false,true, true);
		for(int i=0;i<mxList.size();i++){
			PowerDevice mxDevice = mxList.get(i);
			if(!mxDevice.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
				if(!mxDevice.getDeviceStatus().equals("0")){
					return false;
				}
			}
		}
		
		return true;
	}

}
