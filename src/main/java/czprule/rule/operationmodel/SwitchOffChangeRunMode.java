package czprule.rule.operationmodel;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;

public class SwitchOffChangeRunMode implements RulebaseInf {
	public boolean execute(RuleBaseMode rbm) {
		
		boolean result = true;
		PowerDevice pd = rbm.getPd();
		
		if(!CBSystemConstants.getCurOperateDevs().get(1).equals(pd) || !CBSystemConstants.getParentDev().equals(pd))
			return true;
		
		if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
			return true;
		
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
			boolean b = true;
			PowerDevice sw = rbm.getPd();
			List<PowerDevice> lnList = RuleExeUtil.getDeviceList(sw, SystemConstants.InOutLine, SystemConstants.PowerTransformer, false, true, true);
			if(lnList.size() == 1 && RuleExeUtil.judgeLineFlow(lnList.get(0)).equals("2")) {
				List<PowerDevice> lineList = RuleExeUtil.getLineOtherSideList(lnList.get(0));
				for(PowerDevice line : lineList) {
					LineExecuteLoad loadExe = new LineExecuteLoad();
					loadExe.loadLine(line);
					PowerDevice dev = RuleExeUtil.getDeviceSwitch(line);
					if(dev != null)
						RuleUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "2");
				}
			}
			else {
				
				
			}
			return b;
		}
		else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)) {

			//return new SwitchZBShiftLoad().execute(rbm);
		}
		else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)) {

			//return new SwitchZBShiftLoad().execute(rbm);
		}
		else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
			//return new SwitchMLLoadSharing().execute(rbm);
		}
		else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)){
			//return new SwitchPLLoadSharing().execute(rbm);
		}
		else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
			//return new SwitchMLLoadSharing().execute(rbm);
		}
		
		return true;
	
	}

}
