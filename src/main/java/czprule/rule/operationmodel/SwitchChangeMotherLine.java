/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 开关导母操作基础执行类
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

public class SwitchChangeMotherLine implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		if(!pd.getDeviceType().equals(SystemConstants.Switch)){
        	ShowMessage.view("["+pd.getPowerDeviceName()+"]不是开关！");
        	return false;
        }
		if("01".indexOf(pd.getDeviceStatus())<0){
			ShowMessage.view("导母开关["+pd.getPowerDeviceName()+"]必须处于运行或者热备用状态！");
        	return false;
		}
		if(!pd.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
			ShowMessage.view("导母开关["+pd.getPowerDeviceName()+"]必须是双母接线方式！");
        	return false;
		}
		CommonSearch cs=new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
        inPara.put("tagDevType", SystemConstants.SwitchSeparate);
        inPara.put("isSearchDirectDevice", true);
        cs.execute(inPara, outPara);
		inPara.clear();
		PowerDevice tempDev=null;
		BasisCompOperate bco=new BasisCompOperate();
		List tempKnifes = (ArrayList) outPara.get("linkedDeviceList");
		List tempKnifes1 = new ArrayList(); //一侧母线刀闸
		List tempKnifes2 = new ArrayList(); //另一侧母线刀闸
		List tempKnifes3 = new ArrayList(); //母线对侧刀闸
		
		for (int i = 0; i < tempKnifes.size(); i++) {
			tempDev = (PowerDevice) tempKnifes.get(i);
            if(tempDev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){
            	if(!tempDev.getDeviceStatus().equals("0")){
            		tempKnifes1.add(tempDev);
            	}
            	else
            		tempKnifes2.add(tempDev);
            }
            else
            	tempKnifes3.add(tempDev);
        }
		
		if(pd.getDeviceStatus().equals("0")) {
			return setKnifesOn(tempKnifes1) && setKnifesOff(tempKnifes2);
		}
		else if(pd.getDeviceStatus().equals("1")) {
			return setKnifesOff(tempKnifes3) && setKnifesOff(tempKnifes2) && setKnifesOn(tempKnifes1) && setKnifesOn(tempKnifes3);
		}
		
		return true;
	}
	
	private boolean setKnifesOff(List tempKnifes) {
		boolean result = true;
		for (int i = 0; i < tempKnifes.size(); i++) {
			PowerDevice tempDev = (PowerDevice) tempKnifes.get(i);
			result = RuleExeUtil.deviceStatusExecuteJB(tempDev, "0", "1");
			if(!result)
				return false;
        }
		return true;
	}
	
	private boolean setKnifesOn(List tempKnifes) {
		boolean result = true;
		for (int i = 0; i < tempKnifes.size(); i++) {
			PowerDevice tempDev = (PowerDevice) tempKnifes.get(i);
			result = RuleExeUtil.deviceStatusExecuteJB(tempDev, "1", "0");
			if(!result)
				return false;
        }
		return true;
	}

}
