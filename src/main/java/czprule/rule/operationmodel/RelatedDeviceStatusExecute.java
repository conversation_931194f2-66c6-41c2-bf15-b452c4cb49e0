package czprule.rule.operationmodel;

import java.util.Iterator;
import java.util.Map;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 关联设备目标状态选择器执行类
 * 作    者: 郑柯
 * 开发日期: 2013年8月17日 上午10:13:38 
 */
public class RelatedDeviceStatusExecute implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(!CBSystemConstants.isCurrentSys)
			return true;
		RuleBaseMode curRBM = CBSystemConstants.getCurRBM();
		if(curRBM==null)
			return false;
		PowerDevice pd=curRBM.getPd();
		if(pd==null)
			return false;
		if(!rbm.getPd().equals(pd))
			return true;
		
		for (Iterator<Map.Entry<PowerDevice, String>> it = CBSystemConstants.LineTagStatus.entrySet().iterator(); it.hasNext();) {
			Map.Entry<PowerDevice, String> entry = it.next();
			RuleExeUtil.deviceStatusExecute(entry.getKey(), entry.getKey().getDeviceStatus(), entry.getValue());
		}
		
		return true;
	}
}
