package czprule.rule.operationmodel;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 母线倒母操作
 * 作    者 : 张余平
 * 开发日期 : 2012-03-15
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

public class MotherLineLoadBack implements RulebaseInf {
		
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();//获取到了执行操作的母线
		if(pd==null)
			return false;
		Map<String,Object> inMap=new HashMap<String,Object>();	//输入参数
		Map<String,Object> outMap=new HashMap<String,Object>();	//输出结果
		CommonSearch search=new CommonSearch();
		inMap.put("oprSrcDevice",pd);//搜索起点设备为该母线
		inMap.put("tagDevType", SystemConstants.Switch);//目标设备为开关
		inMap.put("excDevType", SystemConstants.PowerTransformer);//排除搜索设备为主变
		inMap.put("isSearchOffPath",true);	//不搜索断开路径
		search.execute(inMap, outMap);//search执行搜索
		inMap.clear();
		List<PowerDevice> switchList=(List<PowerDevice>) outMap.get("linkedDeviceList");//获取到搜索到的开关
		//执行开关导母 如何所有的开关都导母失败则母线失败
		PowerDevice switchDev=null;
		boolean isloadSwitch = true;
		for(Object obj:switchList){//遍历搜索出来的开关
			isloadSwitch = false;
			switchDev=(PowerDevice) obj;
			//如果开关设备当前状态为运行，为热备时继续往下执行
			if(!switchDev.getDeviceStatus().equals("0") && !switchDev.getDeviceStatus().equals("1"))
				continue;	
//			if(!switchDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
//				&&!switchDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)
//				  &&!switchDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)
//				  &&!switchDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchZYB))
//				continue;
			//如果开关接线方式不为双母接线方式，则跳出继续循环
			if(!switchDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
				continue;
			List<PowerDevice> knifeList = RuleExeUtil.getDeviceDirectList(switchDev, SystemConstants.SwitchSeparate);// 获取开关两侧的刀闸
			for(Object obj1:knifeList){
				PowerDevice knife=(PowerDevice) obj1;
				String defaultstate = knife.getPowerDeviceDefaultSta();
				if(defaultstate.equals("0") && !knife.getDeviceStatus().equals("0") && knife.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)){//如果为0，
					RuleBaseMode rbmode=new RuleBaseMode();
					rbmode.setPd(switchDev);//将此开关放入规则操作设备
					rbmode.setBeginStatus(rbm.getBeginStatus());//将设备起始状态置为设备起始状态
					rbmode.setEndState(rbm.getEndState());//将设备结果状态置为设备结果状态
					new SwitchChangeMotherLine().execute(rbmode);//执行此规则
				}
			}	
		}
		if(isloadSwitch){
			ShowMessage.viewWarning(SystemConstants.getMainFrame(), "当前母线上不存在处于运行的开关，不需要进行导母操作！");
		}
		return true;
		
		/*
		//导母操作
		if("6".equals(rbm.getEndState())){
				//搜索开关
				inMap.put("oprSrcDevice",pd);
				inMap.put("tagDevType", SystemConstants.Switch);
				inMap.put("excDevType", SystemConstants.PowerTransformer);
				inMap.put("isSearchOffPath",false);	//不搜索断开路径
				search.execute(inMap, outMap);
				inMap.clear();
				List<PowerDevice> switchList=(List<PowerDevice>) outMap.get("linkedDeviceList");
				//执行开关导母 如何所有的开关都导母失败则母线失败
				PowerDevice switchDev=null;
				for(Object obj:switchList){
					switchDev=(PowerDevice) obj;
					if(!switchDev.getDeviceStatus().equals("0") && !switchDev.getDeviceStatus().equals("1"))
						continue;	
					if(!switchDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
						&&!switchDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)
						  &&!switchDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC))
						continue;
					if(!switchDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
						continue;
					RuleExecute ruleExecute=new RuleExecute();
					RuleBaseMode rbmode=new RuleBaseMode();
					rbmode.setPd(switchDev);
					rbmode.setBeginStatus(rbm.getBeginStatus());
					rbmode.setEndState(rbm.getEndState());
					ruleExecute.execute(rbmode);
					
				}
				return true;
		}
		//倒回正常运行方式
		if("7".equals(rbm.getEndState())){
				
			    //搜索通路是断开的开关
			    List linkSwitchs=new ArrayList();
				inMap.put("oprSrcDevice",pd);
				inMap.put("tagDevType", SystemConstants.Switch);
				inMap.put("excDevType", SystemConstants.PowerTransformer);
				search.execute(inMap, outMap);
				inMap.clear();
				HashMap<PowerDevice,ArrayList<PowerDevice>> switchList=(HashMap<PowerDevice,ArrayList<PowerDevice>>) outMap.get("pathList");	 			
				//执行开关导母 如何所有的开关都导母失败则母线失败
				List onePath=null; //每条通路
				PowerDevice switchDev=null; //通路中的开关
				PowerDevice knifeDev=null; //通路中的刀闸
				for (Iterator iterator = switchList.keySet().iterator(); iterator.hasNext();) {
					switchDev = (PowerDevice) iterator.next();
					onePath=switchList.get(switchDev);
					if(onePath.size()!=3)
						continue;
					switchDev=(PowerDevice)onePath.get(2);
					if(!switchDev.getDeviceStatus().equals("0"))
						continue;	
					if(!switchDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)
						&&!switchDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)
						  &&!switchDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC))
						continue;
					if(!switchDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
						continue;
					knifeDev=(PowerDevice)onePath.get(1);
					if(knifeDev.getDeviceStatus().equals("0"))
						continue;
					linkSwitchs.add(switchDev);
				}
				if(linkSwitchs.size()==0)
					return true;
				
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(),true,linkSwitchs,"请选择倒回母线的开关:");
				List chooseSwitchs=ecc.getChooseEquip();
				for(Object obj:chooseSwitchs){
					switchDev=(PowerDevice) obj;
					RuleExecute ruleExecute=new RuleExecute();
					RuleBaseMode rbmode=new RuleBaseMode();
					rbmode.setPd(switchDev);
					rbmode.setBeginStatus(rbm.getBeginStatus());
					rbmode.setEndState("6");
					ruleExecute.execute(rbmode);
				}
				return true;
		}
		return true;
		*/
	}
	
}
