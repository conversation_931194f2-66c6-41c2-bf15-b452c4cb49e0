package czprule.rule.operationmodel;

import java.util.Iterator;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipRadioChoose;
import czprule.system.CBSystemConstants;

/**
 * 母联开关拉合刀闸硬联母线执行类
 * <AUTHOR>
 *
 */
public class SwitchJoinExecute  implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if(!CBSystemConstants.isCurrentSys){
			return true;
		}
		
		PowerDevice pd = rbm.getPd(); //得到母联开关
		
		//非母联开关 返回
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))
			return true;
		if(!RuleExeUtil.isSwitchDoubleML(pd))
			return true;
		//判断接线方式，非双母直接返回
		if(!pd.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
			return true;
		//得到母联开关连接的母线
		List<PowerDevice> mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
		if(mlList.size() > 0) {
			//查找母线上所有开关
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(mlList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			//转冷备用选择开关合闸
			//排除非运行开关及母联开关
			
			for(Iterator it = swList.iterator(); it.hasNext();) {
				PowerDevice sw = (PowerDevice)it.next();
				if(!sw.getDeviceStatus().equals("0"))
					it.remove();
				else if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))
					it.remove();
				else if(sw.equals(CBSystemConstants.getSourceDev())) //当前操作设备不能做硬联
					it.remove();
			}
			if(rbm.getBeginStatus().equals("0")) { 
			//选择操作刀闸关联的开关
			RuleExeUtil.swapDeviceList(swList);
			EquipRadioChoose dcd = new EquipRadioChoose(
					SystemConstants.getMainFrame(), true, swList,"请选择需要合上的刀闸所属开关");
			PowerDevice sw = dcd.getChooseEquip();
			if(sw==null)return false;
			//查找拉开的刀闸，执行合上操作
			List<PowerDevice> ssList = RuleExeUtil.getDeviceDirectList(sw, SystemConstants.SwitchSeparate);
			for(Iterator it = ssList.iterator(); it.hasNext();) {
				PowerDevice ss = (PowerDevice)it.next();
				if(!ss.getDeviceStatus().equals("0"))
					RuleExeUtil.deviceStatusChange(ss, ss.getDeviceStatus(), "0");
			}
		}
			//转运行开关拉上闸
			if(rbm.getEndState().equals("0")) { 
				//刀闸合上态计数；
				for (PowerDevice dev:swList){
					int cout =0;
					List<PowerDevice> ssList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
					for(PowerDevice ss:ssList){
					 if(ss.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)&&ss.getDeviceStatus().equals("0")){
							cout++;
						}
						
					}
					
					if(cout==2){
						for (Iterator iterator = ssList.iterator(); iterator.hasNext();) {
							PowerDevice it = (PowerDevice)iterator.next();;
							if(it.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeDY)){
								iterator.remove();
							}
						}
						EquipRadioChoose dcd = new EquipRadioChoose(
								SystemConstants.getMainFrame(), true,ssList ,"请选择"+CZPService.getService().getDevName(dev)+"间隔需要拉开的刀闸");
						for (int i = 0; i < ssList.size(); i++) {//循环需要选择的刀闸集合
							PowerDevice tempDev=ssList.get(i);//tempDev 为选上的刀闸
							String defaultstate = tempDev.getPowerDeviceDefaultSta();//获取刀闸的常态
							if(defaultstate.equals("1")){//如果为1，则设置选上
								dcd.setChooseEquip(tempDev);
								break;
							}
						 }
						PowerDevice sw = dcd.getChooseEquip();
						
						if(sw!=null){
							RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(), "1");
							return true;
						}
						return false;
					}
				}
			}
		}
		
		return true;
	}

}
