package czprule.rule.operationmodel;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

/**
 * 关联设备状态和运行方式设置
 * <AUTHOR>
 *
 */
public class RelatedLineStatusSet implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		
		PowerDevice pd=rbm.getPd();

		PowerDevice curLine = null; //当前站内线路
		//得到线路开关所在站内线路
		List<PowerDevice> circuits = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, null, null, 
				CBSystemConstants.RunTypeSideMother, false, true, true, true, "0");
		if(circuits == null || circuits.size() ==0){
			return true;
		}else{
			curLine = circuits.get(0);
		}
		PowerDevice sideLine = null;  //对侧站内线路
		PowerDevice sideBreaker = null; //对侧线路开关
		PowerDevice sideKnife = null ; //对侧线路刀闸
		Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineByLine(curLine);
		for(PowerDevice pdNew:stationlines.keySet()){
			if(!pdNew.getPowerStationID().equals(curLine.getPowerStationID())){
				sideLine = CBSystemConstants.getPowerDevice(pdNew.getPowerStationID(), pdNew.getPowerDeviceID());
			}
		}

		List<PowerDevice> sideDevice = RuleExeUtil.getDeviceList(sideLine, SystemConstants.Switch, null, CBSystemConstants.RunTypeSwitchXL, 
				CBSystemConstants.RunTypeSideMother, false, true, true, true, "0");
		if(sideDevice != null && sideDevice.size() >0){
			sideBreaker = sideDevice.get(0);
		}
		if(sideBreaker == null ){
			List<PowerDevice> knifes = RuleExeUtil.getDeviceList(sideLine, SystemConstants.SwitchSeparate, null, CBSystemConstants.RunTypeKnifeXL, 
					CBSystemConstants.RunTypeSideMother, false, true, true, true, "0");
			if(knifes != null && knifes.size() >0){
				sideKnife = knifes.get(0);
			}
		}
		
		String curStatus="";
		if(pd.getDeviceType().equals(SystemConstants.Switch)){
			curStatus = pd.getDeviceStatus();
		}else if(pd.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			curStatus = pd.getDeviceStatus().equals("0")?"1":"3";  //刀闸对于的线路状态0是运行或热备用，1：冷备用或者检修 取最大值
		}
		String sideStatus="3";
		if(sideBreaker!=null){
			sideStatus = sideBreaker.getDeviceStatus();
		}else if(sideKnife!=null){
			sideStatus = sideKnife.getDeviceStatus().equals("0")?"1":"3";  //刀闸对于的线路状态0是运行或热备用，1：冷备用或者检修 取最大值
		}
		
		String lineStatus = Integer.valueOf(curStatus) >Integer.valueOf(sideStatus)?sideStatus:curStatus;
		
		//执行开关操作
		RuleExeUtil.deviceStatusSet(curLine, curLine.getDeviceStatus(), lineStatus);
		if(sideLine!=null){
		    RuleExeUtil.deviceStatusSet(sideLine, sideLine.getDeviceStatus(), lineStatus);
		}
		PowerDevice sysLine = RuleExeUtil.getLineSystem(curLine);
		RuleExeUtil.deviceStatusSet(sysLine, sysLine.getDeviceStatus(), lineStatus);
		return true;
	}

}
