/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：预研部操作票产品
 * 功能说明 : 选择执行设备连接的开关执行器(通路是闭合的)
 * 作    者 : 张余平
 * 开发日期 : 2012-03-16
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;
import czprule.wordcard.dao.DeviceStateMentManager;

public class SwitchLinkOnChooseExecute implements RulebaseInf {

	/**
	 * 选择执行满足输入条件的开关  输入条件（变电站类型，设备运行类型，初始状态，执行动作）
	 */
	public boolean execute(RuleBaseMode rbm) {
		
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		String devRunType = rbm.getDeviceruntype();
		
		//一、搜索设备连接的开关
		CommonSearch cs=new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		
		List<PowerDevice> switchs=new ArrayList<PowerDevice>();  //执行开关集合
		List<PowerDevice> tempswitchs=new ArrayList<PowerDevice>();  //开关集合
		PowerDevice tempDev=null;
		if(!"".equals(rbm.getTranType())){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
			 List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
			 if(sourceLineTrans==null||loadLineTrans==null){
				 ShowMessage.view("请先设置线路两端变电站属性！");
				 return false;
			 }
			 if("S".equals(rbm.getTranType())){
				 inPara.put("oprSrcDevice", sourceLineTrans);
	             inPara.put("tagDevType", SystemConstants.Switch);
	             inPara.put("isSearchOffPath",false);	//不搜索断开路径
	             inPara.put("excDevType", SystemConstants.PowerTransformer);
	             cs.execute(inPara, outPara);
	    	 	 inPara.clear();
	    	 	 tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
	    	  	 for (int i = 0; i < tempswitchs.size(); i++) {
	    	 		tempDev=(PowerDevice)tempswitchs.get(i);
	    	 		if(tempDev.getDeviceStatus().equals("2") || tempDev.getDeviceStatus().equals("3"))
	    	 			continue;
	    	 		if (devRunType.equals("")) {
						switchs.add(tempDev);
					} else {
						if (tempDev.getDeviceRunType().equals(devRunType))
							switchs.add(tempDev);
					}
				 }
			 }else{
				 for (int i = 0; i < loadLineTrans.size(); i++) {
					inPara.put("oprSrcDevice", loadLineTrans.get(i));
					inPara.put("tagDevType", SystemConstants.Switch);
					inPara.put("isSearchOffPath",false);	//不搜索断开路径
		            inPara.put("excDevType", SystemConstants.PowerTransformer);
		            cs.execute(inPara, outPara);
		    		inPara.clear();
		    		tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
		    		for (int j = 0; j < tempswitchs.size(); j++) {
		    			tempDev=(PowerDevice)tempswitchs.get(j);
		    			if(tempDev.getDeviceStatus().equals("2") || tempDev.getDeviceStatus().equals("3"))
		    	 			continue;
		    	 		if (devRunType.equals("")) {
							switchs.add(tempDev);
						} else {
							if (tempDev.getDeviceRunType().equals(devRunType))
								switchs.add(tempDev);
						}
					}
				}
			 }
		}else{
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.Switch);
			inPara.put("isSearchOffPath",false);	//不搜索断开路径
            inPara.put("excDevType", SystemConstants.PowerTransformer);
            cs.execute(inPara, outPara);
            tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
   	  	    for (int i = 0; i < tempswitchs.size(); i++) {
	   	 		tempDev=(PowerDevice)tempswitchs.get(i);
		   	 	if(tempDev.getDeviceStatus().equals("2") || tempDev.getDeviceStatus().equals("3"))
		 			continue;
	   	 		if (devRunType.equals("")) {
					switchs.add(tempDev);
				} else {
				    if (tempDev.getDeviceRunType().equals(devRunType))
					    switchs.add(tempDev);
				}
			}
   	  	    
   	  	   //双母接线母联开关默认为母线有效通路开关
   	  	   if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
	   	  	   List<PowerDevice> mlswList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer,
	  	  	    		CBSystemConstants.RunTypeSwitchML, "", false, true, true, true);
	  	  	    for(PowerDevice mlsw:mlswList){
	  	  	    	if(!switchs.contains(mlsw)){
	  	  	    		switchs.add(mlsw);
	  	  	    	}
	  	  	    }
   	  	    }
   	  	    
   	  	    
		}	
		if(switchs.size()==0)
			return true;
		
		if(rbm.getEndState().equals("3")){
			String endState="2";
			//如果设备转检修，开关线转冷备用，再选择开关转检修
			for (int i = 0; i < switchs.size(); i++) {
				tempDev = (PowerDevice) switchs.get(i);
				String beginStatus="";
				if(rbm.getBeginStatus().equals(""))
					beginStatus=tempDev.getDeviceStatus();
				else
					beginStatus=rbm.getBeginStatus();
				RuleExecute ruleExecute=new RuleExecute();
				RuleBaseMode rbmode=new RuleBaseMode();
				rbmode.setPd(tempDev);
				rbmode.setBeginStatus(beginStatus);
				rbmode.setEndState(endState);
				if(!ruleExecute.execute(rbmode)){
					return false;
				}
	        }
			String showMessage="请选择由[检修]转[冷备用]的开关";
			EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, switchs, showMessage);
			List<PowerDevice> chooseEquips=ecc.getChooseEquip();
			if(chooseEquips.size()==0)
				return true;
			for (int i = 0; i < chooseEquips.size(); i++) {
				tempDev = (PowerDevice) switchs.get(i);
				RuleExecute ruleExecute=new RuleExecute();
				RuleBaseMode rbmode=new RuleBaseMode();
				rbmode.setPd(tempDev);
				rbmode.setBeginStatus("2");
				rbmode.setEndState("3");
				if(!ruleExecute.execute(rbmode)){
					return false;
				}
	        }
			
			
		}else{
			for (int i = 0; i < switchs.size(); i++) {
				tempDev = (PowerDevice) switchs.get(i);
				String beginStatus="";
				if(rbm.getBeginStatus().equals(""))
					beginStatus=tempDev.getDeviceStatus();
				else
					beginStatus=rbm.getBeginStatus();
				RuleExecute ruleExecute=new RuleExecute();
				RuleBaseMode rbmode=new RuleBaseMode();
				DeviceStateMentManager dsmm = new DeviceStateMentManager();
				rbmode.setPd(tempDev);
				rbmode.setBeginStatus(beginStatus);
				rbmode.setEndState(rbm.getEndState());
				rbmode.setStateCode(dsmm.getStateCodeByStatus(tempDev.getDeviceType(), rbm.getEndState(), "9")); //默认取基本操作,操作类型代码是9
				if(!ruleExecute.execute(rbmode)){
					return false;
				}
	        }
		}
		return true;
	}

}
