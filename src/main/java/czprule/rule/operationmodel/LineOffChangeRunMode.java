package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;

/**
 * 线路停电调整运行方式
 * <AUTHOR>
 *
 */
public class LineOffChangeRunMode implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		
		PowerDevice pd=rbm.getPd();
		
		
		// 获取负荷侧线路
		List<PowerDevice> loadList = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
		if(loadList == null)
			return false;
		for (PowerDevice load : loadList) {
			if(load !=null && load.getDeviceStatus().equals("0")) {
				if (!loadLine(load)) {
					return false;
				}
			}
		}
		
		return true;
	}
	
	/**
	 * 线路倒负荷
	 * @param line
	 * @param rbm
	 * @return
	 */
	public boolean loadLine(PowerDevice line) {
		boolean result = true;
		if(!line.getDeviceStatus().equals("0"))
			return true;
		List<PowerDevice> tfList = RuleExeUtil.getDeviceList(line, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, true, true);
		if(tfList.size() > 0) { //如果线路直接连接主变，执行主变的主变状态转换
			for (PowerDevice tf : tfList) {
				result = loadTransformer(line, tf);
				if(!result)
					return false;
			}
		}
		else { //如果线路连接了母线，对母线倒负荷
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(line, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
			for (PowerDevice ml : mlList) {
				if(!RuleExeUtil.isDeviceOperate(ml)) {
					result = loadMotherLine(line, ml);
					if(!result)
						return false;
				}
			}
		}
		return true;
	}
	
	/**
	 * 主变倒负荷
	 * @param srcLine
	 * @param tf
	 * @return
	 */
	public boolean loadTransformer(PowerDevice srcLine, PowerDevice tf) {
		boolean result = true;
		PowerDevice lineSwitch = RuleExeUtil.getDeviceSwitch(srcLine);
		//查找主变其他可能的电源点
		List<PowerDevice> swList = RuleExeUtil.getDeviceList(tf, lineSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchQT, "", false, false, false, true, true);
		for(Iterator it = swList.iterator();it.hasNext();) {
			PowerDevice mlsw = (PowerDevice)it.next();
			List<PowerDevice> srcList = RuleExeUtil.getDeviceList(mlsw, lineSwitch, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "", false, false, false, true, true);
			if(srcList.size() == 0)
				it.remove();
		}
		
		PowerDevice dev = null; 
		if(swList.size() == 1)
			dev = swList.get(0);
		else if(swList.size() > 1) {
			EquipCheckChoose ecc = new EquipCheckChoose(SystemConstants.getMainFrame(), true, swList, "请选择["+CBSystemConstants.getPowerStation(srcLine.getPowerStationID()).getPowerStationName()+"]["+srcLine.getPowerDeviceName()+"]停电前合上的开关");
			if(ecc.getChooseEquip()!=null && ecc.getChooseEquip().size() > 0) {
				dev = ecc.getChooseEquip().get(0);
			}
		}
		if(dev != null)
			RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "0");
		else
			result = RuleExeUtil.deviceStatusChange(tf, tf.getDeviceStatus(), "2");
		
		return result;
	}
	
	/**
	 * 母线倒负荷
	 * @param srcLine
	 * @param ml
	 * @return
	 */
	public boolean loadMotherLine(PowerDevice srcLine, PowerDevice ml) {
		
		List<PowerDevice> lineList = null;
		
		//查找可以代供的线路
		PowerDevice xlSwitch = RuleExeUtil.getDeviceSwitch(srcLine);
		lineList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "", false, false, false, true);
		for(Iterator it = lineList.iterator();it.hasNext();) {
			PowerDevice ln = (PowerDevice)it.next();
			if(String.valueOf(RuleExeUtil.judgeLineFlow(ln)).equals("1"))
				return true; //母线上存在其他进线,不需要倒负荷
			else if(String.valueOf(RuleExeUtil.judgeLineFlow(ln)).equals("2"))
				it.remove();
		}
		
		if(lineList.size() > 0 && ml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
			return true;
		
		List<PowerDevice> swList = new ArrayList<PowerDevice>();
		
		//查找热备用的线路开关和母联开关
		List<PowerDevice> swxlList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
		for(PowerDevice sw : swxlList) {
			if(sw.getDeviceStatus().equals("1"))
				swList.add(sw);
			else if(sw.getDeviceStatus().equals("0")) {
				List<PowerDevice> lnlist = RuleExeUtil.getSwitchLoadLine(sw);
				if(lnlist.size() == 1) {
					List<PowerDevice> otherLinelist = RuleExeUtil.getLineOtherSideList(lnlist.get(0));
					for(PowerDevice otherLine : otherLinelist) {
						PowerDevice otherSw = RuleExeUtil.getDeviceSwitch(otherLine);
						if(otherSw != null && otherSw.getDeviceStatus().equals("1"))
							swList.add(otherSw);
					}
				}
			}
		}
		
		
		List<PowerDevice> swmlList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, false, true);
		for(PowerDevice sw : swmlList) {
			if(sw.getDeviceStatus().equals("1"))
				swList.add(sw);
		}
		
		PowerDevice dev = null;
		
		if(swList.size() == 1)
			dev = swList.get(0);
		else if(swList.size() > 1) {
			EquipCheckChoose ecc = new EquipCheckChoose(SystemConstants.getMainFrame(), true, swList, "请选择["+CBSystemConstants.getPowerStation(srcLine.getPowerStationID()).getPowerStationName()+"]["+srcLine.getPowerDeviceName()+"]停电前合上的开关");
			if(ecc.getChooseEquip()!=null && ecc.getChooseEquip().size() > 0) {
				dev = ecc.getChooseEquip().get(0);
			}
		}
		if(dev != null)
			RuleExeUtil.deviceStatusChange(dev, dev.getDeviceStatus(), "0");
		else {
			//如果是电源侧母线，执行母线状态转换
			if(RuleExeUtil.isSourceSide(ml))
				RuleExeUtil.deviceStatusChange(ml, ml.getDeviceStatus(), "2");
		}
		
		return true;
	}

}
