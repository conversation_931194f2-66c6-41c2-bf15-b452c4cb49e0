package czprule.rule.operationmodel;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：图形化智能操作票系统
 * 功能说明 : 母线倒回正常方式操作(用户选择)
 * 作    者 : 郑柯
 * 开发日期 : 2015-04-02
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

public class MotherLineLoadBackChoose implements RulebaseInf {
		
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();//获取到了执行操作的母线
		if(pd==null)
			return false;
		Map<String,Object> inMap=new HashMap<String,Object>();	//输入参数
		Map<String,Object> outMap=new HashMap<String,Object>();	//输出结果
		CommonSearch search=new CommonSearch();
		inMap.put("oprSrcDevice",pd);//搜索起点设备为该母线
		inMap.put("tagDevType", SystemConstants.Switch);//目标设备为开关
		inMap.put("excDevType", SystemConstants.PowerTransformer);//排除搜索设备为主变
		inMap.put("isSearchOffPath",true);	//不搜索断开路径
		search.execute(inMap, outMap);//search执行搜索
		inMap.clear();
		List<PowerDevice> devList=(List<PowerDevice>) outMap.get("linkedDeviceList");//获取到搜索到的设备
		//执行开关导母 如何所有的开关都导母失败则母线失败
		PowerDevice switchDev=null;
		boolean isloadSwitch = true;
		
		//排除冷备用及检修的设备
		for(Iterator it = devList.iterator();it.hasNext();) {
			PowerDevice dev = (PowerDevice)it.next();
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))
				it.remove();
			else if(!dev.getDeviceStatus().equals("0") && !dev.getDeviceStatus().equals("1"))
				it.remove();
		}
		if(CBSystemConstants.isCurrentSys) {
			RuleExeUtil.swapDeviceList(devList);
			String showMessage="请选择需要倒回["+CZPService.getService().getDevName(pd)+"]运行的设备";
			EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, devList, showMessage);
			List<PowerDevice> chooseEquips=ecc.getChooseEquip();
			
			
			for(PowerDevice obj:chooseEquips){//遍历选择要倒母的设备
				switchDev=obj;
				if(switchDev == null)
					continue;
				//如果开关接线方式不为双母接线方式，则跳出继续循环
				if(!switchDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
					continue;
				RuleBaseMode rbmode=new RuleBaseMode();
				rbmode.setPd(switchDev);//将此开关放入规则操作设备
				new SwitchChangeMotherLine().execute(rbmode);//执行此规则
			}
		}
		return true;
	}
	
}
