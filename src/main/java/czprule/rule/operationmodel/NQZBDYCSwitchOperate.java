/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 基础设备执行类
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.operationmodel;



import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;


public class NQZBDYCSwitchOperate implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
        
        if(pd.getDeviceType().equals(SystemConstants.PowerTransformer)
        		&&RuleUtil.isTransformerNQ(pd)){
        	List<PowerDevice> mxList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
        	if(mxList.size()>0){
        		List<PowerDevice> swList = RuleExeUtil.getDeviceList(mxList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
        		for(PowerDevice sw:swList){
        			RuleExeUtil.deviceStatusExecuteJB(sw, sw.getDeviceStatus(), rbm.getEndState());
        		}
        	}
        }
		
		return true;
	}

}
