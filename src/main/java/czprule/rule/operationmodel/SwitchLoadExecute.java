/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：预研部操作票产品
 * 功能说明 : 设备连接的开关或者开关本身执行器
 * 作    者 : 张余平
 * 开发日期 : 2011-08-4
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.operationmodel;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipStatusChoose;
import czprule.system.CBSystemConstants;

public class SwitchLoadExecute implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		
		if(rbm==null)
			return true;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return true;
		if(!pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){//只有线路开关做负荷处理
			return true;
		}
		if(!pd.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){//只有单母接线方式做负荷处理
			return true;
		}
		if(rbm.getBeginStatus().equals("0")){//倒负荷
			 //如果连接了母线，对母线倒负荷
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
			for (PowerDevice ml : mlList) {
				if(!RuleExeUtil.isDeviceOperate(ml)&& (!ml.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)) && (!ml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) ) {
					loadMotherLine(pd, ml);
				}
			}
		}else if(rbm.getEndState().equals("0")) { //负荷倒回
			List<PowerDevice> xlList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
			if(xlList.size()>0){
				LineExecuteLoad loadExe = new LineExecuteLoad();
				loadExe.executeLoadBack(xlList.get(0));
			}

		}
		
		return true;
	}
	/**
	 * 母线倒负荷
	 * @param srcLine
	 * @param ml
	 * @return
	 */
public static boolean loadMotherLine(PowerDevice xlSwitch, PowerDevice ml) {
		
		List<PowerDevice> lineList = null;
		
		//查找可以代供的线路
		lineList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.InOutLine, SystemConstants.PowerTransformer, "", "", false, false, false, true);
		for(Iterator it = lineList.iterator();it.hasNext();) {
			PowerDevice ln = (PowerDevice)it.next();
			if(String.valueOf(RuleExeUtil.judgeLineFlow(ln)).equals("1"))
				return true; //母线上存在其他进线,不需要倒负荷
			else if(String.valueOf(RuleExeUtil.judgeLineFlow(ln)).equals("2"))
				it.remove();
		}
		
		if(lineList.size() > 0 && ml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
			return true;
		
		List<PowerDevice> swList = new ArrayList<PowerDevice>();
		
		//查找热备用的线路开关和母联开关
		List<PowerDevice> swxlList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
		for(PowerDevice sw : swxlList) {
			if(sw.getDeviceStatus().equals("1"))
				swList.add(sw);
			else if(sw.getDeviceStatus().equals("0")) {
				List<PowerDevice> lnlist = RuleExeUtil.getSwitchLoadLine(sw);
				if(lnlist.size() == 1) {
					List<PowerDevice> otherLinelist = RuleExeUtil.getLineOtherSideList(lnlist.get(0));
					for(PowerDevice otherLine : otherLinelist) {
						PowerDevice otherSw = RuleExeUtil.getDeviceSwitch(otherLine);
						if(otherSw != null && otherSw.getDeviceStatus().equals("1"))
							swList.add(otherSw);
					}
				}
			}
		}
		
		
		List<PowerDevice> swmlList = RuleExeUtil.getDeviceList(ml, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML, "", false, false, false, true);
		for(int i=0;i<swmlList.size();i++){//母联开关排除母线ml，搜到的另一端有线路开关或者母联开关，才有可能是可转电的相关母联开关
			if(RuleExeUtil.getDeviceList(swmlList.get(i), ml, SystemConstants.Switch, SystemConstants.PowerTransformer,
					CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchXL, "", false, true, false, true).size()==0){
				swmlList.remove(i);
				i--;
			}
		}
		
		if(swmlList.size()==1&&!swmlList.get(0).getDeviceStatus().equals("0")){
			RuleExeUtil.deviceStatusChange(swmlList.get(0), swmlList.get(0).getDeviceStatus(),"0");
		}else if(swmlList.size()>0){
			PowerDevice otherMX = RuleUtil.getAnotherMotherLine(swmlList.get(0), ml);
			List<PowerDevice> otherswxlList = RuleExeUtil.getDeviceList(otherMX, xlSwitch, SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchXL, "", false, false, false, true);
			for(PowerDevice sw : otherswxlList) {
				if(!sw.getDeviceStatus().equals("0")){//线路开关非运行，合线路开关
					RuleExeUtil.deviceStatusChange(sw, sw.getDeviceStatus(),"0");
				}else if(!swmlList.get(0).getDeviceStatus().equals(("0"))){//线路开关运行，母联开关非运行合母联开关
					RuleExeUtil.deviceStatusChange(swmlList.get(0), swmlList.get(0).getDeviceStatus(),"0");
				}
		
			}
		}else if(swmlList.size()==0){//没有母联开关，则搜是否有其他线路可以转供
			List<PowerDevice> pdList=RuleExeUtil.getDeviceList(ml, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
			for(PowerDevice xlpd:pdList){
				List<PowerDevice> switchList =RuleExeUtil.getDeviceList(xlpd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				if(switchList.contains(xlSwitch)){
					continue;
				}
				List<String> defaultStatusList = new ArrayList<String>();
				List<String> expStatusList = new ArrayList<String>();
				for(PowerDevice sw : switchList) {
					defaultStatusList.add(sw.getDeviceStatus());
				}
				expStatusList.add("2");
				expStatusList.add("3");
				Map tagStatusMap = new HashMap();
				if(CBSystemConstants.isCurrentSys) {
					EquipStatusChoose dialog = new EquipStatusChoose(SystemConstants.getMainFrame(), true, switchList, defaultStatusList,expStatusList, "如需转电请选择线路开关要转换的状态：");
					tagStatusMap=dialog.getTagStatusMap();
					if(tagStatusMap.size()>0){
						List<Map.Entry<PowerDevice,String>> list = new ArrayList<Map.Entry<PowerDevice,String>>(tagStatusMap.entrySet());
				        for(Map.Entry<PowerDevice,String> entry:list){
				    		if(!entry.getKey().getDeviceStatus().equals(entry.getValue())){//改变了才做操作
								RuleExeUtil.deviceStatusExecuteJB(entry.getKey(), entry.getKey().getDeviceStatus(),entry.getValue());
							}
						}
					}
				}else if(switchList.size()>0&&("1").contains(switchList.get(0).getDeviceStatus())){
					RuleExeUtil.deviceStatusExecuteJB(switchList.get(0), switchList.get(0).getDeviceStatus(), "0");
					break;
				}
			} 
			
		}
		return true;
	}
}
