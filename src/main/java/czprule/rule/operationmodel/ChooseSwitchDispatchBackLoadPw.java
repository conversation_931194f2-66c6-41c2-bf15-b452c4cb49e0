package czprule.rule.operationmodel;

import com.tellhow.graphicframework.constants.SystemConstants;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoosePw;
import czprule.system.CBSystemConstants;
import java.util.ArrayList;
import java.util.List;

public class ChooseSwitchDispatchBackLoadPw
  implements RulebaseInf
{
  EquipCheckChoosePw ecc = null;

  public boolean execute(RuleBaseMode rbm) {
    if (CBSystemConstants.isCurrentSys)
    {
    	final  List loadMap = new ArrayList();

      if ((!CBSystemConstants.isSame.booleanValue()) && (CBSystemConstants.getSamepdlist().size() == 0)) {
        CBSystemConstants.isSame = Boolean.valueOf(true);
        new Thread(new Runnable()
        {
          public void run() {
            ChooseSwitchDispatchBackLoadPw.this.ecc = 
              new EquipCheckChoosePw(SystemConstants.getMainFrame(), 
              false, loadMap, "请选择目标设备", CBSystemConstants.getCurRBM());
          }
        }).start();
        return false;
      }

      for (int i = 0; i < CBSystemConstants.getSamepdlist().size(); i++) {
        PowerDevice pd = (PowerDevice)CBSystemConstants.getSamepdlist().get(i);
        if (pd.getDeviceStatus().equals("0")) {
          RuleExeUtil.deviceStatusReset(pd, pd.getDeviceStatus(), "1");
        }
      }

      if (rbm.getPd().getDeviceStatus().equals("1")) {
        RuleExeUtil.deviceStatusReset(rbm.getPd(), rbm.getPd().getDeviceStatus(), "0");
      }

      return true;
    }

    return true;
  }
}