package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

/**
 * 设备接地刀闸或接地线操作执行类
 * <AUTHOR>
 *
 */
public class GroundKnifeLineExecute implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return false;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return false;
		List<PowerDevice> knifes = new ArrayList<PowerDevice>(); // 接地刀闸集合
		List<PowerDevice> tempknifes = new ArrayList<PowerDevice>(); // 接地刀闸集合
		List<PowerDevice> pdList =  new ArrayList<PowerDevice>(); // 当前设备集合

		// 一、搜索设备连接的接地刀闸
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		PowerDevice tempDev = null;
		if (pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)) {
			knifes.add(pd);
		} else {
			if (!"".equals(rbm.getTranType())) {
				PowerDevice sourceLineTrans = CBSystemConstants.LineSource
						.get(pd.getPowerDeviceID());
				List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad
						.get(pd.getPowerDeviceID());
				if (sourceLineTrans == null || loadLineTrans == null) {
					ShowMessage.view("请先设置线路两端变电站属性！");
					return false;
				}
				if ("S".equals(rbm.getTranType())) {
					pdList.add(sourceLineTrans);
					inPara.put("oprSrcDevice", sourceLineTrans);
					inPara.put("tagDevType",
							SystemConstants.SwitchFlowGroundLine);
					inPara.put("isSearchDirectDevice", true);
					cs.execute(inPara, outPara);
					inPara.clear();
					knifes = (ArrayList) outPara.get("linkedDeviceList");
				} else {
					pdList.addAll(loadLineTrans);
					for (int i = 0; i < loadLineTrans.size(); i++) {
						inPara.put("oprSrcDevice", loadLineTrans.get(i));
						inPara.put("tagDevType",
								SystemConstants.SwitchFlowGroundLine);
						inPara.put("isSearchDirectDevice", true);
						cs.execute(inPara, outPara);
						inPara.clear();
						tempknifes = (ArrayList) outPara
								.get("linkedDeviceList");
						for (int j = 0; j < tempknifes.size(); j++) {
							knifes.add(tempknifes.get(j));
						}
					}
				}
			} else {
				pdList.add(pd);
				inPara.put("oprSrcDevice", pd);
				inPara.put("tagDevType", SystemConstants.SwitchFlowGroundLine);
//				inPara.put("tagDevRunType", CBSystemConstants.RunTypeGroundKnife);
				inPara.put("excDevRunType", CBSystemConstants.RunTypeGroundZXDDD);
				inPara.put("isSearchDirectDevice", true);
				cs.execute(inPara, outPara);
				inPara.clear();
				knifes = (ArrayList) outPara.get("linkedDeviceList");
			}
		}

		// 执行接地刀闸操作
		for (int i = 0; i < knifes.size(); i++) {
			tempDev = (PowerDevice) knifes.get(i);
			RuleExecute ruleExecute = new RuleExecute();
			RuleBaseMode rbmode = new RuleBaseMode();
			rbmode.setPd(tempDev);
			rbmode.setBeginStatus(rbm.getBeginStatus());
			rbmode.setEndState(rbm.getEndState());
			if (!ruleExecute.execute(rbmode)) {
				return false;
			}
		}

		for(PowerDevice eq : pdList) {
			// 母线例外 ？母线如何例外？
			// 获取设备各侧接地刀闸
			knifes = RuleUtil.getDirectDevice(cs, inPara, outPara, eq,
					SystemConstants.SwitchFlowGroundLine);
			// 获取设备各侧刀闸
			// List<PowerDevice> switchs = RuleUtil.getDirectDevice(cs,
			// inPara,outPara, pd, SystemConstants.SwitchSeparate);
			List<PowerDevice> switchs = new ArrayList<PowerDevice>();
			switchs.add(RuleUtil.getSideKnife0(cs, inPara, outPara, eq));
			switchs.add(RuleUtil.getSideKnife1(cs, inPara, outPara, eq));
			List<PowerDevice> result = new ArrayList<PowerDevice>(switchs);
			for (PowerDevice knife : switchs) {
				// 获取设备刀闸相连的接地刀闸
				if (knife == null) {
					result.remove(knife);
					continue;
				}
				tempknifes = RuleUtil.getDirectDevice(cs, inPara, outPara, knife,
						SystemConstants.SwitchFlowGroundLine);
				for (PowerDevice temp : knifes) {
					if (tempknifes.size() > 0 && tempknifes.contains(temp)) {
						// 若刀闸相连的接地刀闸包含设备相连的接地刀闸则不符合条件
						result.remove(knife);
					}
				}
			}
			// 没找到符合条件的设备侧刀闸
			if (result.size() == 0) {
				//return true;
				continue;
			}
			// 目标状态是否为“挂上” 注："挂上"与刀闸的合上相对应
			// 0-3 运行-检修 4 合上刀闸 5 拉开刀闸
			List<PowerDevice> target = new ArrayList<PowerDevice>();
			if (rbm.getEndState().equals("0")) {
				// 提示用户选择哪侧挂接地线
				/*EquipCheckChoose ecc = new EquipCheckChoose(
						SystemConstants.getMainFrame(), true, result,
						"选择要挂上接地线的刀闸侧");
				target = ecc.getChooseEquip();*/
				target=result;
			} else {
				// 得到已挂接地线的刀闸侧
				String stID;
				String pdID;
				String knID;
				for (PowerDevice dev : result) {
				   stID=eq.getPowerStationID();
				   pdID= eq.getPowerDeviceID();
				   knID=dev.getPowerDeviceID();
					// 从接地线缓存中查找接地线
				   PowerDevice temp=CBSystemConstants.getRMDeviceByDetailAndTypeFromAllRMCache(PowerDevice.GROUNDLINE, stID, knID, pdID);
					
					/*
					 * PowerDevice temp =
					 * CBSystemConstants.getGroundLineByDeviceAndKnife(pd
					 * .getPowerStationID(), pd.getPowerDeviceID(), dev
					 * .getPowerDeviceID());
					 */
					if (temp != null) {
						target.add(dev);
					}
				}
			}
			for (PowerDevice dev : target) {
				// 遍历要操作接地线的设备侧刀闸，调用接地线操作基础类
				if (!GroundLineOperate
						.execute(CBSystemConstants.getDeviceStateValue(rbm
								.getEndState()), CBSystemConstants
								.getDeviceStateValue(rbm.getBeginStatus()), eq, dev,PowerDevice.GROUNDLINE)) {
					continue;
				}
			}
			//4就是挂接地线
			if(rbm.getEndState().equals("0")){
				if(!eq.getDeviceStatus().equals("3"))
					RuleExeUtil.deviceStatusSet(eq, eq.getDeviceStatus(), "3");
			}else{
				if(!eq.getDeviceStatus().equals("2"))
					RuleExeUtil.deviceStatusSet(eq, eq.getDeviceStatus(), "2");
			}	
		}
		return true;
	}
}
