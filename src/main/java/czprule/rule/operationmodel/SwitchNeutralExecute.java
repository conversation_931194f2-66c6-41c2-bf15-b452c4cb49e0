package czprule.rule.operationmodel;

import java.util.List;

import javax.swing.JOptionPane;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;

public class SwitchNeutralExecute implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		
		//操作设备为主变
		if(pd.getDeviceType().equals(SystemConstants.PowerTransformer)){
			//非内桥接线方式
			if(RuleUtil.isTransformerNQ(pd)){
				return true;
			}
			//并列运行，主变中性点调整（运行转冷备用、冷备用转运行）
			if(RuleExeUtil.isTransformerBL(pd)){
				//运行转冷备用
				if(CBSystemConstants.getCurRBM().getBeginStatus().equals("0")){
					//合上其他主变中性点接地刀闸
					if(isNeedOnOtherZXD(pd)) {
						//并列运行的主变
						List<PowerDevice> tfList = RuleExeUtil.getTransformerBL(pd);
						//将不带线路负荷的主变移至最后
						for(int i = tfList.size()-1; i >=0; i--) {
							if(!RuleExeUtil.isTransformerHasLoad(tfList.get(i))) {
								tfList.add(tfList.get(i));
								tfList.remove(i);
							}
						}
						
						PowerDevice tf = null;
						if(tfList.size() == 1)
							tf = tfList.get(0);
						else if(tfList.size() > 1) {
							if(CBSystemConstants.jh_tai == 1) 
								tf = tfList.get(0);
							else {
								int r = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "请选择合上哪台主变的中性点地刀？", "选择", JOptionPane.DEFAULT_OPTION, JOptionPane.INFORMATION_MESSAGE, null, tfList.toArray(), tfList.toArray()[0]);
								if (r == -1)
									return false;
								tf = tfList.get(r);
							}
						}
						if(tf != null) {
							List<PowerDevice> gds = RuleExeUtil.getDeviceDirectList(tf, SystemConstants.SwitchFlowGroundLine);
							RuleExeUtil.swapLowDeviceList(gds);
							for (PowerDevice gd : gds) {
								if(gd.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD) && gd.getDeviceStatus().equals("1")) {
									RuleExeUtil.deviceStatusChange(gd, "1", "0");
								}
							}
						}
					}
				}
				//冷备用转运行
				else if(CBSystemConstants.getCurRBM().getEndState().equals("0")){
					//拉开其他主变中性点接地刀闸
					if(isNeedOnOtherZXD(pd)) {
						//并列运行的主变
						List<PowerDevice> tfList = RuleExeUtil.getTransformerBL(pd);
						for(PowerDevice tf : tfList){
							//并列运行的主变的直连的中性点接地刀闸
							List<PowerDevice> gds = RuleExeUtil.getDeviceDirectList(tf, SystemConstants.SwitchFlowGroundLine,CBSystemConstants.RunTypeGroundZXDDD);
							for (PowerDevice gd : gds) {
								if(gd.getDeviceStatus().equals("0") && gd.getPowerVoltGrade() == tf.getPowerVoltGrade()) {
									RuleExeUtil.deviceStatusChange(gd, gd.getDeviceStatus(), "1");
								}
							}
						}
					}
				}
			}
		}
		return true;
	}
	
	//判断：是否需要操作其他主变的中性点地刀
	private boolean isNeedOnOtherZXD(PowerDevice pd){
		boolean isNeedOnOtherZXD = false;
		List<PowerDevice> curgds = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchFlowGroundLine);
		for (PowerDevice gd : curgds) {
			if(gd.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD) && gd.getPowerVoltGrade()==pd.getPowerVoltGrade() && gd.getDeviceStatus().equals("0")) {
				isNeedOnOtherZXD = true;
				break;
			}
		}
		return isNeedOnOtherZXD;
	}
}
