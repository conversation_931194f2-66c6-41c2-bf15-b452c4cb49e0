package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2013-7-11 下午4:23:16 
 */
public class TransformStartEndExecute implements RulebaseInf {

	public static PowerDevice xlSourceSwitch;
	public static List<PowerDevice> mlSourceSwitchList = new ArrayList<PowerDevice>();
	@Override
	public boolean execute(RuleBaseMode rbm) {
		
		if(rbm.getBeginStatus().equals("0")) {
			xlSourceSwitch =null;
			
		}else{
			if(rbm.getPd()!=null&&!RuleUtil.isTransformerNQ(rbm.getPd())){
				if(xlSourceSwitch!=null&&xlSourceSwitch.getDeviceStatus().equals("1")){
					RuleExeUtil.deviceStatusExecute(xlSourceSwitch,"1","0");	
				}
				for(PowerDevice mlSourceSwitch:mlSourceSwitchList){
					if(mlSourceSwitch.getDeviceStatus().equals("0")){
						RuleExeUtil.deviceStatusExecute(mlSourceSwitch,"0","1");	
					}
				}
			}
			
			xlSourceSwitch = null;
			mlSourceSwitchList.clear();
		}
	
		
		return true;
	}
	
	

}
