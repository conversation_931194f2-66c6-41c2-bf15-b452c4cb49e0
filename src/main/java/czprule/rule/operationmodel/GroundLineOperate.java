/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票系统
 * 功能说明 : 接地线操作基础类
 * 作    者 : 章雪松
 * 开发日期 : 2013-07-30
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.operationmodel;

import java.util.ArrayList;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.CBSystemConstants;

/**
 * 接地线操作基础类
 * */
public class GroundLineOperate {
	public static boolean execute(String targetStatu, String srcStatu,
			PowerDevice pd, PowerDevice knife,int rmType) {
//		if(targetStatu.equals("1")){
//			srcStatu="0";
//		}
		// 直接在设备上挂接地线
		String knifeID;
		if (knife == null) {
			knifeID = "";
		} else
			knifeID = knife.getPowerDeviceID();
        String stationID=pd.getPowerStationID();
		// 从缓存中获取接地线
		PowerDevice groundLine = CBSystemConstants.getRMDeviceByDetailAndType(rmType, stationID, knifeID, pd.getPowerDeviceID());
		
		// 如果接地线信息不存在
		if (groundLine == null) {
			// 添加接地线
			groundLine = new PowerDevice();
			groundLine.setPowerDeviceID(StringUtils.getUUID());
			groundLine.setDevice(pd.getPowerDeviceID());
			groundLine.setKnife(knifeID);
			groundLine.setRmType(rmType);
			groundLine.setDeviceStatus(srcStatu);
            groundLine.setPowerStationID(stationID);
            groundLine.setDeviceType(SystemConstants.RemovableDevice);
            groundLine.setOrgaId(pd.getOrgaId());
            //PowerSystemDBOperator.addStationGroundLine(groundLine.getPowerDeviceID(),pd.getPowerDeviceID(), knifeID, srcStatu,groundLine.getRmType());
			
		}	
		// 将接地线设置为目标状态
		//groundLine.setDeviceStatus(targetStatu);
		//groundLine.setPowerDeviceName(targetStatu.equals("0") ? "挂上" : "拆掉");
		//将接地线加入操作缓存
		DispatchTransDevice dtd=new DispatchTransDevice();
        dtd.setTransDevice(groundLine);
        dtd.setParentDevice(CBSystemConstants.getParentDev());
        dtd.setBeginstatus(srcStatu);
        dtd.setEndstate(targetStatu);
        dtd.setFlag("1");
        CBSystemConstants.putDtdMap(dtd);
		// 调用接地线绘制类
//		if (!new DrawRemovableDevice().execute(pd, knife, targetStatu,groundLine.getRmType())) {
//			return true;
//		}
		
		// 将接地线加入活动缓存
	    /*HashMap<PowerDevice, String> activeRMS = CBSystemConstants.getActiveRMDevice().get(stationID);
		if(activeRMS==null){
			activeRMS=new HashMap<PowerDevice,String>();
		    CBSystemConstants.getActiveRMDevice().put(stationID, activeRMS);
		}
		
		if(activeRMS.get(groundLine)==null){
		    activeRMS.put(groundLine,srcStatu);
		}*/
		
		// 将接地线加入固定缓存
		ArrayList<PowerDevice> rms = CBSystemConstants.getRMDevice().get(stationID);
		if(rms==null){
			rms=new ArrayList<PowerDevice>();
			CBSystemConstants.getRMDevice().put(stationID,rms );
		}
		if(!rms.contains(groundLine)){
		     rms.add(groundLine);
		}
		groundLine.setDeviceStatus(targetStatu);
        return true;
	}
}
