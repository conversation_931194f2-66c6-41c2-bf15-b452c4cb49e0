package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.conditionmodel.judgeLineOperateOrder;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.LineTransChooseDialog;
import czprule.system.CBSystemConstants;
import czprule.system.DeviceSVGPanelUtil;

public class SwitchOtherSideExecute implements RulebaseInf {
	// 选择电源侧，负荷侧
	PowerDevice checkDev = null;
	PowerDevice sourceDev = null;

	public boolean execute(RuleBaseMode rbm) {
		if (rbm == null)
			return true;
		PowerDevice pd = rbm.getPd();
		if (pd == null)
			return true;
		if (!CBSystemConstants.getSourceDev().equals(pd))
			return true;

		// 线路开关
		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {

			// 查找线路
			PowerDevice circuit = null;
			List<PowerDevice> circuits = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine, null, null, CBSystemConstants.RunTypeSideMother, false, true, true, true, "0");
			if (circuits.size() == 0) {
				return true;
			} else {
				circuit = circuits.get(0);
			}
			// 线路开关判断电源侧,3/2方式不弹窗；
			if (pd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
				return true;

			// 对侧厂站线路
			PowerDevice otheresidecircuit = null;
			List<PowerDevice> othersidecircuits = RuleExeUtil.getLineOtherSideList(circuit);
			
			if(rbm.getBeginStatus().equals("0")){
				for(int i=0;i<othersidecircuits.size();i++){
					PowerDevice othersidesw = RuleExeUtil.getDeviceSwitch(othersidecircuits.get(i));
					if(othersidesw!=null&&!othersidesw.getDeviceStatus().equals("0")){
						othersidecircuits.remove(i);
						i--;
					}
				}
			}
			
			RuleExeUtil.sortByLineName(othersidecircuits);
			if (othersidecircuits.size() == 0) {
				return true;
			} else {
				otheresidecircuit = othersidecircuits.get(0);
			}
			if(otheresidecircuit != null) {
				PowerDevice otherxlswitch = RuleExeUtil.getDeviceSwitch(otheresidecircuit);
				boolean isNeed = true; //是否提示操作对侧开关
				if(!CBSystemConstants.isCurrentSys)
					isNeed = false;
				else if(otherxlswitch!=null&&otherxlswitch.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)
						&&pd.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine))
					isNeed = false;
				else if(rbm.getBeginStatus().equals("0") && !otheresidecircuit.getDeviceStatus().equals("0"))
					isNeed = false;
				else if(rbm.getEndState().equals("0") && otheresidecircuit.getDeviceStatus().equals("0"))
					isNeed = false;
				if(isNeed) {
					PowerDevice st = CBSystemConstants.getMapPowerStation().get(otheresidecircuit.getPowerStationID());
//					int isok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(),"是否操作对侧【"+CZPService.getService().getDevName(st)+"】开关？",
					int isok = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(),"是否操作对侧开关？",
					CBSystemConstants.SYSTEM_TITLE,
							JOptionPane.YES_NO_OPTION);
					if (isok != JOptionPane.OK_OPTION) {
						return true;
					}
				}
				else 
					return true;
			}
			
			RuleBaseMode rbmLine = new RuleBaseMode();
			rbmLine.setPd(circuit);
			rbmLine.setBeginStatus(rbm.getBeginStatus());
			rbmLine.setEndState(rbm.getEndState());
			boolean result = (new judgeLineOperateOrder()).execute(rbmLine);
			if(!result)
				return false;
			
//			if (judgePowerSide(circuit)) {
//			} else if (checkDev != null) {
//				return true;
//			} else
//				return false;
			String moc = circuit.getDeviceRunModel();
			// 单双电源线路判断
			/*
			 * if(moc.equals(CBSystemConstants.RunModelOneLine)){ return true; }
			 */
			
			otheresidecircuit =CBSystemConstants.LineSource.get(otheresidecircuit.getPowerDeviceID());
			
			
						// 对侧厂站开关
			PowerDevice othersideswith = null;
			List<PowerDevice> othersideswiths = RuleExeUtil.getDeviceList(otheresidecircuit, SystemConstants.Switch, null, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true, "0");
			if (othersideswiths.size() == 0) {
				return true;
			} else {
				othersideswith = othersideswiths.get(0);
			}
			// 对侧开关拉开操作

			if (rbm.getBeginStatus().equals("0") && rbm.getEndState().equals("1")) {
				List<PowerDevice> mlList = RuleExeUtil.getDeviceList(othersideswith, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
				for (PowerDevice ml : mlList) {
					if(!RuleExeUtil.isDeviceOperate(ml)&& (!ml.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo)) && (!ml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)) ) {
						SwitchLoadExecute.loadMotherLine(pd, ml);
					}
				}
				if(othersideswith.getDeviceStatus().equals("0"))
					RuleExeUtil.deviceStatusSet(othersideswith, othersideswith.getDeviceStatus(), "1");
			} else if (rbm.getBeginStatus().equals("1") && rbm.getEndState().equals("0")) {
				RuleExeUtil.deviceStatusSet(othersideswith, othersideswith.getDeviceStatus(), "0");
				List<PowerDevice> xlList = RuleExeUtil.getDeviceList(othersideswith, SystemConstants.InOutLine, SystemConstants.PowerTransformer, true, true, true);
				if(xlList.size()>0&&othersideswith.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
					LineExecuteLoad loadExe = new LineExecuteLoad();
					loadExe.executeLoadBack(xlList.get(0));
				}
			}

		}
		// 旁路开关对侧开关处理
		else if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)) {

			// 查找旁路母线
			PowerDevice plLine;

			List<PowerDevice> plLineList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, null, CBSystemConstants.RunTypeSideMother, null, false, true, true, true, "0");
			if (plLineList.size() > 0)
				plLine = plLineList.get(0);
			else
				return true;

			// 查找被代输电线

			List<PowerDevice> inOutLine = RuleExeUtil.getDeviceList(plLine, SystemConstants.InOutLine, null, false, true, true);

			if (inOutLine.size() == 0)
				return true;
			// 线路开关判断电源侧
			
			RuleBaseMode rbmLine = new RuleBaseMode();
			rbmLine.setPd(inOutLine.get(0));
			(new judgeLineOperateOrder()).execute(rbmLine);
			
//			if (judgePowerSide(inOutLine.get(0))) {
//			} 
//			else if (checkDev != null) {
//				return true;
//			} else
//				return false;
			for (PowerDevice dev : inOutLine) {

				String moc = dev.getDeviceRunModel();

				// 单双电源线路判断? 意义？？
				// if(moc.equals(CBSystemConstants.RunModelCableLine)){
				// return true;
				// }
				// 对侧厂站线路
				PowerDevice otheresidecircuit = null;
				List<PowerDevice> othersidecircuits = RuleExeUtil.getLineOtherSideList(dev);
				if (othersidecircuits.size() == 0) {
					return true;
				} else {
					otheresidecircuit = othersidecircuits.get(0);
				}
				// 对侧厂站开关
				PowerDevice othersideswith = null;
				List<PowerDevice> othersideswiths = RuleExeUtil.getDeviceList(otheresidecircuit, SystemConstants.Switch, null, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true, "0");
				if (othersideswiths.size() == 0) {
					return true;
				} else {
					othersideswith = othersideswiths.get(0);
				}
				// 旁路刀闸拉
				PowerDevice sideknife = null;
				List<PowerDevice> sideknifes = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, null, CBSystemConstants.RunTypeKnifePL, null, false, false, true, true);
				if (sideknifes.size() > 0)
					sideknife = sideknifes.get(0);

				// 对侧开关拉开操作 本侧刀闸操作

				if (rbm.getBeginStatus().equals("0") && rbm.getEndState().equals("1")) {

					RuleExeUtil.deviceStatusSet(othersideswith, othersideswith.getDeviceStatus(), "1");
					RuleExeUtil.deviceStatusSet(sideknife, sideknife.getDeviceStatus(), "1");
				} else if (rbm.getBeginStatus().equals("1") && rbm.getEndState().equals("0")) {

					RuleExeUtil.deviceStatusSet(othersideswith, othersideswith.getDeviceStatus(), "0");
				}

			}

			return true;
		}

		return true;
	}

	// 电源侧 负荷侧选择
	private boolean judgePowerSide(PowerDevice dl) {
		if (!dl.getDeviceType().equals(SystemConstants.InOutLine))
			return false;

		Map<PowerDevice, String> stationlines = QueryDeviceDao.getPowersLineByLine(dl);

		// 打开线路关联的变电站接线图
		String filePath = SystemConstants.getGuiBuilder().getActivateSVGPanel().getFilePath();
		PowerDevice dev = null; // 变电站对象
		List<PowerDevice> trans = new ArrayList<PowerDevice>();
		for (Iterator iterator = stationlines.keySet().iterator(); iterator.hasNext();) {
			dev = (PowerDevice) iterator.next();
			trans.add(dev);
			DeviceSVGPanelUtil.openSVGPanel(dev.getPowerStationID(), dev.getPowerDeviceID());
		}

		if (!"".equals(dl.getPowerStationName())) // 如果是在站内操作线路，打开对侧变电站后还要回到当前变电站，如果是全网图线路则不管
			SystemConstants.getGuiBuilder().activateTabbedPageByName(filePath);

		// 选择线路两端变电站电源侧负荷侧

		/*
		 * for (Iterator iterator = stationlines.keySet().iterator();
		 * iterator.hasNext();) { dev=(PowerDevice)iterator.next(); //String
		 * flow = RuleExeUtil.getLineFlow(dev); //String flow =
		 * RuleExeUtil.judgeLineFlow(dev); String flow =
		 * EMSService.getService().getLineFlow(dev.getPowerStationID(),
		 * dev.getPowerDeviceID()); if(flow.equals("2")){ sourceDev = dev;
		 * break; } }
		 * 
		 * if(sourceDev== null&&checkDev==null) { for (Iterator iterator =
		 * stationlines.keySet().iterator(); iterator.hasNext();) {
		 * dev=(PowerDevice)iterator.next(); if(dev.equals(dl))
		 * stationlines.put(dev, "0"); else stationlines.put(dev, "1"); } }
		 */
		if (checkDev == null) {
			LineTransChooseDialog linetransChoose = new LineTransChooseDialog(SystemConstants.getMainFrame(), true, stationlines, "选择【" + CZPService.getService().getDevName(dl) + "】的解合环顺序");
			checkDev = linetransChoose.getReslut();
		}
		if (dl.equals(checkDev))
			return true;
		return false;

	}
}
