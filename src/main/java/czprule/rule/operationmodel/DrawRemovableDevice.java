package czprule.rule.operationmodel;

import java.awt.geom.Point2D;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.w3c.dom.Element;

import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.czp.svg.document.DefaultSVGDocumentResolver;
import com.tellhow.graphicframework.action.impl.AddGroundLineByLineAction;
import com.tellhow.graphicframework.action.impl.AttachFlagSvgAction;
import com.tellhow.graphicframework.algorithm.ElementSearch;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.DOMUtil;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;

public class DrawRemovableDevice {

	private static SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
	
	/**
	 * 挂或拆接地线
	 * 
	 * @param pd
	 *            设备对象
	 * @param knife
	 *            关联刀闸对象
	 * @param targetStatu
	 *            目标状态，决定是挂还是拆
	 * */
	public boolean execute(PowerDevice pd, PowerDevice knife,
			String targetStatu, int rmType) {
		
		// test
		/*
		 * CBSystemConstants.getGroundLine(pd.getPowerStationID(),
		 * "90a3376a-ecf6-4559-8e15-770b0493c13f"
		 * ,"8b01cd50-7e58-4485-bc42-71c58ef8ef37");
		 * CBSystemConstants.getGroundLine
		 * (pd.getPowerStationID(),"a6721a9d-ef41-42e2-a666-06fcc22c6073"
		 * ,"ccbfcff0-0be4-472a-8db5-cad3c46c8ae9");
		 * CBSystemConstants.getGroundLine
		 * (pd.getPorwerStationID(),"a6721a9d-ef41-42e2-a666-06fcc22c6073"
		 * ,"d3c39758-3d5c-4900-90c9-0032fcc4980b");
		 * CBSystemConstants.getGroundLine
		 * (pd.getPowerStationID(),"90a3376a-ecf6-4559-8e15-770b0493c13f"
		 * ,"a8fdee72-d03c-46fb-ace2-9b44dc37ca53");
		 */
		if(pd == null)
			return false;
		String knifeID="";
		if(knife==null){
			knifeID ="";
		}else {
			knifeID=knife.getPowerDeviceID();
		}
		int rmstatu=-1;
		if(pd!=null){
			rmstatu = TicketDBManager.getRMStatus(pd.getPowerDeviceID(),knifeID,rmType);
		}
		if(rmstatu==-1&&TempTicket.isRoll&&targetStatu.equals("0")){
			return true;
		}
        //String flag="1";
		Element line = null;
		if (rmType == PowerDevice.GROUNDLINE) {
			Element pdElement = resolver.getDeviceGroupElement(pd);
			//Element graph=resolver.getDeviceGraphElement(pdElement);
			//GroundPoint g = DOMUtil.getGroundPoint(graph);
			//Double p = g.getPoint();
			//Point2D.Double p = DOMUtil.getElementStartPoint(graph);
			
			
			// DOMUtil.ElementToXMl(knElement);

			if (knife == null) {
				line = pdElement;
			} else {
				Element knElement = resolver.getDeviceGroupElement(knife);
				ElementSearch es = new ElementSearch();
				Map inMap = new HashMap();
				Map outMap = new HashMap();
				if(pdElement!=null)
				line = getLinkBetweenElement(es, inMap, outMap, pdElement,
						knElement);
			}
			if(line==null&&pd.getDeviceType().equals(SystemConstants.InOutLine)){
				line=pdElement;
			}
			if (line == null) {
				return false;
			}
			String d = resolver.getDeviceGraphElement(line).getAttribute("d");
			if(d.equals("")){
				d=resolver.getDeviceGraphElement(line).getAttribute("points");
			}
			Point p = getGroundPoint(d);

			boolean isAttached;
			if (targetStatu.equals("0")) {
				isAttached = true;
			} else {
				isAttached = false;
			}

			return new AddGroundLineByLineAction(pdElement,p.getFlag(), isAttached,
					p.getX(), p.getY(), pd.getPowerDeviceID(),
					knifeID).execute();
		} else if (rmType == PowerDevice.ACCESSCARD) {
			return signOnOrOff(pd,"check", targetStatu, "check.png",0,0);
		} else if (rmType == PowerDevice.SOMEBODYIN) {
			return signOnOrOff(pd,"stop", targetStatu, "stop.png",0,0);
		}else if(rmType==PowerDevice.CHUANDONGPAI){
			return signOnOrOff(pd,"pass", targetStatu, "pass.png",-10,-10);
		}else if(rmType==PowerDevice.GZPCDP){
			return signOnOrOff(pd,"pass", targetStatu, "pass.png",-10,-10);
		}else if(rmType==PowerDevice.ADDFHCL){
			DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
			if(targetStatu.equals("0"))
				svgResolver.addChildCircle(pd,"单带");//向SVG图中添加单带标签
			else if(targetStatu.equals("1"))
				svgResolver.removeChildNode(pd,"单带");//从SVG图中剔除单带标签
		}
		else if(rmType==PowerDevice.ADDCHZ){
			DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
			if(targetStatu.equals("0"))
				svgResolver.addChildCircle(pd,"重合闸运行");//向SVG图中添加单带标签
			else if(targetStatu.equals("1"))
				svgResolver.removeChildNode(pd,"重合闸运行");//从SVG图中剔除单带标签
		}
		return false;
	}

	public static boolean signOnOrOff(PowerDevice pd,String flagName,String targetStatu,String fileName,double tfx,double tfy){
		Element pdElement = resolver.getDeviceGroupElement(pd);
		Point2D.Double p = DOMUtil.getElementStartPoint(resolver
				.getDeviceGraphElement(pdElement));
		boolean isAttached;
		if (targetStatu.equals("0")) {
			isAttached = true;
		} else {
			isAttached = false;
		}
		return new AttachFlagSvgAction(pdElement, flagName, isAttached,
				p.getX()+tfx, p.getY()+tfy, fileName).execute();
	}
	
	/**
	 * 按搜索类型查找设备关联的对象
	 * 
	 * @param searchType搜索类型
	 * */
	public List<Element> searchElement(ElementSearch es, Map inMap, Map outMap,
			Element op, String searchType) {
		inMap.clear();
		inMap.put("oprSrcElement", op);
		inMap.put("searchType", searchType);
		es.execute(inMap, outMap);
		return (ArrayList<Element>) outMap.get("ElementList");
	}

	/**
	 * 查找两直接相连设备之间的线路
	 * 
	 * @param element1
	 *            开关
	 * @param element2
	 *            刀闸
	 * */
	public Element getLinkBetweenElement(ElementSearch es, Map inMap,
			Map outMap, Element element1, Element element2) {
		List<Element> pdList = searchElement(es, inMap, outMap, element1,
				"LinkElement");
		List<Element> eqList;
		for (int k = 0; k < pdList.size(); k++) {
			Element element = pdList.get(k);
			eqList = searchElement(es, inMap, outMap, element,
					"LinkEquipElement");
			if (eqList.contains(element2)) {
				return element;
			}
		}
		return null;
	}

	/**
	 * 寻找挂接地线的点坐标 这里只处理了简单的直线类型，折线暂时没有考虑
	 * */
	public static Point getGroundPoint(String d) {
		if(d.equals("")){
			return new Point(0, 0, "0");
		}
		d=d.replace(",", " ");
		String[] points = d.split("[ML ]");
//		String[] point;
		List<String> point =new ArrayList<String>();
		for(int i=0;i<points.length;i++){
			if(!points[i].equals("")){
				point.add(points[i]);
			}
		}
		float[] p = new float[4];
		for (int i = 0; i < 2; i++) {
//			point = points[i].trim().split("[, ]");
			
			p[2 * i ] = Float.parseFloat(point.get(2 * i ));
			p[2 * i +1] = Float.parseFloat(point.get(2 * i +1));
		}
		String flag = "-1";
		if (p[2] == p[0]) {
			flag = "0";
		}
		if (p[1] == p[3]) {
			flag = "1";
		}
		return new Point(p[0] + (p[2] - p[0]) / 2, p[1] + (p[3] - p[1]) / 2,
				flag);

	}

	public boolean  execute(String stationid, String device, String knife,
			String targetStatu, int rmType) {
		PowerDevice pd = CBSystemConstants.getPowerDevice(stationid, device);
		PowerDevice kn = CBSystemConstants.getPowerDevice(stationid, knife);
		return execute(pd, kn, targetStatu, rmType);
	}


	public boolean execute(PowerDevice pd) {
		if(pd == null)
			return false;
		//挂牌设备
		PowerDevice gppd = CBSystemConstants.getPowerDevice(pd.getPowerStationID(), pd.getDevice());
		//牌状态
		String targetStatu=pd.getDeviceStatus();
		//牌种类
		int rmType=pd.getRmType();
		//挂牌操作
		if (rmType == PowerDevice.ACCESSCARD) {
			DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
			if(targetStatu.equals("0"))
				svgResolver.addChildCircle(gppd,"检修");//向SVG图中添加检修标签
			else if(targetStatu.equals("1"))
				svgResolver.removeChildNode(gppd,"检修");//从SVG图中剔除检修标签
		} else if (rmType == PowerDevice.SOMEBODYIN) {
			DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
			if(targetStatu.equals("0"))
				svgResolver.addChildCircle(gppd,"施工");//向SVG图中添加施工标签
			else if(targetStatu.equals("1"))
				svgResolver.removeChildNode(gppd,"施工");//从SVG图中剔除施工标签
		}else if(rmType==PowerDevice.CHUANDONGPAI){
			DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
			if(targetStatu.equals("0"))
				svgResolver.addChildCircle(gppd,"传");//向SVG图中添加传动牌标签
			else if(targetStatu.equals("1"))
				svgResolver.removeChildNode(gppd,"传");//从SVG图中剔除传动牌标签
		}else if(rmType==PowerDevice.GZPCDP){
			DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
			if(targetStatu.equals("0"))
				svgResolver.addChildCircle(gppd,"光");//向SVG图中添加光字牌挂传动牌标签
			else if(targetStatu.equals("1"))
				svgResolver.removeChildNode(gppd,"光");//从SVG图中剔除光字牌挂传动牌标签
		}else if(rmType==PowerDevice.ADDFHCL){
			DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
			if(targetStatu.equals("0"))
				svgResolver.addChildCircle(gppd,"单带");//向SVG图中添加单带标签
			else if(targetStatu.equals("1"))
				svgResolver.removeChildNode(gppd,"单带");//从SVG图中剔除单带标签
		}else if(rmType==PowerDevice.ADDCHZ){
			DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
			if(targetStatu.equals("0"))
				svgResolver.addChildCircle(gppd,"重合闸运行");//向SVG图中添加单带标签
			else if(targetStatu.equals("1"))
				svgResolver.removeChildNode(gppd,"重合闸运行");//从SVG图中剔除单带标签
		}else if(rmType==PowerDevice.AVC){
			DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
			if(targetStatu.equals("0"))
				svgResolver.addChildCircle(gppd,"AVC");//向SVG图中添加AVC标签
			else if(targetStatu.equals("1"))
				svgResolver.removeChildNode(gppd,"AVC");//从SVG图中剔除AVC标签
		}else if(rmType==PowerDevice.VQC){
			DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
			if(targetStatu.equals("0"))
				svgResolver.addChildCircle(gppd,"VQC");//向SVG图中添加VQC标签
			else if(targetStatu.equals("1"))
				svgResolver.removeChildNode(gppd,"VQC");//从SVG图中剔除VQC标签
		}else if(rmType==PowerDevice.JDBYB){
			DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
			if(targetStatu.equals("0"))
				svgResolver.addChildCircle(gppd,"压板");//向SVG图中添加压板标签
			else if(targetStatu.equals("1"))
				svgResolver.removeChildNode(gppd,"压板");//从SVG图中剔除压板标签
		}
		return false;
	}

}

class Point {

	private float x;
	private float y;
	private String flag;

	Point(float x, float y, String flag) {
		this.x = x;
		this.y = y;
		this.flag = flag;
	}

	public float getX() {
		return x;
	}

	public void setX(float x) {
		this.x = x;
	}

	public float getY() {
		return y;
	}

	public void setY(float y) {
		this.y = y;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

}
