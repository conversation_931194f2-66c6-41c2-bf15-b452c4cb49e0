/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 设备操作规则执行器（相邻状态分步执行）
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.dao.RuleManagerDao;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;
import czprule.wordcard.dao.DeviceStateMentManager;

public class RuleExecuteStep extends RuleExecute {

	
	public boolean execute(RuleBaseMode Srcrbm) {
		
		boolean result = true;
		String opr = Srcrbm.getEndState();
		DeviceStateMentManager dsmm = new DeviceStateMentManager();
		if(Srcrbm.getStateCode().equals(""))
			Srcrbm.setStateCode(dsmm.getStateCodeByStatus(Srcrbm.getPd().getDeviceType(), Srcrbm.getEndState()));
		if((opr.equals("0")||opr.equals("1")||opr.equals("2")||opr.equals("3"))) {
			int begin = Integer.valueOf(Srcrbm.getBeginStatus());
			int end = Integer.valueOf(Srcrbm.getEndState()); 
			if(begin < end) {
				while(begin < end) {
					RuleBaseMode rbm = new RuleBaseMode();
					rbm.setPd(Srcrbm.getPd());
					rbm.setBeginStatus(String.valueOf(begin));
					rbm.setEndState(String.valueOf(begin+1));
					
					rbm.setStateCode(dsmm.getStateCodeByStatus(Srcrbm.getPd().getDeviceType(), String.valueOf(begin+1)));
					rbm.setMessageList(Srcrbm.getMessageList());
					
					result = executeStep(rbm, Srcrbm);
					if(!result)
						return false;
					begin++;
				}
			}
			else if(begin > end) {
				while(begin > end) {
					RuleBaseMode rbm = new RuleBaseMode();
					rbm.setPd(Srcrbm.getPd());
					rbm.setBeginStatus(String.valueOf(begin));
					rbm.setEndState(String.valueOf(begin-1));
					rbm.setStateCode(dsmm.getStateCodeByStatus(Srcrbm.getPd().getDeviceType(), String.valueOf(begin-1)));
					
					rbm.setMessageList(Srcrbm.getMessageList());
					
					result = executeStep(rbm, Srcrbm);
					if(!result)
						return false;
					begin--;
				}
			}
			return true;
		}
		else {
			return executeStep(Srcrbm, Srcrbm);
		}
		
		//return executeStep(Srcrbm, Srcrbm);
	}
	
	
	public boolean executeStep(RuleBaseMode Steprbm, RuleBaseMode Srcrbm) {
		if(Steprbm==null)
			return false;
		PowerDevice pd=Steprbm.getPd();
		if(pd==null)
			return false;
		if(pd.getDeviceStatus().equals(Steprbm.getEndState())){
			return true;
		}
		
		//保存当前操作设备
		CBSystemConstants.putCurOperateDev(Steprbm.getPd());
		
		String runmode=pd.getDeviceRunModel().trim();  //runmode 设备接线方式，空值默认单母接线方式
		if("".equals(runmode)){
		    runmode=CBSystemConstants.RunModelOneMotherLine;  
		}
		String devType=pd.getDeviceType();
        RuleManagerDao rmd=new RuleManagerDao();
        RuleBaseMode rbm=null;
        RulebaseInf rb=null; 
        
        if(CBSystemConstants.isLock)
        {
        	boolean result = true;
           //执行设备操作场景条件
	       List<RuleBaseMode> scenelist=rmd.getRuleCBClass(runmode, devType, Steprbm.getBeginStatus(), Steprbm.getStateCode(), "0");
		   for (int i = 0; i < scenelist.size(); i++) {
			    rbm=scenelist.get(i);
			    rbm.setPd(pd); 
			    try {
				    rb=  (RulebaseInf)Class.forName(rbm.getRuleBeanClass()).newInstance();
				    System.out.println("执行场景类：   "+rbm.getRuleValue()+rbm.getRuleBeanClass());
				    Steprbm.getMessageList().clear();
				    result = rb.execute(Srcrbm);
				    if(isShowMessage) {
						if(Srcrbm.getMessageList().size() > 0) {
							String message = "";
							for(String str : Srcrbm.getMessageList()) {
								message = message + str + "\r\n";
							}
							ShowMessage.viewWarning(SystemConstants.getMainFrame(), message);
							if(CBSystemConstants.isCurrentSys)
							Srcrbm.getMessageList().clear();
						}
						if(Srcrbm.getInfoList().size() > 0) {
							String message = "";
							for(String str : Srcrbm.getInfoList()) {
								message = message + str + "\r\n";
							}
							ShowMessage.view(SystemConstants.getMainFrame(), message);
							if(CBSystemConstants.isCurrentSys)
							Srcrbm.getInfoList().clear();
							
						}
//						if(!result && Srcrbm.getMessageList().size() == 0 && Srcrbm.getInfoList().size() == 0) {
//							ShowMessage.viewWarning(SystemConstants.getMainFrame(), "操作失败！");
//						}
				    }
				    else {
				    	if(CBSystemConstants.isCurrentSys){
					    	if(Srcrbm.getMessageList().size() > 0) {
					    		//Srcrbm.getMessageList().clear();
					    		return false;
					    	}
					    	if(Srcrbm.getInfoList().size() > 0) {
					    		//Srcrbm.getInfoList().clear();
					    		//20131205
					    		if(CBSystemConstants.isSame)
					    			result = false;
					    		else
					    			result = true;
					    		continue;
					    	}
				    	}
				    }
					if(!result)
						return false;
				} catch (ClassNotFoundException e) {
					ShowMessage.view("不存在逻辑类："+rbm.getRuleBeanClass());
					return false;
				} catch (Exception e) {
					e.printStackTrace();
					ShowMessage.view("逻辑类内部报错："+rbm.getRuleBeanClass());
					return false;
				}
        	}
		   if(!result)
			   return false;
        }
       //执行设备操作动作
        	 List<RuleBaseMode> operatelist=rmd.getRuleCBClass(runmode, devType, Steprbm.getBeginStatus(), Steprbm.getStateCode(), "1");
      	   /////
             if(operatelist.size()==0){
          	   //String srcStatusName = CBSystemConstants.getDeviceStatusName(devType, Steprbm.getBeginStatus());
          	   //String tagStatusName = CBSystemConstants.getDeviceOperateName(devType, Steprbm.getStateCode());
          	   //String runmodelName = CBSystemConstants.getDictionaryName(runmode, "runmodel");
          	   //ShowMessage.view("请配置"+runmodelName+"设备类型["+SystemConstants.getMapEquipType().get(devType)+"]"+srcStatusName+"->"+tagStatusName+"执行规则！");
      		   CBSystemConstants.getCurOperateDevs().remove(CBSystemConstants.getCurOperateDevs().size());
      		   return true;
      	   }
             for (int i = 0; i < operatelist.size(); i++) {
      		    rbm=operatelist.get(i);
      		    rbm.setPd(pd); 
      		    try {
      				rb = (RulebaseInf) Class.forName(rbm.getRuleBeanClass()).newInstance();
      			    System.out.println("执行动作类：   "+rbm.getRuleValue()+rbm.getRuleBeanClass());
      				if(!rb.execute(rbm))
      					return false;
      			} catch (ClassNotFoundException e) {
      				ShowMessage.view("不存在执行类："+rbm.getRuleBeanClass());
      				return true;
      			} catch (Exception e) {
      				e.printStackTrace();
      				ShowMessage.view("执行类内部报错："+rbm.getRuleBeanClass());
      				return false;
      			}
      	   }
        
        CBSystemConstants.getCurOperateDevs().remove(CBSystemConstants.getCurOperateDevs().size());
	    return true;
	}
}
