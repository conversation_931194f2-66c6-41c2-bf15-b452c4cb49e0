package czprule.rule.runmode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.swing.JTabbedPane;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

/**
 * 版权声明: 泰豪软件股份有陝公坸版权所有 功能说明: 根杮传入的设备状思集坈设置所有关蝔设备状思 作 者: 郑柯 开坑日期: 2013年11月18日
 * 下坈7:20:56
 */
public class ExecuteDeviceStatus {

	private static String transformerTempStatus = "";
	private static PowerDevice srcDev = null;
	
	public static void execute(HashMap<PowerDevice, String> deviceStatusMap, boolean isSaveDtd) {

		List<PowerDevice> swList = new ArrayList<PowerDevice>(); // 开关缓存
		List<PowerDevice> devList = new ArrayList<PowerDevice>(); // 设备缓存
		List<PowerDevice> list = new ArrayList<PowerDevice>();
		for (Iterator it = deviceStatusMap.entrySet().iterator(); it.hasNext();) {
			Entry<PowerDevice, String> e = (Entry<PowerDevice, String>) it
					.next();
			PowerDevice pd = e.getKey();

			if(pd.isPW()||CBSystemConstants.roleCode.equals("1")){
				executePW(pd,isSaveDtd);
				continue;
			}
				
			if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
				transformerTempStatus = CBSystemConstants.RunModelThreeTwo;

			if (pd.getDeviceType().equals(SystemConstants.SwitchSeparate)
					|| pd.getDeviceType().equals(
							SystemConstants.SwitchFlowGroundLine)) {
				List<PowerDevice> listTemp = new ArrayList<PowerDevice>();
				if(pd.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
					listTemp = RuleExeUtil.getKnifeRelateSwitch(pd);
				}
				else
					listTemp = RuleExeUtil.getDeviceDirectList(pd,SystemConstants.Switch);
				if (listTemp.size() > 0) {
					for (PowerDevice sw : listTemp) { // 将刀闸〝接地刀闸直接连接的开关加入开关缓存
						if (!swList.contains(sw))
							swList.add(sw);
					}
				} 
				else {
					list = RuleExeUtil.getDeviceDirectList(pd, "");
					for (PowerDevice dev : list) { // 将刀闸〝接地刀闸直接连接的设备加入开关缓存
						if (!devList.contains(dev) && !dev.getDeviceType().equals(SystemConstants.SwitchSeparate)
								&& !dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine))
							devList.add(dev);
					}
				}
			} else if (pd.getDeviceType().equals(SystemConstants.Switch)) {
				if (!swList.contains(pd)) // 加入开关缓存
					swList.add(pd);
			}
		}
		for (PowerDevice sw : swList) { // 将开关关蝔的设备加入设备缓存
			list = RuleExeUtil.getDeviceList(sw, "", SystemConstants.PowerTransformer+","+SystemConstants.Switch, true, true, true);
			for (PowerDevice dev : list) {
				if (!devList.contains(dev)
						&& !dev.getDeviceType().equals(SystemConstants.Switch)
						&& !dev.getDeviceType().equals(
								SystemConstants.SwitchSeparate)
						&& !dev.getDeviceType().equals(
								SystemConstants.SwitchFlowGroundLine))
					devList.add(dev);
			}
			
			if(RuleExeUtil.isSourceSide(sw)) {
				//线路开关或毝蝔开关代替主坘开关接线情况
				if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL) ||
						sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
					list = RuleExeUtil.getDeviceList(sw, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer+","+SystemConstants.Switch, "", "", false, true, false, true);
					for (PowerDevice dev : list) {
						if (!devList.contains(dev))
							devList.add(dev);
					}
				}
			}
			else {
				//主坘负蝷侧开关或毝蝔开关代替线路开关接线情况
				if(sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC) ||
						sw.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
					list = RuleExeUtil.getDeviceList(sw, SystemConstants.InOutLine, SystemConstants.PowerTransformer+","+SystemConstants.Switch, "", "", false, true, false, true);
					for (PowerDevice dev : list) {
						if (!devList.contains(dev))
							devList.add(dev);
					}
				}
			}
			
			PowerDevice sideMother = null;
			for (PowerDevice dev : devList) {
				if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)) {
					sideMother = dev;
					break;
				}
			}
			if(sideMother != null) {
				list = RuleExeUtil.getDeviceList(sideMother, "", SystemConstants.PowerTransformer+","+SystemConstants.Switch, true, true, true);
				for (PowerDevice dev : list) {
					if (!devList.contains(dev)
							&& !dev.getDeviceType().equals(SystemConstants.Switch)
							&& !dev.getDeviceType().equals(
									SystemConstants.SwitchSeparate)
							&& !dev.getDeviceType().equals(
									SystemConstants.SwitchFlowGroundLine))
						devList.add(dev);
				}
			}
		}

		// 判断开关状思
		for (PowerDevice sw : swList) {
			String status = "";
			status = judgeSwitch(sw);
			if (!sw.getDeviceStatus().equals(status))
				RuleExeUtil.deviceStatusSet(sw, sw.getDeviceStatus(), status, isSaveDtd);
		}

		// 判断设备状思
		for (PowerDevice dev : devList) {
			String status = "";
			if (dev.getDeviceType().equals(SystemConstants.ElecShock))
				status = judgeElecShock(dev);
			else if (dev.getDeviceType().equals(SystemConstants.MotherLine)||dev.getDeviceType().equals(SystemConstants.InOutLine)||dev.getDeviceType().equals(SystemConstants.PowerTransformer))
				continue;
			else if (dev.getDeviceType().equals(SystemConstants.VolsbTransformer))
				status = judgePT(dev);
			else
				status = judgeOther(dev);
			if (!status.equals("") && !dev.getDeviceStatus().equals(status)&&(dev.getPowerDeviceName().indexOf("5100")==-1))
				RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(), status, isSaveDtd);
		}
		// 判断毝线状思
		for (PowerDevice dev : devList) {
			String status = "";
			if (dev.getDeviceType().equals(SystemConstants.MotherLine)) {
				if(CBSystemConstants.executeDeviceStatusNew){
					status = judgeMotherLineNEW(dev);
				}else{
					status = judgeMotherLine(dev);
				}
				if (!dev.getDeviceStatus().equals(status))
					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(),
							status, isSaveDtd);
			}
		}
		
		// 判断线路状思
		for (PowerDevice dev : devList) {
			
			if (dev.getDeviceType().equals(SystemConstants.InOutLine)) {
				String status = "";
				List<PowerDevice> lineList = null;
				if(srcDev == null && CBSystemConstants.roleCode.equals("0") && CBSystemConstants.isCurrentSys) {
	    			srcDev = dev;
	    			lineList = RuleExeUtil.getLineAllSideList(dev); 
	    			srcDev = null;
				}
				else {
					lineList = new ArrayList<PowerDevice>();
					lineList.add(dev);
				}

				//判断线路整体状思，线路坄侧都设置为整体状思
				if(status.equals(""))
					status = judgeLineGZ(dev,lineList);
				for(PowerDevice line : lineList) {
					if (!line.getDeviceStatus().equals(status))
						RuleExeUtil.deviceStatusSet(line, line.getDeviceStatus(),
								status, isSaveDtd);
				}
				
				
				
			}
		}

		// 判断主坘状思
		for (PowerDevice dev : devList) {
			String status = "";
			if (dev.getDeviceType().equals(SystemConstants.PowerTransformer)) {
				if(CBSystemConstants.executeDeviceStatusNew){
					status = judgeTransformerNEW(dev);
				}else{
					status = judgeTransformer(dev);
				}
				if (!dev.getDeviceStatus().equals(status))
					RuleExeUtil.deviceStatusSet(dev, dev.getDeviceStatus(),
							status, isSaveDtd);
			}
		}
	}
	public static void executePW(PowerDevice pd,boolean isSaveDtd){

		List<PowerDevice> devList = new ArrayList<PowerDevice>();
		if (pd.getDeviceType().equals(SystemConstants.SwitchSeparate)
				|| pd.getDeviceType().equals(
						SystemConstants.SwitchFlowGroundLine)) {
			List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);
			for (PowerDevice dev : list) { // 将刀闸〝接地刀闸直接连接的设备加入开关缓存
				if(!devList.contains(dev)){
					devList.add(dev);
				}
					
			}
			
		} else if (pd.getDeviceType().equals(SystemConstants.Switch)) {
			if (!devList.contains(pd)) // 加入开关缓存
				devList.add(pd);
		}


		// 判断开关状思
		for (PowerDevice sw : devList) {
            String status  = judgeSwitchPw(sw);
			if (!sw.getDeviceStatus().equals(status))
				RuleExeUtil.deviceStatusSet(sw, sw.getDeviceStatus(), status, isSaveDtd);
		}

	}

	//判断开关状态
    public static String judgeSwitchPw(PowerDevice pd) {
        boolean isGroundKnifeOn = false; //接地刀闸是否合上
        boolean isKnifeOn1 = false;
        boolean isKnifeOn2 = false;

        List<PowerDevice> devList = RuleExeUtil.getDeviceDirectList(pd,null); //查询所有相邻设备
        List<PowerDevice> devList2 = new ArrayList<PowerDevice>(); //存储刀闸
        Iterator<PowerDevice> iterator = devList.iterator();
        while (iterator.hasNext()){
            PowerDevice dev = iterator.next();
            if (dev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)) {
                if(dev.getDeviceStatus().equals("0")){
                    isGroundKnifeOn = true;
                    break;
                }
            }else if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate) ){
                devList2.add(dev);
            }
        }
        // 如果存在合上接地刀闸，开关状态为检修
        if (isGroundKnifeOn) {
            return "3";
        }

        if(devList2.size()==0){
            isKnifeOn1 = true;
            isKnifeOn2 = true;
        }else if(devList2.size()==1){
            isKnifeOn1 = true;
            if(devList2.get(0).getDeviceStatus().equals("0")){
                isKnifeOn2 = true;
            }
        }else if(devList2.size()==2){
            if(devList2.get(0).getDeviceStatus().equals("0")){
                isKnifeOn1 = true;
            }
            if(devList2.get(1).getDeviceStatus().equals("0")){
                isKnifeOn2 = true;
            }
        }

        if (!isKnifeOn1 || !isKnifeOn2) { // 如果两侧侧的刀闸均为断开，开关状态为冷备用
            return "2";
        } else if (isKnifeOn1 && isKnifeOn2) { // 如果两侧刀闸都连通，开关状态为热备用
            if(pd.getDeviceStatus().equals("0"))
                return "0";
            else
                return "1";
        }

        return pd.getDeviceStatus();
    }


	public static String judgeSwitch(PowerDevice pd) {
		
		boolean isGroundKnifeOn = false;
		boolean isKnifeOn1 = false;
		boolean isKnifeOn2 = false;
		List<PowerDevice> devList = null;
		devList = RuleExeUtil.getDeviceDirectList(pd,SystemConstants.SwitchFlowGroundLine);
		for (PowerDevice dev : devList) {
			if(RuleExeUtil.getDeviceDirectList(dev, SystemConstants.InOutLine).size()>0
					&&RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate).size()==1){//小车开关直连的地刀如果直连线路，则不识别为开关地刀
				continue;
			}
			if (dev.getDeviceStatus().equals("0")) {
				isGroundKnifeOn = true;
				break;
			}
		}
		if (isGroundKnifeOn) { // 如果存在坈上的接地刀闸，开关状思为检修
			return "3";
		}
		
		//devList = RuleExeUtil.getSwitchRelateKnifeByPort(pd, "1");
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC) || pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
			devList = RuleExeUtil.getSwitchRelateKnifeByPort(pd, "1");
			
			for(int i=0;i<devList.size();i++){
				if(devList.get(i).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeBLQ)){
					devList.remove(i);
					i--;
				}
			}
		}
			
		else
			devList = RuleExeUtil.getDeviceDirectByPortList(pd, SystemConstants.SwitchSeparate, "1");
	
		if(devList.size()>1){//如果有尝车，则坪判断尝车
			for(int z=0;z<devList.size();z++){
				PowerDevice xcdz=devList.get(z);
				if(xcdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					devList.clear();
					devList.add(xcdz);
					break;
				}
			}
		}
		
		
		//西北66kV其他刀闸
		for (Iterator it = devList.iterator(); it.hasNext();) {
			PowerDevice dev = (PowerDevice)it.next();
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT) && dev.getPowerVoltGrade()==66)
				it.remove();
		}
				
		if(devList.size() == 0)
			isKnifeOn1 = true;
		else if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
			isKnifeOn1 = false;
			for (PowerDevice dev : devList) {
				if (dev.getDeviceStatus().equals("0")) {
					isKnifeOn1 = true;
					break;
				}
			}
		}
		else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
			isKnifeOn1 = true;
			for (PowerDevice dev : devList) {
				if (!dev.getDeviceStatus().equals("0")) {
					isKnifeOn1 = false;
					break;
				}
			}
		}
		else {
			isKnifeOn1 = false;
			for (PowerDevice dev : devList) {
				if (dev.getDeviceStatus().equals("0")) {
					isKnifeOn1 = true;
					break;
				}
			}
		}
		
		//devList = RuleExeUtil.getSwitchRelateKnifeByPort(pd, "2");
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC) || pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))
			devList = RuleExeUtil.getSwitchRelateKnifeByPort(pd, "2");
		else
			devList = RuleExeUtil.getDeviceDirectByPortList(pd, SystemConstants.SwitchSeparate, "2");
		
		
		if(devList.size()>1){//如果有尝车，则坪判断尝车
			for(int z=0;z<devList.size();z++){
				PowerDevice xcdz=devList.get(z);
				if(xcdz.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
					devList.clear();
					devList.add(xcdz);
				}
			}
		}
		
		//西北66kV其他刀闸
		for (Iterator it = devList.iterator(); it.hasNext();) {
			PowerDevice dev = (PowerDevice)it.next();
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeQT) && dev.getPowerVoltGrade()==66)
				it.remove();
		}
		
		if(devList.size() == 0)
			isKnifeOn2 = true;
		else if(pd.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
			isKnifeOn2 = false;
			for (PowerDevice dev : devList) {
				if (dev.getDeviceStatus().equals("0")) {
					isKnifeOn2 = true;
					break;
				}
			}
		}
		else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)) {
			isKnifeOn2 = true;
			for (PowerDevice dev : devList) {
				if (!dev.getDeviceStatus().equals("0")) {
					isKnifeOn2 = false;
					break;
				}
			}
		}
		else {
			isKnifeOn2 = false;
			for (PowerDevice dev : devList) {
				if (dev.getDeviceStatus().equals("0")) {
					isKnifeOn2 = true;
					break;
				}
			}
		}
		
		if (!isKnifeOn1 || !isKnifeOn2) { // 如果存在一侧的刀闸均为断开，开关状思为冷备用
			return "2";
		} else if (isKnifeOn1 && isKnifeOn2) { // 如果两侧刀闸都连通，开关状思为热备用
			if(pd.getDeviceStatus().equals("0"))
				return "0";
			else
				return "1";
		}
		return pd.getDeviceStatus();
	}

    public static String judgeLine(PowerDevice pd) {
		
		
		List<PowerDevice> devList = null;

		// 如果存在坈上的接地刀闸，线路状思为检修
		devList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchFlowGroundLine);
		for (PowerDevice dev : devList) {
			if (dev.getDeviceStatus().equals("0")) {
				return "3";
			}
		}
		
		//查找线路连通的毝线
		devList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
		for (PowerDevice dev : devList) {
			if (dev.getDeviceStatus().equals("0")) {
				return "0"; //如果与毝线连通，则线路为违行
			}
		}
		
		//查找线路丝通开关连接的毝线
		List<PowerDevice> allBusDevList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.Switch, false, true, true);
		for (PowerDevice dev : allBusDevList) {
			if (dev.getDeviceStatus().equals("3"))
				return "2";
			else
				return dev.getDeviceStatus();
		}
		
		
		String minStatus = "2";
		//查找线路丝通过毝线连通的开关
		devList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, false, true, true);
		
		if(devList.size() == 0) {
			//查找线路丝通过毝线连接的所有开关
			List<PowerDevice> allDevList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			if(allDevList.size() == 0)
				devList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, null, null, null, false, false, false, true);
			else
				return "2"; //如果开关未连通，则线路为冷备用
		}
		if(devList.size() > 0) {
			for (PowerDevice dev : devList) {
				if(Integer.valueOf(dev.getDeviceStatus()) < Integer.valueOf(minStatus))
					minStatus = dev.getDeviceStatus();
			}
			return minStatus;
		}
		
		devList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate);
		if(devList.size() > 0) {
			for (PowerDevice dev : devList) {
				if (dev.getDeviceStatus().equals("0")) {
					return "0";
				}
			}
			return "2";
		}
		
		return "0";
	}

	public static String judgeLineGZ(PowerDevice curline,List<PowerDevice> lineList) {

		List<PowerDevice> devList = null;

		//List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(pd);
		// 如果存在本侧坈上的接地刀闸，线路状思为检修
		
		if(CBSystemConstants.bdzTicket){
			devList = RuleExeUtil.getDeviceDirectList(curline, SystemConstants.SwitchFlowGroundLine);
			for (PowerDevice dev : devList) {
				if (dev.getDeviceStatus().equals("0")) {
					return "3";
				}
			}
		}else{
			for(PowerDevice ln : lineList) {
				if(ln.equals(curline)){
					devList = RuleExeUtil.getDeviceDirectList(ln, SystemConstants.SwitchFlowGroundLine);
					for (PowerDevice dev : devList) {
						if (dev.getDeviceStatus().equals("0")) {
							return "3";
						}
					}
				}else{
					devList = RuleExeUtil.getDeviceDirectList(ln, SystemConstants.SwitchFlowGroundLine);
					List<PowerDevice> swList = RuleExeUtil.getDeviceList(curline, SystemConstants.Switch,
							SystemConstants.PowerTransformer, true, true, true);
					String state = "3";
					for(PowerDevice sw:swList){
						if(!sw.getDeviceStatus().equals("")&&Integer.parseInt(state)>Integer.parseInt(sw.getDeviceStatus())){
							state = sw.getDeviceStatus();
						}
					}
					for (PowerDevice dev : devList) {
						if (dev.getDeviceStatus().equals("0")&&state.equals("2")) {
							return "3";
						}
					}
				}
			}
		}
		
		// 如果线路丝在检修，坖坄侧最高状思（最高是违行）为整体状思
		List<String> staList = new ArrayList<String>();
		String lineMinStatus = "";
		
		if(CBSystemConstants.bdzTicket){
			//查找线路连通的毝线
			String lnSta = "";
			devList = RuleExeUtil.getDeviceList(curline, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
			for (PowerDevice dev : devList) {
				if (dev.getDeviceStatus().equals("0")) {
					lnSta = "0"; //如果与毝线连通，则线路为违行
					break;
				}
			}
			if(!lnSta.equals("")) {
				staList.add(lnSta);
			}
			
			//查找线路丝通开关连接的毝线
			List<PowerDevice> allBusDevList = RuleExeUtil.getDeviceList(curline, SystemConstants.MotherLine, SystemConstants.Switch, false, true, true);
			for (PowerDevice dev : allBusDevList) {
				if (dev.getDeviceStatus().equals("3"))
					lnSta = "2";
				else
					lnSta = dev.getDeviceStatus();
			}
			if(!lnSta.equals("")) {
				staList.add(lnSta);
			}
			
			
			String minStatus = "2";
			//查找线路丝通过毝线连通的开关
			devList = RuleExeUtil.getDeviceList(curline, SystemConstants.Switch, SystemConstants.PowerTransformer, false, true, true);
			if(devList.size() == 0) {
				//查找线路丝通过毝线连接的所有开关
				List<PowerDevice> allDevList = RuleExeUtil.getDeviceList(curline, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
				if(allDevList.size() == 0)
					devList = RuleExeUtil.getDeviceList(curline, SystemConstants.Switch, null, null, null, false, false, false, true);
				else
					lnSta = "2"; //如果开关未连通，则线路为冷备用
			}
			if(!lnSta.equals("")) {
				staList.add(lnSta);
			}
			
			if(devList.size() > 0) {
				for (PowerDevice dev : devList) {
					if(Integer.valueOf(dev.getDeviceStatus()) < Integer.valueOf(minStatus))
						minStatus = dev.getDeviceStatus();
				}
				lnSta = minStatus;
			}
			if(!lnSta.equals("")) {
				staList.add(lnSta);
			}
		}else{
			for(PowerDevice ln : lineList) {
				//查找线路连通的毝线
				String lnSta = "";
				devList = RuleExeUtil.getDeviceList(ln, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
				for (PowerDevice dev : devList) {
					if (dev.getDeviceStatus().equals("0")) {
						lnSta = "0"; //如果与毝线连通，则线路为违行
						break;
					}
				}
				if(!lnSta.equals("")) {
					staList.add(lnSta);
					continue;
				}
				
				//查找线路丝通开关连接的毝线
				List<PowerDevice> allBusDevList = RuleExeUtil.getDeviceList(ln, SystemConstants.MotherLine, SystemConstants.Switch, false, true, true);
				for (PowerDevice dev : allBusDevList) {
					if (dev.getDeviceStatus().equals("3"))
						lnSta = "2";
					else
						lnSta = dev.getDeviceStatus();
				}
				if(!lnSta.equals("")) {
					staList.add(lnSta);
					continue;
				}
				
				
				String minStatus = "2";
				//查找线路丝通过毝线连通的开关
				devList = RuleExeUtil.getDeviceList(ln, SystemConstants.Switch, SystemConstants.PowerTransformer, false, true, true);
				if(devList.size() == 0) {
					//查找线路丝通过毝线连接的所有开关
					List<PowerDevice> allDevList = RuleExeUtil.getDeviceList(ln, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
					if(allDevList.size() == 0)
						devList = RuleExeUtil.getDeviceList(ln, SystemConstants.Switch, null, null, null, false, false, false, true);
					else
						lnSta = "2"; //如果开关未连通，则线路为冷备用
				}
				if(!lnSta.equals("")) {
					staList.add(lnSta);
					continue;
				}
				
				if(devList.size() > 0) {
					for (PowerDevice dev : devList) {
						if(Integer.valueOf(dev.getDeviceStatus()) < Integer.valueOf(minStatus))
							minStatus = dev.getDeviceStatus();
					}
					lnSta = minStatus;
				}
				if(!lnSta.equals("")) {
					staList.add(lnSta);
					continue;
				}
				
				devList = RuleExeUtil.getDeviceDirectList(ln, SystemConstants.SwitchSeparate);
				if(devList.size() > 0) {
					for (PowerDevice dev : devList) {
						if (dev.getDeviceStatus().equals("0")) {
							lnSta = "0";
							break;
						}
						else
							lnSta = "2";
					}
					
				}
				if(lnSta.equals("")) {
					continue;
				}
				staList.add(lnSta);
			}
		}
		
		for(String lnSta : staList) {
			if(lineMinStatus.equals(""))
				lineMinStatus = lnSta;
			else if(Integer.valueOf(lineMinStatus) > Integer.valueOf(lnSta))
				lineMinStatus = lnSta;
		}
		
		
		return lineMinStatus;
	}
	public static String judgeMotherLineNEW(PowerDevice pd) {

		  List<PowerDevice> devList = null;

		  // 如果存在坈上的接地刀闸，毝线状思为检修
		  devList = RuleExeUtil.getDeviceDirectList(pd,
		    SystemConstants.SwitchFlowGroundLine);
		  for (PowerDevice dev : devList) {
		   if (dev.getDeviceStatus().equals("0")) {
		    return "3";
		   }
		  }

		  // 如果毝线连接到电溝，毝线状思为违行
		  if (RuleExeUtil.isMLOnSource(pd))
		   return "0";
		  
		  //存在线路刀闸（代线路开关）在坈佝，毝线在违行状思
		  List<PowerDevice> dzList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXLS, null, true, false, true, true);
		  if(dzList.size() > 0 && dzList.get(0).getDeviceStatus().equals("0"))
		   return "0";
		  
			//存在PT刀闸分佝，毝线在冷备用状思
			List<PowerDevice> ptdzList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePT, null, true, false, true, true);
			if(ptdzList.size() > 0 && ptdzList.get(0).getDeviceStatus().equals("1"))
				return "2";

		  // 根杮毝线连接的开关的最高状思确定毝线状思
		  String minStatus = "2";
		  String oldStatus = pd.getDeviceStatus();
		  if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
			   devList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchPL,null,false, false, true, true);
			  else if(RuleExeUtil.isSourceSide(pd))
			   devList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchXL,null,false, false, true, true);
			  else{
			   if(pd.getPowerVoltGrade()>100){
			    devList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchXL+","+CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchFHC,null,false, false, true, true);
			    
			   }else{
			    devList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchFHC,null,false, false, true, true);
			    
			   }
			  }
			   
			  for (PowerDevice dev : devList) {
			   if(Integer.valueOf(dev.getDeviceStatus()) < Integer.valueOf(minStatus))
			    minStatus = dev.getDeviceStatus();
			  }
			  
			  
			  
			  if(oldStatus.equals("3")&&minStatus.equals("2")){
			   minStatus = oldStatus;
			  }
			  if(oldStatus.equals("3")&&CBSystemConstants.getCurRBM().getEndState().equals("2")){
			   minStatus = oldStatus;
			  }
			  
			  //杜丝到开关的特殊情况默认违行
			  devList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC,"",false, true, true, true);
			  devList.addAll(RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeKnifeXLS,"",false, true, true, true));
			  if(devList.size()==0){
			   minStatus="0";
			  }
			  
			  return minStatus;
	}
	public static String judgeMotherLine(PowerDevice pd) {

		List<PowerDevice> devList = null;

		// 如果存在坈上的接地刀闸，毝线状思为检修
		devList = RuleExeUtil.getDeviceDirectList(pd,
				SystemConstants.SwitchFlowGroundLine);
		for (PowerDevice dev : devList) {
			if (dev.getDeviceStatus().equals("0")) {
				return "3";
			}
		}

		// 如果毝线连接到电溝，毝线状思为违行
		if (RuleExeUtil.isMLOnSource(pd))
			return "0";
		
		//存在线路刀闸（代线路开关）在坈佝，毝线在违行状思
		List<PowerDevice> dzList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifeXLS, null, true, false, true, true);
		if(dzList.size() > 0 && dzList.get(0).getDeviceStatus().equals("0"))
			return "0";
		
		//存在PT刀闸分佝，毝线在冷备用状思
		List<PowerDevice> ptdzList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeKnifePT, null, true, false, true, true);
		if(ptdzList.size() > 0 && ptdzList.get(0).getDeviceStatus().equals("1"))
			return "2";

		// 根杮毝线连接的开关的最高状思确定毝线状思
		String minStatus = "2";
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
			devList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchPL,null,false, false, true, true);
		else if(RuleExeUtil.isSourceSide(pd))
			devList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchXL,null,false, false, true, true);
		else
			devList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch,SystemConstants.PowerTransformer,CBSystemConstants.RunTypeSwitchML+","+CBSystemConstants.RunTypeSwitchFHC,null,false, false, true, true);
		
		for (PowerDevice dev : devList) {
			if(Integer.valueOf(dev.getDeviceStatus()) < Integer.valueOf(minStatus))
				minStatus = dev.getDeviceStatus();
		}
		List<PowerDevice> kgList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch,SystemConstants.PowerTransformer,null,null,false, true, true, true);
		
		if(dzList.size() == 0 && devList.size() == 0 && kgList.size()==0)
			return "0";
		return minStatus;
	}

	public static String judgeTransformer(PowerDevice pd) {
		
		List<PowerDevice> devList = null;

		// 如果存在坈上的接地刀闸，主坘状思为检修
		devList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchFlowGroundLine);
		for (PowerDevice dev : devList) {
			if (!RuleUtil.isZXDDD(dev) && 
					dev.getDeviceStatus().equals("0") &&
					dev.getPowerVoltGrade() == pd.getPowerVoltGrade()) {
				return "3";
			}
		}
		
		List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
		if(kfList.size() > 0 && kfList.get(0).getDeviceStatus().equals("1")) {
			return "2";
		}
			
		
		// 根杮主坘连接的开关的最高状思确定主坘状思
		devList.clear();
		List<Double> volList = RuleExeUtil.getTransformerVol(pd);
		if(volList.size() == 1)
			devList.addAll(RuleExeUtil.getTransformerSwitchByVol(pd, volList.get(0)));
		else {
			if(volList.size() == 2) {
				for(int i = volList.size()-1; i < volList.size(); i++) {
					devList.addAll(RuleExeUtil.getTransformerSwitchByVol(pd, volList.get(i))); //电压等级侧的都放进List
				}
			}
			else {
				for(int i = 1; i < volList.size(); i++) {
					devList.addAll(RuleExeUtil.getTransformerSwitchByVol(pd, volList.get(i))); //电压等级侧的都放进List
				}
			}
		}
		
		
		String statusTemp = "2";//最低状思是冷备用
		if(transformerTempStatus.equals(CBSystemConstants.RunModelThreeTwo)){//如果是3/2接线方弝，坪覝主坘附属的主坘开关〝线路开关〝主坘刀闸有违行的，则主坘为违行状思
			for (PowerDevice dev : devList) {
				if (dev.getDeviceType().equals(SystemConstants.Switch)) {
					if(Integer.parseInt(dev.getDeviceStatus()) < Integer.parseInt(statusTemp))
						statusTemp = dev.getDeviceStatus();
				}
			}
			return statusTemp;
		}
		
		for (PowerDevice dev : devList) {
			if (dev.getDeviceType().equals(SystemConstants.Switch)) {
				if(Integer.parseInt(dev.getDeviceStatus()) < Integer.parseInt(statusTemp))
					statusTemp = dev.getDeviceStatus();
			}
		}
		return statusTemp;
	}
	public static String judgeTransformerNEW(PowerDevice pd) {
		
		List<PowerDevice> devList = null;

		// 如果存在坈上的接地刀闸，主坘状思为检修
		devList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchFlowGroundLine);
		for (PowerDevice dev : devList) {
			if (!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD) && 
					dev.getDeviceStatus().equals("0") &&
					dev.getPowerVoltGrade() == pd.getPowerVoltGrade()) {
				return "3";
			}
		}
		
		List<PowerDevice> kfList = RuleExeUtil.getTransformerKnifeSource(pd);
		if(kfList.size() > 0 && kfList.get(0).getDeviceStatus().equals("1")) {
			return pd.getDeviceStatus();
		}
			
		
		// 根杮主坘连接的开关的最高状思确定主坘状思
		devList.clear();
		List<Double> volList = RuleExeUtil.getTransformerVol(pd);
		if(volList.size() == 1)
			devList.addAll(RuleExeUtil.getTransformerSwitchByVol(pd, volList.get(0)));
		else {
			if(volList.size() == 2) {
				for(int i = volList.size()-1; i < volList.size(); i++) {
					devList.addAll(RuleExeUtil.getTransformerSwitchByVol(pd, volList.get(i))); //电压等级侧的都放进List
				}
			}
			else {
				for(int i = 1; i < volList.size(); i++) {
					devList.addAll(RuleExeUtil.getTransformerSwitchByVol(pd, volList.get(i))); //电压等级侧的都放进List
				}
			}
		}
		
		String oldStatus = pd.getDeviceStatus();
		
		String statusTemp = "2";//最低状思是冷备用
		if(transformerTempStatus.equals(CBSystemConstants.RunModelThreeTwo)){//如果是3/2接线方弝，坪覝主坘附属的主坘开关〝线路开关〝主坘刀闸有违行的，则主坘为违行状思
			for (PowerDevice dev : devList) {
				if (dev.getDeviceType().equals(SystemConstants.Switch)) {
					if(Integer.parseInt(dev.getDeviceStatus()) < Integer.parseInt(statusTemp))
						statusTemp = dev.getDeviceStatus();
				}
			}
			return statusTemp;
		}
		
		for (PowerDevice dev : devList) {
			if (dev.getDeviceType().equals(SystemConstants.Switch)) {
				if(Integer.parseInt(dev.getDeviceStatus()) < Integer.parseInt(statusTemp))
					statusTemp = dev.getDeviceStatus();
			}
		}
		
		if(oldStatus.equals("3")&&statusTemp.equals("2")){
			statusTemp = oldStatus;
		}
		return statusTemp;
	}
	public static String judgeElecShock(PowerDevice pd) {
		if (pd == null)
			return "-1";

		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		List results = null;
		PowerDevice temDev = null;
		String devStatus = "";

		// 一〝杜索直接连接的线路
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.InOutLine);// 目标设备线路
		inPara.put("isSearchDirectDevice", false);
		cs.execute(inPara, outPara);
		inPara.clear();
		results = (ArrayList) outPara.get("linkedDeviceList");
		if (results != null && results.size() > 0) // 电抗器连接的线路，坪有一个
		{
			temDev = (PowerDevice) results.get(0);
			List<PowerDevice> dcList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate);
			if(dcList.size() == 1) {
				if(dcList.get(0).getDeviceStatus().equals("0"))
					return "0";
				else if(dcList.get(0).getDeviceStatus().equals("1"))
					return "2";
			}
		}
		
//		results = RuleExeUtil.getDeviceList(pd, SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, false, true, true);
//		if (results != null && results.size() > 0) // 电抗器连接的主坘，坪有一个
//		{
//			temDev = (PowerDevice) results.get(0);
//			devStatus = temDev.getDeviceStatus();
//			if(devStatus.equals("0"))
//				return devStatus;
//		}
		
		// 二〝杜索连接的开关
		String switchStatus = "";
		results = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.ElecShock+","+SystemConstants.PowerTransformer, true, true, true);
		if (results != null && results.size() > 0) // 电抗器连接的开关，坪有一个
		{
			switchStatus = "3";
			for (int i = 0; i < results.size(); i++) {
				temDev = (PowerDevice) results.get(i);
				if(Integer.valueOf(temDev.getDeviceStatus()) < Integer.valueOf(switchStatus))
					switchStatus = temDev.getDeviceStatus();
			}
			if(switchStatus.equals("0"))
				return switchStatus;
		}

		// 三〝杜索直接连接的刀闸,接地刀闸，根杮这些设备的状思判定电抗状思
		String knifeStatus = "";
		String groudStatus = "";
		String lineStatus = "";
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.SwitchSeparate + ","
				+ SystemConstants.SwitchFlowGroundLine);// 目标设备开关
		inPara.put("isSearchDirectDevice", true);
		cs.execute(inPara, outPara);
		inPara.clear();
		results = (ArrayList) outPara.get("linkedDeviceList");
		if (results != null && results.size() > 0) // 电抗器连接的刀闸，坪有一个
		{
			for (int i = 0; i < results.size(); i++) {
				temDev = (PowerDevice) results.get(i);
				devStatus = temDev.getDeviceStatus();
				if (SystemConstants.SwitchSeparate.equals(temDev
						.getDeviceType())) {
					knifeStatus = devStatus;
				} else {
					groudStatus = devStatus;
				}
			}
		}

		// 检修
		if ("0".equals(groudStatus)) {
			return "3";
		}
		// 冷备用
		if ("1".equals(knifeStatus)) {
			return "2";
		}
		// 热备用〝违行
		if (!"".equals(switchStatus)) {
			return switchStatus;
		}

		return "0";
	}
	
	
	/**
	 * PT状思判断
	 * @param pd
	 * @return
	 */
	public static String judgePT(PowerDevice pd) {
		if (pd == null)
			return "-1";

		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		List<PowerDevice> devList = null;
		PowerDevice temDev = null;
		String devStatus = "";

		// 杜索直接连接的接地刀闸
		devList = RuleExeUtil.getDeviceDirectList(pd,
				SystemConstants.SwitchFlowGroundLine);
		if (devList.size() > 0) {
			for (PowerDevice dev : devList) {
				if (dev.getDeviceStatus().equals("0")) {
					return "3";
				}
			}
		}
		
		// 杜索直接连接的刀闸
		devList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate);
		if(devList.size() > 0 && devList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePT)) {
			if(devList.get(0).getDeviceStatus().equals("0"))
				return "0";
			else
				return "2";
		}

		// 杜索直接连接的开关
		devList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch,
				SystemConstants.PowerTransformer, true, true, true);
		if (devList.size() > 0) // 设备连接的开关
			return devList.get(0).getDeviceStatus();
		else {
			devList = RuleExeUtil.getDeviceDirectList(pd,
					SystemConstants.SwitchSeparate);
			for (PowerDevice dev : devList) {
				if (dev.getDeviceStatus().equals("1")) { // 设备连接的刀闸断开
					return "2";
				}
			}
		}
		return "0";
	}

	public static String judgeOther(PowerDevice pd) {
		if (pd == null)
			return "-1";
		List<PowerDevice> devList = null;
		
		// 杜索直接连接线路或主坘
		devList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.InOutLine+","+SystemConstants.PowerTransformer);
		if (devList.size() > 0) {
			for (PowerDevice dev : devList) {
				return dev.getDeviceStatus();
			}
		}

		// 杜索直接连接的接地刀闸
		devList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchFlowGroundLine);
		if (devList.size() > 0) {
			for (PowerDevice dev : devList) {
				if (dev.getDeviceStatus().equals("0")&&!dev.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)) {
					return "3";
				}
			}
		}
		
		// 杜索直接连接的刀闸
		devList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate);
		if (devList.size() > 0) {
			for (PowerDevice dev : devList) {
				if (dev.getDeviceStatus().equals("1")) {
					return "2";
				}
			}
		}

		// 杜索直接连接的开关
		devList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch,
				SystemConstants.PowerTransformer, true, true, true);
		if (devList.size() > 0) // 设备连接的开关
			return devList.get(0).getDeviceStatus();
		else {
			devList = RuleExeUtil.getDeviceDirectList(pd,
					SystemConstants.SwitchSeparate);
			for (PowerDevice dev : devList) {
				if (dev.getDeviceStatus().equals("1")) { // 设备连接的刀闸断开
					return "2";
				}
			}
		}
		return pd.getDeviceStatus();
	}
}
