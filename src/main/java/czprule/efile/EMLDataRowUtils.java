package czprule.efile;

import java.util.HashMap;

public class EMLDataRowUtils {

	private static HashMap<Class, EMLDataRowConvert> converts = new HashMap<Class, EMLDataRowConvert>();
	
	public static void register(Class clazz, EMLDataRowConvert convert) {
		converts.put(clazz, convert);
	}
	
	public static EMLDataRowConvert getConverter(Class clazz) {
		return converts.get(clazz);
	}
	
	public static interface EMLDataRowConvert {
		
		public String convert(Class clazz, Object value);
		
	}
	
	public static void main(String[] args) {
		converts.put(null, new EMLDataRowConvert() {
			public String convert(Class clazz, Object value) {
				return "无";
			}
		});
		System.out.println(converts.get(null));
	}
}
