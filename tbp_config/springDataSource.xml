<?xml version="1.0" encoding="UTF-8"?>


<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.0.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd">
	
	<bean id="tbp.sys.DataSource1"
		class="org.apache.commons.dbcp.BasicDataSource"
		destroy-method="close">
		<property name="driverClassName">
			<value>dm.jdbc.driver.DmDriver</value>
		</property>
		<property name="url">
		 <value>jdbc:dm://192.168.65.57:5243/HNCZP?ignoreCase=true</value>
<!--		 <value>jdbc:dm://127.0.0.1:5236/?ignoreCase=true</value>-->
<!--		 <value>jdbc:dm://10.37.150.42:5236/?ignoreCase=true</value>-->
		</property>
		<property name="username">
			<value>SYSDBA</value>
<!--			<value>SYSDBA</value>-->
<!--			<value>PLATFORM</value>-->
		</property>
		<property name="password">
			<value>SYSDBA</value>
<!--			<value>Hyy114514</value>-->
<!--			<value>LEEZH_SDOMS123</value>-->
		</property>
		<property name="maxActive">
			<value>25</value>
		</property>
		<property name="maxIdle">
			<value>25</value>
		</property>		
		<property name="validationQuery" value="SELECT 1 FROM DUAL" />
	</bean>
	 
	<!-- 
	<bean id="tbp.sys.DataSource1"
		class="org.apache.commons.dbcp.BasicDataSource"
		destroy-method="close">
		<property name="driverClassName">
			<value>oracle.jdbc.driver.OracleDriver</value>
		</property>
		<property name="url">
			<value>**************************************</value>
		</property>
		<property name="username">
			<value>opcard</value>
		</property>
		<property name="password">
			<value>opcard</value>
		</property>
	</bean>
	 -->
	<bean id="tbp.sys.DataSourceOMS"
		class="org.apache.commons.dbcp.BasicDataSource"
		destroy-method="close">
		<property name="driverClassName">
			<value>dm6.jdbc.driver.DmDriver</value>
		</property>
		<property name="url">
			<value>***********************************</value>
		</property>
		<property name="username">
			<value>SYSDBA</value>
		</property>
		<property name="password">
			<value>SYSDBA</value>
		</property>
	</bean> 
	<bean id="tbp.sys.DataSourceEMS"
		class="org.apache.commons.dbcp.BasicDataSource"
		destroy-method="close">
		<property name="driverClassName">
			<value>dm6.jdbc.driver.DmDriver</value>
		</property>
		<property name="url">
			<value>******************************</value>
		</property>
		<property name="username">
			<value>SYSDBA</value>
		</property>
		<property name="password">
			<value>SYSDBA</value>
		</property>
	</bean> 
	
	<bean id="tbp.sys.DataSourceHISDB"
		class="org.apache.commons.dbcp.BasicDataSource"
		destroy-method="close">
		<property name="driverClassName">
			<value>dm6.jdbc.driver.DmDriver</value>
		</property>
		<property name="url">
			<value>********************************</value>
		</property>
		<property name="username">
			<value>SYSDBA</value>
		</property>
		<property name="password">
			<value>SYSDBA</value>
		</property>
	</bean> 

	<!-- 配置多数据源路由器 -->
	<bean id="tbp.sys.DataSource"
		class="tbp.common.spring.core.moredata.DBRoutingDataSource">
		<property name="targetDataSources">
			<map key-type="java.lang.String">
				<!-- 建了多少个数据源就在加入相关的key及value,
					value为bean_id -->
				<entry key="TBP_SYS_DATASOURCE1"
					value-ref="tbp.sys.DataSource1" />
					<!--
				<entry key="TBP_SYS_DATASOURCE2"
					value-ref="tbp.sys.DataSource2" />
					 -->
			</map>
		</property>
		<property name="defaultTargetDataSource"
			ref="tbp.sys.DataSource1" />
	</bean>
		<bean id="tbp.sys.SessionFactory"
		class="org.springframework.orm.hibernate3.LocalSessionFactoryBean">
		<property name="dataSource">
			<ref bean="tbp.sys.DataSource" />
		</property>
		
		<property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">
					<!--  org.hibernate.dialect.Oracle9Dialect-->
					org.hibernate.dialect.DmDialect
				</prop>
				<prop key="hibernate.show_sql">true</prop>
				<prop key="hibernate.jdbc.batch_size">60</prop>
				<!--  prop key="hibernate.hbm2ddl.auto">update</prop--> 
			</props>
		</property>
		
		<property name="useTransactionAwareDataSource" value="true"></property>

	</bean>

	
</beans>